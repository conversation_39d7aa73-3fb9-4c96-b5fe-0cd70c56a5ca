@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  max-width: 374px;
  margin-right: 28px;
  @media (max-width: 768px) {
    margin-right: 0px;
    margin-bottom: 24px;
  }
  &:last-child {
    margin-right: 0px;
    margin-bottom: 0px;
  }
}

.imageBox {
  padding-bottom: 116%;
  position: relative;
  border-radius: 24px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: box-shadow 0.3s ease-out;
  margin-bottom: 16px;
  .stickyImageContainer {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: $skeleton;
    overflow: hidden;
    opacity: 0.5;
  }
}

.infoContainer {
  display: flex;
  width: 100%;
  align-items: flex-start;
  justify-content: space-between;
}

.name {
  width: 100%;
  max-width: 100px;
  background: $skeleton;
  height: 30px;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  opacity: 0.5;
  margin-bottom: 4px;
}

.info {
  width: 100%;
  max-width: 150px;
  background: $skeleton;
  height: 24px;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  opacity: 0.5;
}

.button {
  width: 100%;
  max-width: 120px;
  background: $skeleton;
  height: 50px;
  border-radius: 100px;
  overflow: hidden;
  position: relative;
  opacity: 0.5;
}
