import { <PERSON> } from "react-router-dom";
import Modal from "../Modal";
import { useMediaQuery } from "@mui/material";
import { Cross } from "../svgs";
import styles from "../../styles/how-it-works.module.scss";
import { t } from "i18next";
import i18n from "../../i18n";

const HowItWorksModal = ({
  type,
  show,
  setShow,
}: {
  type: "ios" | "android";
  show: boolean;
  setShow: Function;
}) => {
  const under768 = useMediaQuery("(max-width: 768px)");
  return (
    <Modal
      show={show}
      style={{
        height: under768 ? '100%' : 600,
        maxWidth: 1100,
        background: "#0F133A",
      }}
    >
      <div
        className={
          styles.howItWorksModal +
          " flex justify-content-between " +
          (type === "ios" ? "items-center" : "items-flexEnd")
        }
      >
        {type === "ios" && (
          <>
            <div>
              <h5>IOS</h5>
              <p>
                <b>01</b> {t("howItWorks.ios.stepOne")}
              </p>
              <p>
                <b>02</b> {t("howItWorks.ios.stepTwo")}
              </p>
              <p>{t("howItWorks.ios.iphoneSupport")}</p>
              <p>{t("howItWorks.ios.notSupport")}</p>
              <p>
                {t("howItWorks.ios.notSure")} {t("howItWorks.ios.check")}{" "}
                <Link
                  to="https://support.apple.com/en-us/102478"
                  target="_blank"
                >
                  {t("howItWorks.ios.appleSupport")}
                </Link>
              </p>
            </div>
            <div>
              <img
                src={`/images-int/how-it-works/how_it_works_ios${
                  i18n.language === "ar" ? "_ar.png" : ".png"
                }`}
                alt="Orbit Mobile - Support IOS devices - Apple - Iphone - Ipad"
              />
            </div>
          </>
        )}
        {type === "android" && (
          <>
            <div>
              <h5>{t("howItWorks.samsung.title")}</h5>
              <div className={styles.infoText}>
                <p>
                  <b>01 </b> {t("howItWorks.samsung.stepOne")}
                </p>
                <p>
                  <b>02</b> {t("howItWorks.samsung.stepTwo")}
                </p>
                <p>{t("howItWorks.samsung.findIMEI")}</p>
              </div>
              <div className={styles.imgWidth}>
                <img
                  src={`/images-int/how-it-works/how_it_works_samsung${
                    i18n.language === "ar" ? "_ar.png" : ".png"
                  }`}
                  alt="Orbit Mobile - Support IOS devices - Apple - Iphone - Ipad"
                />
              </div>
              <p>
                {t("howItWorks.ios.notSure")} {t("howItWorks.ios.check")}{" "}
                <Link
                  to="https://www.samsung.com/au/support/mobile-devices/esim-compatibility/?srsltid=AfmBOooSmv6VHROvspMRBpfj220buT6-iWuVb_MKXfe0TQnAglbFmgxj"
                  target="_blank"
                >
                  {t("howItWorks.samsung.samSupport")}
                </Link>
              </p>
            </div>
            <div className={styles.andriodBorder}>
              <h5>{t("howItWorks.otherDevices.title")}</h5>
              <div className={styles.infoText}>
                <p>
                  <b>01 </b> {t("howItWorks.otherDevices.stepOne")}
                </p>
                <p>
                  <b>02</b> {t("howItWorks.otherDevices.stepTwo")}
                </p>
              </div>
              <div className={styles.imgWidth}>
                <img
                  src={`/images-int/how-it-works/how_it_works_other${
                    i18n.language === "ar" ? "_ar.png" : ".png"
                  }`}
                  alt="Orbit Mobile - Support IOS devices - Apple - Iphone - Ipad"
                />
              </div>
              <p>
                {t("howItWorks.ios.notSure")} {t("howItWorks.ios.check")}{" "}
                {t("howItWorks.otherDevices.manuSupport")}
              </p>
            </div>
          </>
        )}
      </div>
      <span onClick={() => setShow(false)} className={styles.close}>
        <Cross />
      </span>
    </Modal>
  );
};

export default HowItWorksModal;
