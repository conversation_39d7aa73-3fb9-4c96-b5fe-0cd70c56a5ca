import Button from "../Button";
import Modal from "../Modal";
import Toggle from "../Toggle";
import styles from "./voicemail.module.scss";

const Voicemail = ({ show, setShow }: any) => {
  return (
    <Modal show={show} style={{ maxWidth: 700 }}>
      <div className={styles.main}>
        <h5>Voicemail settings</h5>
        <div className={styles.toggleContainer}>
          Voicemail
          <div className={styles.toggle}>
            <div style={{ marginRight: 17 }}>On</div>
            <Toggle />
          </div>
        </div>
        <div className={styles.buttons}>
          <Button
            style={{ marginRight: 16 }}
            color="secondary"
            onClick={() => {
              setShow(false);
            }}
          >
            Discard Changes
          </Button>
          <Button>Save</Button>
        </div>
      </div>
    </Modal>
  );
};

export default Voicemail;
