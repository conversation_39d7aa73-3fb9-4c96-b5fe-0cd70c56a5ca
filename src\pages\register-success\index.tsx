import { Link, useNavigate } from "react-router-dom";
import AppButtons from "../../components/AppButtons";
import Button from "../../components/Button";
import { ArrowBack } from "../../components/svgs";
import styles from "../../styles/register-success.module.scss";
import { useDispatch, useSelector } from "react-redux";
import ConfirmationModal from "../../components/ConfirmationModal";
import { useEffect, useState } from "react";
import { formatPrice } from "../../components/utils/formatPrice";

const RegisterSuccess = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { basket, currency } = useSelector((state: any) => state);

  const getTotal = () => {
    return formatPrice(basket?.prices[0].cost);
  };

  const cancelPurchase = () => {
    localStorage.removeItem("basket");
    dispatch({
      type: "set",
      basket: null,
      creditSelection: null,
    });
    navigate("/dashboard/home");
  };

  const [showConfirmCancel, setShowConfirmCancel] = useState(false);

  useEffect(() => {
    if (basket !== null) {
      dispatch({
        type: "notify",
        payload: {
          error: false,
          heading: "Registration Successful!",
          message: "You can now complete your purchase",
        },
      });
    }
  }, []);

  return (
    <div className={styles.container}>
      <ConfirmationModal
        show={showConfirmCancel}
        setShow={setShowConfirmCancel}
        proceed={cancelPurchase}
        heading="Are you sure?"
        text="You're about to cancel your purchase. You'll have to shop again if you wish to get these items back."
        continueButton="Yes, Cancel Purchase"
        cancelButton="No, Keep Shopping"
      />
      <Link to="/shop">
        <button className={styles.backButton}>
          <ArrowBack />
        </button>
      </Link>
      <div className={styles.main}>
        <div className={styles.text}>
          <h2>Welcome to the Gist family!</h2>
          {basket !== null ? (
            <p>
              Your account has been created, you can now finish your purchase.
            </p>
          ) : (
            <>
              <p>
                Your account has been created. You can now add new bundles and
                track them in your dashboard.
              </p>
              <div className={styles.buttons}>
                <Link style={{ textDecoration: "none" }} to="/dashboard/home">
                  <Button style={{ marginRight: 16 }}>Go to Dashboard</Button>
                </Link>
                <Link style={{ textDecoration: "none" }} to="/shop">
                  <Button>Go to Shop</Button>
                </Link>
              </div>
            </>
          )}
        </div>
        {basket !== null ? (
          <div className={styles.continuePurchase}>
            <div className={styles.basketData}>
              <b>
                Total: {basket.prices[0].currencySymbol}
                {getTotal()}
              </b>{" "}
              (1 item)
            </div>
            <div className={styles.buttons}>
              <Button
                color="tertiary"
                onClick={() => {
                  setShowConfirmCancel(true);
                }}
              >
                Cancel Purchase
              </Button>
              <Link to="/cart-summary" style={{ textDecoration: "none" }}>
                <Button>Go to Checkout</Button>
              </Link>
            </div>
          </div>
        ) : (
          <div className={styles.appBar}>
            <div className={styles.appText}>
              <h5>Get the app!</h5>
              <p>
                Build a plan, track your usage and enjoy the freedom as you
                travel
              </p>
            </div>
            <AppButtons style={{ marginTop: 0 }} />
          </div>
        )}
      </div>
    </div>
  );
};

export default RegisterSuccess;
