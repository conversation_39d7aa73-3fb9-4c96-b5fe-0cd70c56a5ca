@use "../../styles/theme.scss" as *;

.container {
  position: relative;
  width: 24px;
  height: 24px;
  padding: 3px;
}
.box {
  border: 1px solid $dark-dark-purple;
  border-radius: 1000px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.1s ease;
  width: 100%;
  height: 100%;
  position: relative;
  transition: all 0.1s ease;
  /*&:hover {
    .circle {
      opacity: 1;
      background: $light-primary;
      &.checkedCircle {
        opacity: 1;
        background: $secondary;
      }
    }
  }*/
}
.circle {
  width: 10px;
  height: 10px;
  background: $secondary;
  border-radius: 5px;
  opacity: 0;
  transition: all 0.1s ease;
  &.checkedCircle {
    opacity: 1;
  }
}
.checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
