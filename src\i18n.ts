import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from 'i18next-browser-languagedetector';
import translationEN from '../public/locales/en/translation.json';
import translationAr from '../public/locales/ar/translation.json';
import Backend from 'i18next-http-backend';

export const localeDetector = () => {
  if (typeof window !== 'undefined' && window.navigator) {
    const userLanguage = window.navigator.language.slice(0, 2);
    if (userLanguage !== 'en' && userLanguage !== 'ar') {
        return 'en'
    }
    return userLanguage
  }
  return 'en';
};

const resources = {
  en: {
    translation: translationEN
  },
  ar: {
    translation: translationAr
  }
};

i18n
  .use(Backend)
  .use(initReactI18next)
  .use(LanguageDetector)
  .init({
    resources,
    fallbackLng: "en",
    lng: localeDetector()
  });

  export default i18n;