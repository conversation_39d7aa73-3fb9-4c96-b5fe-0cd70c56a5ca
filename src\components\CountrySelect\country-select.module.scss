@use "../../styles/theme.scss" as *;

.label {
  font-size: 14px;
  line-height: 21px;
  color: $dark-dark-purple;
  margin-bottom: 4px;
}

.menuButton {
  height: 52px;
  width: 100%;
  background: #eff1f7;
  padding: 16px;
  border: none;
  border-radius: 40px;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  color: $dark-dark-purple;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: background 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &.disabled {
    cursor: auto;
    pointer-events: none;
    opacity: 0.5;
  }
  svg {
    width: 24px;
    height: 24px;
    transition: all 0.2s ease;
  }
  &.iconOpen {
    .expand svg {
      transform: rotate(180deg);
    }
  }
}

.expand {
  display: flex;
  align-items: center;
}

.box {
  height: 100%;
}

.menuItem {
  padding: 18.5px 10px;
  cursor: pointer;
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.modalMain {
  padding: 32px;
}

.itemsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  @media (max-width: 1025px) {
    grid-template-columns: 1fr 1fr 1fr;
  }
  @media (max-width: 550px) {
    grid-template-columns: 1fr 1fr;
  }
  @media (max-width: 470px) {
    grid-template-columns: 1fr;
  }
}

.searchContainer {
  margin-bottom: 16px;
}
