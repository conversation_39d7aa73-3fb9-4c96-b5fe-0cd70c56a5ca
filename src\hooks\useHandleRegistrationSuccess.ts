import { ApiGet } from "../pages/api/api";
import { useDispatch } from "react-redux";
import CryptoJS from 'crypto-js';

const useHandleRegistrationSuccess = () => {
  const dispatch = useDispatch()
  
  const googleAnalyticsRegistrationEvent = (data:any) => {
    window.gtag('event', 'register', {
      customerId: CryptoJS.SHA256(data.email).toString(CryptoJS.enc.Hex),
      affiliation: 'Registration',           
    });
  } 

  const handleRegistrationSuccess = (response: any) => {
    const token = response.data.auth;
    const now = new Date();
    const expiryTime = (now.getTime() + 82800000).toString();
    localStorage.setItem("token", token);
    localStorage.setItem("expiryTime", expiryTime);
    ApiGet("/users").then((response: any) => {
      googleAnalyticsRegistrationEvent(response.data)
      dispatch({
        type: "set",
        loggedIn: true,
      });
      dispatch({
        type: "set",
        userInfo: {
          firstName: response.data.firstName,
          lastName: response.data.lastName,
          email: response.data.email,
        },
      });
    });
  }

  return {
    handleRegistrationSuccess
  }
};

export default useHandleRegistrationSuccess;
