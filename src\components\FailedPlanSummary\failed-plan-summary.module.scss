@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  max-width: 554px;
  padding: 24px;
  border-radius: 8px;
  background: rgba(239, 241, 247, 0.2);
  backdrop-filter: blur(3px);
  display: grid;
  grid-template-rows: auto auto;
  grid-row-gap: 16px;
  color: #fff;
  font-size: 16px;
  line-height: 24px;
  .price {
    font-weight: 600;
  }
  .countryName {
    display: grid;
    align-items: center;
    grid-template-columns: 20px auto;
    font-weight: 600;
    grid-column-gap: 8px;
    .flag {
      width: 20px;
      height: 20px;
      border-radius: 1000px;
      background-color: #bdbdbd;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
    }
  }
  .row {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.binWrapper {
  svg {
    vertical-align: middle;
    margin-right: 0px;
  }
}
