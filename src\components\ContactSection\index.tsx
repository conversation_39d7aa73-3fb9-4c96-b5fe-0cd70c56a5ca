import Button from "../Button";
import { Envelope } from "../svgs";
import styles from "./contact-section.module.scss";

const ContactSection = ({ title, text, buttonText }: any) => {
  return (
    <div className={styles.mainContainer}>
      <h4>{title}</h4>
      <p dangerouslySetInnerHTML={{ __html: text }} />
      <div className={styles.buttons}>
        <a style={{ textDecoration: "none" }} href="mailto:<EMAIL>">
          <Button style={{ marginRight: 24 }}>
            <Envelope />
            {buttonText}
          </Button>
        </a>
        <a
          style={{ textDecoration: "none" }}
          href="https://wa.me/+447920488130"
          target="_blank"
        >
          <Button>
            <img
              src="/images-int/social/WhatsApp.png"
              style={{ width: 24, marginRight: 8 }}
            />
            WhatsApp Us
          </Button>
        </a>
      </div>
    </div>
  );
};

export default ContactSection;
