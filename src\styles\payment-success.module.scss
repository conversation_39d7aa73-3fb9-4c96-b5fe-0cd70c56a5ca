.container {
  flex-grow: 1;
  width: 100%;
  background-image: url("/../public/images-int/payment/payment_success.png");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  align-items: stretch;
  min-height: calc(100vh - 100px);
  @media (max-width: 768px) {
    min-height: initial;
  }
}

.background {
  width: 100%;
  background: linear-gradient(
    180deg,
    rgba(22, 11, 42, 0.48) 0%,
    rgba(22, 11, 42, 0.36) 50%,
    rgba(22, 11, 42, 0.48) 100%
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  text-align: center;
  padding-top: 186px;
  @media (max-width: 768px) {
    padding: 50px 24px 0 24px;
  }
  h2 {
    margin: 0 0 12px 0;
    font-weight: 700;
    font-size: 40px;
    line-height: 60px;
    @media (max-width: 768px) {
      font-size: 30px;
      line-height: 45px;
    }
  }
  p {
    font-size: 20px;
    line-height: 30px;
    margin: 0;
    padding-bottom: 12px;
    @media (max-width: 768px) {
      font-size: 16px;
      line-height: 24px;
    }
  }
}

.appBar {
  width: 100%;
  background: rgba(22, 11, 42, 0.6);
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 33px 164px;
  @media (max-width: 1000px) {
    padding: 33px 24px;
  }
  @media (max-width: 768px) {
    margin: 0 -24px;
    margin-top: auto;
    width: calc(100% + 48px);
    padding: 24px;
    flex-direction: column;
    align-items: flex-start;
  }
  .appText {
    color: #eff1f7;
    h5 {
      margin: 0 0 8px 0;
      font-weight: 700;
      font-size: 20px;
      line-height: 30px;
      text-align: start;
    }
    p {
      margin: 0;
      font-size: 16px;
      line-height: 24px;
      text-align: start;
    }
  }
}

.failedPlans {
  margin-bottom: 36px;
  width: 100%;
  max-width: 1120px;
  display: flex;
  justify-content: center;
  padding: 0 50px;
  @media (min-width: 795px) {
    &.grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-column-gap: 12px;
      grid-row-gap: 12px;
      justify-items: center;
    }
  }
  @media (max-width: 795px) {
    flex-direction: column;
    align-items: center;
    padding: 0 12px;
    > div {
      margin-bottom: 12px;
    }
  }
}
.failedButtons {
  display: flex;
  @media (max-width: 575px) {
    flex-direction: column;
    align-items: center;
    margin-bottom: 50px;
    .dashboardButton {
      margin-top: 20px;
      margin-left: 0px !important;
    }
  }
  .dashboardButton {
    margin-left: 20px;
  }
}
