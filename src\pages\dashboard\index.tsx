import { useDispatch } from "react-redux";
import styles from "../../styles/dashboard.module.scss";
import { useEffect, useState } from "react";
import BundleBoard from "../../components/BundleBoard";
import { useNavigate, useParams } from "react-router-dom";
import WalletSection from "../../components/WalletSection";
import { ApiGet } from "../api/api";
import PaymentHistorySection from "../../components/PaymentHistory";
import AccountSettings from "../../components/AccountSettings";
import TopupLimitModal from "../../components/TopupLimitModal";

const Dashboard = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { section } = useParams();

  const [activeCountry, setActiveCountry] = useState(null as any);

  const [plans, setPlans] = useState([] as any);
  const [plansLoading, setPlansLoading] = useState(true);

  // Get Saved Cards
  const [cardsLoading, setCardsLoading] = useState(true);
  const [cards, setCards] = useState([] as any);

  const getCards = (after = () => {}) => {
    setCardsLoading(true);
    ApiGet("/payments/cards")
      .then((response) => {
        if ("cards" in response.data) {
          setCards(response.data.cards);
        } else {
          setCards([] as any);
        }
        dispatch({
          type: "set",
          cards: response.data.cards,
        });
        setCardsLoading(false);
        after();
      })
      .finally(() => setCardsLoading(false));
  };

  useEffect(getCards, []);

  const reload = (after: any = null, showLoading = true) => {
    if (showLoading) setPlansLoading(true);
    ApiGet("/users")
      .then((response) => {
        let planList = [] as any;
        response.data.esimsDetails.forEach((detail: any) => {
          detail.esimSubscriptions.forEach((subscription: any) => {
            let sub = { ...subscription, iccid: detail.iccid };
            planList.push(sub);
          });
        });
        setPlans(planList);
        setPlansLoading(false);

        if (after) {
          after();
        }
      })
      .catch((error) => {
        console.log(error);
        localStorage.removeItem("token");
        dispatch({
          type: "set",
          loggedIn: false,
        });
        navigate("/login");
      });
  };

  useEffect(reload, []);

  const [showDailyLimit, setShowDailyLimit] = useState(false);
  const [showWeeklyLimit, setShowWeeklyLimit] = useState(false);

  return (
    <div className={`${styles.main} max-width-container`}>
      {section === "cards" ? (
        <WalletSection
          cards={cards}
          cardsLoading={cardsLoading}
          setCardsLoading={setCardsLoading}
          getCards={getCards}
        />
      ) : section === "payments-history" ? (
        <PaymentHistorySection />
      ) : section === "settings" ? (
        <AccountSettings />
      ) : (
        <BundleBoard
          plans={plans?.length ? plans : null}
          activeCountry={activeCountry}
          setActiveCountry={setActiveCountry}
          loading={plansLoading}
          repopulate={reload}
        />
      )}
      <TopupLimitModal
        show={showDailyLimit}
        setShow={setShowDailyLimit}
        type="daily"
      />
      <TopupLimitModal
        show={showWeeklyLimit}
        setShow={setShowWeeklyLimit}
        type="weekly"
      />
    </div>
  );
};

export default Dashboard;
