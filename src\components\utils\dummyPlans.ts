import { v4 as uuidv4 } from "uuid";

export const dummyPlans = [
  {
    countryCode: "GB",
    countryName: "United Kingdom",
    plans: [
      {
        type: "Combo",
        id: uuidv4(),
        data: {
          initial: 5,
          remaining: 2.3,
          display: "2.3 GB left",
        },
        minutes: {
          initial: 100,
          remaining: 43,
          display: "43 mins left",
        },
        sms: {
          initial: 100,
          remaining: 100,
          display: "100 SMS left",
        },
        validity: 23,
        autorenew: true,
      },
      {
        type: "Phone Number",
        id: uuidv4(),
        minutes: {
          initial: 100,
          remaining: 93,
          display: "93 mins left",
        },
        sms: {
          initial: 100,
          remaining: 72,
          display: "72 SMS left",
        },
        validity: 23,
        autorenew: true,
      },
      {
        type: "Data",
        id: uuidv4(),
        data: {
          initial: 5,
          remaining: 2.3,
          display: "2.3 GB left",
        },
        validity: 13,
        autorenew: false,
      },
    ],
  },
  {
    countryCode: "IT",
    countryName: "Italy",
    plans: [
      {
        type: "Data",
        id: uuidv4(),
        data: {
          initial: 5,
          remaining: 2.3,
          display: "2.3 GB left",
        },
        validity: 13,
        autorenew: false,
      },
      {
        type: "Data",
        id: uuidv4(),
        data: {
          initial: 5,
          remaining: 2.3,
          display: "2.3 GB left",
        },
        validity: 13,
        autorenew: false,
      },
      {
        type: "Phone Number",
        id: uuidv4(),
        minutes: {
          initial: 100,
          remaining: 93,
          display: "93 mins left",
        },
        sms: {
          initial: 100,
          remaining: 72,
          display: "72 SMS left",
        },
        validity: 23,
        autorenew: true,
      },
    ],
  },
  {
    countryCode: "ES",
    countryName: "Spain",
    plans: [
      {
        type: "Combo",
        id: uuidv4(),
        data: {
          initial: 5,
          remaining: 2.3,
          display: "2.3 GB left",
        },
        minutes: {
          initial: 100,
          remaining: 43,
          display: "43 mins left",
        },
        sms: {
          initial: 100,
          remaining: 100,
          display: "100 SMS left",
        },
        validity: 23,
        autorenew: true,
      },
    ],
  },
];
