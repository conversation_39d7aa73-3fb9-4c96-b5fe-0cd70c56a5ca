import { useMediaQuery } from "@mui/material";
import Modal from "../Modal";
import styles from "./reg-modal.module.scss";
import Button from "../Button";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";

const RegSuccessModal = ({ show }: any) => {
  const under768 = useMediaQuery("(max-width: 768px)");
  const navigate = useNavigate();

  const handleNextPage = () => {
    let currentBasketStorage = localStorage.getItem("basket");
    let currentBasket = currentBasketStorage ? JSON.parse(currentBasketStorage) : [];
    if (Object.keys(currentBasket).length > 0) {
      navigate("/checkout")
    } else {
      navigate("/dashboard/home")
    }
  }

  return (
    <Modal
      show={show}
      style={{ height: under768 ? "auto" : 350, maxWidth: 490 }}
    >
      <div className={styles.main}>
        <img
          src="/images-int/help/help_activating.svg"
          className={styles.giftImage}
        />
        <h3>{ t('account.msgs.createdAccount') }</h3>
        <p>{ t('account.msgs.successReg') }</p>
        <Button
          style={{
            marginTop: 21,
            fontSize: 14,
            fontWeight: 700,
            lineHeight: "21px",
            height: 48,
            width: "100%",
            maxWidth: 232,
          }}
          onClick={() => handleNextPage()}
        >
          { t('general.continue') }
        </Button>
      </div>
    </Modal>
  );
};

export default RegSuccessModal;
