@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  background-color: $secondary;
  padding: 50px 80px 38px 80px;
  color: #fff;
  @media (max-width: 430px) {
    padding: 50px 24px;
  }
  h5 {
    text-align: center;
  }
}
.divider {
  width: 100%;
  height: 1px;
  background-color: #fff;
  margin-bottom: 20px;
}

.footerLogo {
  width: 150px;
}

.row {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  @media (max-width: 1150px) {
    flex-direction: column;
    justify-content: center;
  }
}

.linksContainer {
  display: flex;
  align-items: center;
  a {
    margin-inline-start: 50.5px;
    text-decoration: none;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: #fff;
    @media (max-width: 768px) {
      margin-inline-start: 30px;
    }
    &:last-child {
      margin-inline-end: 0;
    }
    &:hover {
      color: #eee7f8;
    }
  }
  @media (max-width: 1150px) {
    margin: 20px 0 50px 0;
  }
  @media (max-width: 690px) {
    display: grid;
    grid-template-columns: auto auto;
    grid-column-gap: 50px;
    justify-content: space-between;
    justify-items: start;
    width: 100%;
    grid-row-gap: 30px;
  }
}

.stores {
  margin-top: 15px;
  display: flex;
  align-items: center;
  @media (max-width: 425px) {
    flex-direction: row;
  }
  img {
    margin-right: 10px;
  }
}

.socials {
  color: #fff;
  @media (max-width: 1150px) {
    display: flex;
    align-items: center;
  }
  @media (max-width: 490px) {
    flex-direction: column;
    width: 100%;
    max-width: 280px;
  }
  .text {
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    margin: 0;
    margin-bottom: 17px;
    @media (max-width: 1150px) {
      margin-bottom: 0px;
      margin-right: 30px;
    }
    @media (max-width: 490px) {
      margin-right: 0;
      margin-bottom: 12px;
      text-align: start;
    }
  }
  .socialLinks {
    display: flex;
    gap: 24px;

    align-items: center;
    a {
      color: #fff;
      &:hover {
        color: $light-primary;
      }
      &:last-child {
        margin-right: 0;
      }
    }
    img {
      vertical-align: middle;
      width: 24px;
      height: 24px;
    }
  }
}

.legal {
  display: flex;
  align-items: center;
  gap: 48px;

  a {
    text-decoration: none;
    font-weight: 400;
    font-size: 12px;
    line-height: 24px;
    color: #fff;
    text-align: center;

    &:hover {
      color: #eee7f8;
    }
  }
  @media (max-width: 1150px) {
    align-self: initial;
    margin-top: 25px;
  }
  @media (max-width: 490px) {
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    max-width: 280px;
  }
}

.copyrights {
  font-size: 12px;
}
