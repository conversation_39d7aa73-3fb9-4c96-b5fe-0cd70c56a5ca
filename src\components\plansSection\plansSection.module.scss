.contianer {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  flex-direction: column;
  @media (max-width: 768px) {
    padding: 0px;
  }
  a {
    text-decoration: none;
  }
  .seeAllLink {
    text-decoration: none;
    font-weight: 700;
    color: #088bdd;
    display: flex;
    align-items: center;
    svg {
      margin-inline-start: 8px;
    }
  }
  h4 {
    font-size: 26px;
  }
  .selectedCountry {
    margin-top: 15px;
    border: 1px solid #000;
    border-radius: 25px;
    padding: 20px;
    gap: 8px;
    flex-wrap: wrap;
    display: flex;
  }
  .countryContainer {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: auto;
    grid-column-gap: 19px;
    row-gap: 16px;
    @media (max-width: 768px) {
      display: flex;
      align-items: flex-start;
      overflow-y: scroll;
      grid-column-gap: 16px;
      padding-bottom: 16px;
    }
    .rowOne {
      grid-column: span 5;
    }
    .countryBox {
      background-color: #efeeed;
      padding: 25px 15px 10px 15px;
      border-radius: 12px;
      width: 100%;
      cursor: pointer;
      text-align: center;
      @media (max-width: 768px) {
        padding: 16px 16px 24px 16px;
        min-width: 120px;
        width: auto;
        align-self: stretch;
        border-radius: 24px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
      }
      .flag {
        background-color: #fff;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin: 0 auto 8px auto;
        @media (max-width: 768px) {
          width: 40px;
          height: 40px;
        }
      }
      p {
        width: 100%;
        margin: 0;
        font-size: 15px;
        @media (max-width: 768px) {
          font-size: 14px;
        }
      }
    }
    .countryBoxRegion {
      @media (max-width: 425px) {
        width: 100%;
        display: flex;
        padding: 15px 30px;
        align-items: center;
        .flag,
        img {
          width: 30px;
          height: 30px;
          margin: 0;
        }
      }
    }
    .open {
      background-color: #fff;
      border: 1px solid #000;
      &.open svg {
        transform: rotate(180deg);
      }
    }
  }
  .regionContainer {
    gap: 15px;
    @media (max-width: 650px) {
      flex-direction: column;
      align-items: center;
    }
    .regionBox {
      background: #ceedf5;
      background-size: cover;
      background-position: bottom;
      border-radius: 24px;
      width: calc(100% / 3.2);
      text-align: start;
      padding: 32px;
      cursor: pointer;
      min-height: 230px;
      @media (max-width: 650px) {
        width: 100%;
      }
      h2 {
        font-size: 24px;
        margin: 0px;
        line-height: 28px;
      }
      p {
        font-size: 18px;
        line-height: 28px;
        margin: 0;
      }
    }
  }
  svg {
    width: 25px;
    transition: transform 0.3s ease;
    color: #088bdd;
    @media (max-width: 768px) {
      height: 24px;
      width: 24px;
      position: absolute;
      bottom: 4px;
    }
  }
}
.select_all {
  button {
    margin-top: 20px;
    a {
      color: #fff;
      text-decoration: none;
    }
  }
}
