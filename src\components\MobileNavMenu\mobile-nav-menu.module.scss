@use "../../styles/theme.scss" as *;

.main {
  position: fixed;
  z-index: 2147483601;
  height: 100%;
  overflow-y: auto;
  width: 100%;
  top: 0px;
  left: 0px;
  background: #fff;
  padding: 100px 34px 32px 34px;
  display: flex;
  flex-direction: column;
  .logo {
    width: 94px;
  }
}

.close {
  position: absolute;
  top: 29px;
  right: 26px;
  padding: 0;
  background: 0;
  border: 0;
  color: #088bdd;
  svg {
    width: 24px;
    height: 24px;
  }
}

.searchButton {
  border: none;
  background: none;
  padding: 0;
  color: $dark-dark-purple;
  cursor: pointer;
  height: 30px;
  width: 30px;
  @media (max-width: 635px) {
    height: initial;
    width: initial;
  }
  &:hover {
    color: $secondary;
  }
  svg {
    vertical-align: middle;
    height: 30px;
    width: 30px;
    @media (max-width: 635px) {
      height: initial;
      width: initial;
    }
  }
}

.homeLink {
  position: absolute;
  top: 19px;
  left: 16px;
}

.linksContainer {
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
  width: 100%;
  padding-bottom: 24px;
  .link {
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    border-radius: 100px;
    font-weight: 400;
    font-size: 14px;
    margin-bottom: 12px;
    &.active {
      background: $primary;
      color: #fff;
      font-weight: 700;
    }
    &:hover {
      color: $secondary;
    }
  }
  a {
    text-decoration: none;
    color: $secondary;
  }
}

.buttons {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  margin-top: auto;
}

.name {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  color: $secondary;
  span {
    font-weight: 600;
    margin-left: 12px;
  }
}

.dashLinksContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 14px;
  line-height: 20px;
  margin-top: 24px;
  .title {
    color: #667085;
    margin-bottom: 8px;
  }
  .dashboardLink {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 0px;
    width: 175px;
    border-radius: 12px;
    margin-bottom: 12px;
    svg {
      color: #088bdd;
      width: 24px;
      height: 24px;
      vertical-align: middle;
      margin-right: 8px;
    }
  }
}
