import TablePagination from "@mui/material/TablePagination";
import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON> } from "../svgs";
import { useSelector } from "react-redux";
import { getCardNetworkImage } from "../utils/getCardNetworkImage";
import styles from "./payment-history.module.scss";
import { HashLink as Link } from "react-router-hash-link";
import { Helmet } from 'react-helmet-async';
import Button from "../Button";
import formatDate from "../utils/formatDate";
import { ApiGet } from "../../pages/api/api";
import { formatPrice } from "../utils/formatPrice";
import { CircularProgress } from "@mui/material";
import { t } from "i18next";

const PaymentHistory = () => {
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const { countries, isSmartBannerOpen } = useSelector((state: any) => state);

  const [payments, setPayments] = useState([] as any);

  const [loading, setLoading] = useState(true);

  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    ApiGet("/payments")
      .then((response) => {
        if ("payments" in response.data) {
          setPayments(response.data.payments);
        } else {
          setPayments([]);
        }
        setLoading(false);
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  const handleChangePage = (event: any, newPage: any) => {
    setCurrentPage(newPage);
  };

  return (
    <div className={styles.main}>
      <Helmet>
        <title>{t("general.orbit")} | { t('general.dashboard') } |  { t('account.pages.paymentHistory') }</title>
      </Helmet>
      <div className={styles.topBar} style={{ marginTop: isSmartBannerOpen ? '50px' : '0' }}>
        <h3>{t("account.pages.paymentHistory")}</h3>
      </div>
      {loading ? (
        <div
          style={{ display: "flex", justifyContent: "center", paddingTop: 200 }}
        >
          <CircularProgress
            style={{ color: "#82DEDE", width: 40, height: 40 }}
          />
        </div>
      ) : payments.length > 0 ? (
        <div className={styles.tableContainer}>
          <table className={styles.table}>
            <thead>
              <tr>
                <th>{t("account.paymentHistory.date")}</th>
                <th>{t("account.paymentHistory.country")}</th>
                <th>{t("account.paymentHistory.dataAmount")}</th>
                <th>{t("account.paymentHistory.validity")}</th>
                <th>{t("account.paymentHistory.price")}</th>
                <th>{t("account.paymentHistory.paymentMethod")}</th>
              </tr>
            </thead>
            <tbody>
              {payments
                .slice(
                  (currentPage - 1) * rowsPerPage,
                  (currentPage - 1) * rowsPerPage + rowsPerPage
                )
                .map((payment: any, index: number) => {
                  if (payment.purchasedItems.length > 0) {
                    return payment.purchasedItems.map((purchasedItem: any) => {
                      let countryIcon = countries.find(
                        (country: any) =>
                          country.countryName === purchasedItem.country
                      )?.iconUrl;
                      console.log(countryIcon);
                      return (
                        <tr key={`payment-item-${index}`}>
                          <td>{formatDate(payment.purchaseDate)}</td>
                          <td>
                            {purchasedItem.country === "Worldwide Credit" ? (
                              "-"
                            ) : (
                              <div className="flex items-center">
                                {countryIcon && <img src={countryIcon} />}
                                {purchasedItem.country}
                              </div>
                            )}
                          </td>
                          <td>{purchasedItem.dataAmount}</td>
                          <td>{purchasedItem.daysValidity}</td>
                          <td className={styles.amount}>
                            {purchasedItem.currencySymbol}
                            {formatPrice(parseFloat(purchasedItem.cost))}
                          </td>
                          <td className={styles.card}>
                            <span>
                              <span>{getCardNetworkImage(payment)}</span>
                            {payment.last4Digits
                                ? <><span>••••</span>
                                  <span>{payment.last4Digits}</span></>
                              : "-"}
                            </span>
                          </td>
                        </tr>
                      );
                    });
                  } else {
                    return (
                      <tr key={`payment-item-${index}`}>
                        <td>{formatDate(payment.purchaseDate)}</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td className={styles.amount}>
                          {payment.currencySymbol}
                          {formatPrice(parseFloat(payment.amount))}
                        </td>
                        <td className={styles.card}>
                          {getCardNetworkImage(payment)}
                          {payment.last4Digits
                            ? `•••• ${payment.last4Digits}`
                            : "-"}
                        </td>
                      </tr>
                    );
                  }
                })}
            </tbody>
          </table>
          <div className={`${styles.paginationContainer} blog-pagination`}>
            <TablePagination
              component="div"
              count={payments.length}
              page={currentPage - 1}
              showFirstButton
              showLastButton
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
            />
          </div>
        </div>
      ) : (
        <div className={styles.noBundles}>
          <img
            src="/images-int/dashboard/no_plans.svg"
            className={styles.image}
          />
          <h5>{t("account.paymentHistory.noHistory")}</h5>
          <p>{t("account.paymentHistory.noHistoryMsg")}</p>
          <Link style={{ textDecoration: "none" }} to="/#plans-section">
            <Button>
              <Trolley />
              {t("account.paymentHistory.shopPlans")}
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
};

export default PaymentHistory;
