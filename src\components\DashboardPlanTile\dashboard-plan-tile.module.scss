@use "../../styles/theme.scss" as *;

.main {
  background: #fff;
  border-radius: 24px;
  padding: 20px;
}

.top {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.planStatus {
  padding: 6px 9px;
  display: inline-flex;
  align-items: center;
  border-radius: 10px;
  font-size: 12px;
  line-height: 22px;
  font-weight: 400;
  color: #000014;
  &.active {
    background: linear-gradient(90deg, #cee8f8 0%, #cdf7ee 100%);
  }
  &.inactive {
    background: #efeeed;
  }
  .activeCircle {
    width: 8px;
    height: 8px;
    border-radius: 10px;
    background-color: #088bdd;
    margin-inline-end: 7.5px;
  }
  svg {
    margin-inline-end: 6px;
  }
}

.nameContainer {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  .flag {
    width: 34px;
    height: 34px;
    border-radius: 100px;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    margin-inline-end: 10px;
  }
  .promotionalIcon {
    width: 34px;
    height: 34px;
    border-radius: 100px;
    background-color: #9bf0de;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0f133a;
    font-size: 16px;
    font-weight: 700;
    margin-inline-end: 10px;
  }
  .planInfo {
    font-size: 12px;
    line-height: 16px;
    color: $secondary;
  }
  .titleText {
    font-size: 16px;
    line-height: 22px;
    font-weight: 700;
    color: $secondary;
  }
}

.expiryNotice {
  background-color: #ffdd99;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 10px;
  .top {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 12px;
    line-height: 16px;
    margin-bottom: 6px;
    svg {
      vertical-align: middle;
      margin-inline-end: 8px;
    }
  }
  .text {
    font-size: 10px;
    line-height: 15px;
  }
}

.planData {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-evenly;
  font-size: 12px;
  line-height: 18px;
  margin-bottom: 17px;
  .dataLabel {
    color: #667085;
    display: flex;
    align-items: center;
    svg {
      margin-inline-end: 4px;
      vertical-align: middle;
    }
  }
  .dataValue {
    padding-inline-start: 20px;
  }
}

.bottomSection {
  background: #dadeec;
  border-radius: 0px 0px 24px 24px;
  margin: 24px -24px -24px -24px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $dark-dark-purple;
  &.renew {
    background-color: #cebee3;
  }
  &.expire {
    background-color: #ffb9bc;
  }
}

.autorenew {
  display: flex;
  align-items: center;
  .autorenewText {
    margin-right: 16px;
  }
}

.held {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: $dark-dark-purple;
  img {
    width: 100%;
    max-width: 46px;
    margin-bottom: 12px;
  }
  .renewTitle {
    display: flex;
    align-items: center;
    font-weight: 600;
    margin-bottom: 4px;
    line-height: 24px;
    svg {
      vertical-align: middle;
      margin-right: 2px;
    }
  }
  p {
    margin: 0 0 12px 0;
    font-size: 14px;
    line-height: 21px;
    text-align: center;
    max-width: 374px;
  }
}

.iccidContainer {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  background-color: #fafaf9;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  line-height: 22px;

  .iccidLabel {
    display: flex;
    flex-grow: 1;
    align-items: center;
    color: #667085;
    svg {
      vertical-align: middle;
      margin-inline-end: 4px;
    }
  }

  .iccid {
    display: flex;
    align-items: center;
    color: #000014;
    .copyButton {
      background: none;
      border: none;
      outline: none;
      padding: 0px;
      margin-inline-start: 6px;
      cursor: pointer;
      svg {
        width: 16px;
        height: 16px;
        color: #667085;
      }
    }
  }
}
