@use "../../styles/theme.scss" as *;

.container {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 0 24px;
  background: rgba(22, 11, 42, 0.6);
  @media (max-width: 768px) {
    padding: 12px;
  }
}

.modal {
  width: 100%;
  max-height: 80vh;
  border-radius: 24px;
  background: #fff;
  display: flex;
  position: relative;
  overflow: hidden;
  @media (max-width: 500px) {
    max-height: 100%;
    height: auto !important;
  }
}

.main {
  width: 100%;
  overflow: auto;
  position: relative;
  overflow-x: hidden;
}
