<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link
      rel="icon"
      type="image/png"
      href="/images-int/favicons/favicon-96x96.png"
      sizes="96x96"
    />
    <link
      rel="icon"
      type="image/svg+xml"
      href="/images-int/favicons/favicon.svg"
    />
    <link rel="shortcut icon" href="/images-int/favicons/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="/images-int/favicons/apple-touch-icon.png"
    />
    <meta name="apple-mobile-web-app-title" content="Orbit Mobile" />
    <link rel="manifest" href="/images-int/favicons/site.webmanifest" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no" />
    <meta name="theme-color" content="#000000" />
    <meta name="google-site-verification" content="Zoar8_rlYGUcIqc2MLbk9swGgBwoG_gZDjfrqJYXJ9o" />
    <!--
      <meta name="robots" content="noindex" />
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <script src="https://apis.google.com/js/api.js" async defer></script>
    <script>
      const initGooglePay = () => {
        window.paymentsClient = new google.payments.api.PaymentsClient({
          environment: "TEST",
        });
      };
    </script>
    <script
      async
      src="https://pay.google.com/gp/p/js/pay.js"
      onload="initGooglePay()"
    ></script>
    <script src="https://www.paypal.com/sdk/js?client-id=test&components=buttons,funding-eligibility&disable-funding=card"></script>
    <script>
      window.fbAsyncInit = function () {
        FB.init({
          appId: "***************",
          xfbml: true,
          version: "v21.0",
        });
        FB.AppEvents.logPageView();
      };

      (function (d, s, id) {
        var js,
          fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {
          return;
        }
        js = d.createElement(s);
        js.id = id;
        js.src = "https://connect.facebook.net/en_US/sdk.js";
        fjs.parentNode.insertBefore(js, fjs);
      })(document, "script", "facebook-jssdk");
    </script>
    <!--
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-4XPPWCJXZQ"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-4XPPWCJXZQ');
    </script>
    -->
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() { dataLayer.push(arguments); }
      if ((localStorage.getItem('consentMode') === null)) {
        gtag('consent', 'default', {
          'ad_storage': 'denied',
          'ad_personalization': 'denied',
          'ad_user_data': 'denied',
          'analytics_storage': 'denied',
        });
      } else {
        gtag('consent', 'default', JSON.parse(localStorage.getItem('consentMode')))
      }
      </script>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-W3M3XCL2');</script>
      <!-- End Google Tag Manager -->
    <script src="https://js.stripe.com/v3/"></script>
    <script
      crossorigin
      src="https://applepay.cdn-apple.com/jsapi/1.latest/apple-pay-sdk.js"
    ></script>
    <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>
    <script
      src="//eu.fw-cdn.com/12923931/881455.js"
      chat="true"
      widgetId="6de10ede-7ecf-4b50-9775-93bbd8c34624"
    ></script>
  </head>
  <body>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-W3M3XCL2" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="/src/index.tsx"></script>
    <script type="module" src="/src/App.tsx"></script>
  </body>
</html>
