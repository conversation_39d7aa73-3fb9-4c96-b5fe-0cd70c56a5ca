import styles from "./esim-compatible-modal.module.scss";
import Button from "../Button";
import { Fade } from "@mui/material";
import { useEffect } from "react";
import $ from "jquery";
import { Link } from "react-router-dom";
import { t } from "i18next";

const EsimCompatibleModal = ({ show, setShow, proceed }: any) => {
  useEffect(() => {
    if (show) {
      $(document.body).css("overflow-y", "hidden");
    } else {
      $(document.body).css("overflow-y", "scroll");
    }
  }, [show]);

  return (
    <Fade in={show} unmountOnExit>
      <div className={styles.container}>
        <div className={styles.modal}>
          <div className={styles.main} id="modal-scroll">
            <Button
              onClick={() => {
                setShow(false);
              }}
              style={{ marginLeft: "auto", marginBottom: 2, padding: 0 }}
              color="tertiary"
            >
              { t('buttons.cancel') }
            </Button>
            <svg
              width="97"
              height="97"
              viewBox="0 0 97 97"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M48 39V58"
                stroke="#FF7378"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M43.1526 15.1138L9.89987 72.5504C9.37012 73.4679 9.09078 74.5086 9.08985 75.5681C9.08891 76.6275 9.3664 77.6687 9.89452 78.5871C10.4226 79.5056 11.1829 80.2692 12.099 80.8014C13.0151 81.3335 14.055 81.6156 15.1145 81.6193H81.6201C82.6795 81.6156 83.7194 81.3335 84.6356 80.8014C85.5517 80.2692 86.3119 79.5056 86.8401 78.5871C87.3682 77.6687 87.6457 76.6275 87.6447 75.5681C87.6438 74.5086 87.3645 73.4679 86.8347 72.5504L53.5819 15.1138C53.0562 14.1951 52.297 13.4315 51.3813 12.9005C50.4656 12.3695 49.4258 12.0898 48.3673 12.0898C47.3087 12.0898 46.269 12.3695 45.3533 12.9005C44.4376 13.4315 43.6784 14.1951 43.1526 15.1138V15.1138Z"
                stroke="#FF7378"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M48 68.7012C48.5523 68.7012 49 68.2535 49 67.7012C49 67.1489 48.5523 66.7012 48 66.7012C47.4477 66.7012 47 67.1489 47 67.7012C47 68.2535 47.4477 68.7012 48 68.7012Z"
                fill="#FF7378"
              />
            </svg>
            <h3 className={styles.heading}>
              You will need an eSIM compatible phone to be able to use any
              bundles that provide data.
            </h3>
            <p>
              You can check the{" "}
              <a
                onClick={() => {
                  $(document.body).css("overflow-y", "scroll");
                }}
                href="/esim-compatible-devices"
                target="_blank"
              >
                eSIM compatible devices list
              </a>{" "}
              if you’re unsure about your phone.
            </p>
            <div className={styles.button}>
              <Button
                onClick={() => {
                  setShow(false);
                  proceed();
                }}
                style={{ height: "auto", padding: "13px 24px" }}
              >
                I understand, my phone is eSIM compatible
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Fade>
  );
};

export default EsimCompatibleModal;
