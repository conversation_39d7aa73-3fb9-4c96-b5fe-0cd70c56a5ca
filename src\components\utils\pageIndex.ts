export const pageIndex = [
  /*{
    link: "/blog",
    name: "Blog",
    keywords: "blog post article",
  },
  {
    link: "/dashboard/home",
    name: "Dash<PERSON>",
    keywords: "dashboard my account",
  },*/
  {
    link: "/esim-compatible-devices",
    name: "eSIM compatible phones list",
    keywords: "esim compabitble device list",
    title: "Check if your device is eSIM compatible",
    summary:
      "Stay tech-savvy and ensure that your device carrier is unlocked and that your device supports eSIM. By proceeding you are taking full responsibility for the compatiability of your device. Please note, Gist Mobile will not be liable for any issues due to device incompatiblity.",
  },
  /*{
    link: "/forgot-password",
    name: "Forgot password",
    keywords: "reset forgot password recovery",
  },
  {
    link: "/help",
    name: "Help",
    keywords: "faq help frequently asked questions",
  },
  {
    link: "/how-it-works",
    name: "How it works",
    keywords: "how it works about us gist",
  },
  {
    link: "/login",
    name: "<PERSON><PERSON>",
    keywords: "login log in",
  },
  {
    link: "/offers",
    name: "Offers",
    keywords: "offers",
  },
  {
    link: "/privacy-policy",
    name: "Privacy Policy",
    keywords: "privacy policy legal",
  },
  {
    link: "/terms-and-conditions",
    name: "Terms and conditions",
    keywords: "terms and conditions legal",
  },
  {
    link: "/register",
    name: "Register",
    keywords: "register sign up create account",
  },
  {
    link: "/shop",
    name: "Shop",
    keywords: "shop buy plan purchase",
  },
  {
    link: "/why-gist",
    name: "Why Gist",
    keywords: "why gist about us",
  },*/
];
