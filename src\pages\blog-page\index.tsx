import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import styles from "../../styles/blog-page.module.scss";
import Loader from "../../components/utils/Loader";
import { resolveRichText } from "../../components/utils/richTextConverter";
import moment from "moment";
import { Helmet } from 'react-helmet-async';
import { t } from "i18next";
import { CmsApiGet, cmsURL } from "../api/cms-api";
import FaqAndSearchPlan from "../../components/FaqAndSearchPlan/FaqAndSearchPlan";
import BlogTile from "../../components/BlogTile";
import { FacebookBlack, LinkedinSquare, LinkIcon, XLogoBlack } from "../../components/svgs";

const BlogPage = () => {
  const { slug } = useParams();
  const [blogData, setBlogData] = useState(null as any);
  const [readingTime, setReadingTime] = useState(0)

  useEffect(() => {
    CmsApiGet(`/api/slugify/slugs/blog/${slug}?populate=deep`).then((response) => {
      setBlogData(response.data.data);
    });
  }, []);

  useEffect(() => {
    if (blogData?.attributes.article ) {
      const div = document.getElementById(`blog-${blogData.id}`)
      const paragraphs = div?.getElementsByTagName('p')
      let totalText = ''
      if (paragraphs) {
        for (let p of paragraphs) {
          totalText += p.textContent || p.innerText;  // Get the text content of the paragraph
        }
        const amountOfWords = totalText.split(/\s+/).length
        setReadingTime(Math.round(amountOfWords / 200))
      }
    }
  }, [blogData])

  const returnSharingLinks = () => (
    <div className={styles.shareLink}>
      <Link to="/">
        <LinkIcon />
      </Link>
      <a href={`https://www.linkedin.com/shareArticle?mini=true&url=${encodeURIComponent(window.location.href)}`} target="_blank">
        <LinkedinSquare />
      </a>
      <a href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(window.location.href)}`} target="_blank">
        <XLogoBlack />
      </a>
      <a href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`} target="_blank">
        <FacebookBlack />
      </a>
    </div>
  )

  return (
    <div className={styles.mainContainer}>
      {
        blogData && (
          <Helmet>
            <title>{t("general.orbit")}| {blogData.attributes.title}</title>
            <meta name="description" content={ t('help.howTogetHelp') } />
          </Helmet>
        )
      }
      {blogData ? (
        <div className={styles.blogArticle}>
          <h2>{blogData.attributes.title}</h2>
          <div className={styles.blogInfo}>
            <div className={styles.authorData}>
              <img src={!blogData.attributes.authorImg.data ? "/images-int/blog/defaultAvatar.png" : cmsURL + blogData.attributes.authorImg.data.attributes.url } />
              <div>
                <p className={styles.author}>{blogData.attributes.author}</p>
                <p className={styles.date}>{ moment(blogData.attributes.createdAt).format("DD MMM YYYY") } • { readingTime } min read</p>
              </div>
            </div>
            { returnSharingLinks() }
          </div>
          {
              blogData.attributes.headerImg.data && (
                <div className={styles.headerImg} style={{ backgroundImage: `url(${cmsURL + blogData.attributes.headerImg.data.attributes.url})` }}></div>
              )
          }
          <div className={styles.container}>
            <div
            className={styles.content}
            dangerouslySetInnerHTML={{
              __html: blogData.attributes.article ? resolveRichText(blogData.attributes.article) : "",
            }}
            id={`blog-${blogData.id}`}
          />
          <div className={styles.blogInfo}>
            <div className={styles.authorData}>
              <img src={!blogData.attributes.authorImg.data ? "/images-int/blog/defaultAvatar.png" : cmsURL + blogData.attributes.authorImg.data.thumbnail.url } />
            <div>
            <p className={styles.author}>{blogData.attributes.author}</p>
            <p className={styles.date}>{blogData.attributes.jobTitle}</p>
          </div>
        </div>
        { returnSharingLinks() }
      </div>
    </div>
    <div className={styles.relatedArticles}>
        <h3>Articles</h3>
        <div className="flex">
          {
            blogData.attributes.related_blogs && blogData.attributes.related_blogs.data.map((article:any, index: number) => {
              if (index < 3) {
                return  <BlogTile blog={article} key={index} />
              }
            }
          )
          }
        </div>
    </div>
  </div>
      ) : (
        <Loader />
      )}
      <FaqAndSearchPlan faqOnly />
    </div>
  );
};

export default BlogPage;
