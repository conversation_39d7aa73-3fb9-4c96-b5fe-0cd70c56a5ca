@use "../styles/theme.scss" as *;

.topSection {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  color: #fff;
  position: relative;
  .background {
    background: linear-gradient(
      90deg,
      rgba(22, 11, 42, 0.6) 0%,
      rgba(22, 11, 42, 0.3) 50%,
      rgba(22, 11, 42, 0.6) 100%
    );
    padding: 155px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    @media (max-width: 768px) {
      padding: 155px 24px;
    }
  }
  h2 {
    font-weight: 700;
    font-size: 40px;
    line-height: 60px;
    margin: 0 0 16px 0;
    text-align: center;
  }
}

.countries {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  padding: 50px;
  grid-row-gap: 60px;
  grid-column-gap: 25px;
  @media (max-width: 2500px) {
    grid-template-columns: repeat(6, 1fr);
  }
  @media (max-width: 2150px) {
    grid-template-columns: repeat(5, 1fr);
  }
  @media (max-width: 1100px) {
    grid-template-columns: repeat(4, 1fr);
  }
  @media (max-width: 900px) {
    grid-template-columns: repeat(3, 1fr);
  }
  @media (max-width: 768px) {
    padding: 40px 24px;
    grid-template-columns: repeat(2, 1fr);
    grid-row-gap: 24px;
    grid-column-gap: 24px;
  }
}

.backButton {
  padding: 0;
  background: none;
  border: none;
  position: absolute;
  top: 27px;
  left: 55px;
  cursor: pointer;
  color: #fff;
  @media (max-width: 768px) {
    top: 24px;
    left: 24px;
  }
  svg {
    vertical-align: middle;
  }
}
