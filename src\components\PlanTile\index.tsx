import { useDispatch, useSelector } from "react-redux";
import Button from "../Button";
import { useNavigate } from "react-router-dom";
import styles from "./plan-tile.module.scss";
import { useParams } from "react-router-dom";
import { useTranslation } from 'react-i18next';
import { t } from "i18next";

const PlanTile = ({ plan, index, zoneImage = null }: any) => {
  const { i18n } = useTranslation();
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { loggedIn } = useSelector((state: any) => state);

  return (
    <div className={`${styles.planBox} ${styles[`color-${index % 5}`]} ${styles[`color-${plan.dataAllowance === 1 && id === '2' && plan.validity === 1 && 'special'}`]}`}>
      {
        plan.prices[0].discountAmount && (
          <div className={styles.discount}>
            <p className={styles.discountAmount} style={{ backgroundImage: `url('/images-int/shop/discount${i18n.language === 'ar' ? 'Ar' : ''}.svg')`, width: i18n.language === 'ar' ? '100px' : '120px' }}><span>{ plan.prices[0].discountPercentage }% { t('plan.discount') }</span></p>
            <span className={styles.oldPrice}>{ t('plan.was') } <span style={{ textDecoration: 'line-through' }}>${ plan.prices[0].cost.toFixed(2) }</span></span>
          </div>
        )
      }
      <h4 className={styles.dataAmount}>{plan.dataAllowance} { t('plan.gbData') }</h4>
      {
        i18n.language === 'en' ? (
          <p className={styles.validity}>{plan.validity} { (plan.validity > 1 ? t('plan.days') : 'day') + ' ' + t('plan.validity')} </p>
        ) : (
          <p className={styles.validity}>{t('plan.validity')} {plan.validity > 1 && plan.validity} { plan.validity > 10 ?  'يوماً' : plan.validity === 1 ? 'يوم واحد' : t('plan.days')}</p>
        )
      }
      <Button
        style={{ margin: "0 auto", width: "100%", background: plan.dataAllowance === 1 && id === '2' && plan.validity === 1 && '#DD084B' }}
        onClick={() => {
          let planToAdd = { ...plan };

          if (zoneImage !== null) {
            planToAdd.zoneImage = zoneImage;
          }

          localStorage.setItem("basket", JSON.stringify(planToAdd));

          dispatch({
            type: "set",
            basket: planToAdd,
          });

          if (loggedIn) {
            navigate("/checkout");
          } else {
            navigate("/login");
          }
        }}
      >
        {t('plan.buyFor')} {plan.prices[0].currencySymbol}
        {plan.prices[0].discountAmount ? plan.prices[0].discountedCost.toFixed(2) : plan.prices[0].cost.toFixed(2)}
      </Button>
    </div>
  );
};

export default PlanTile;
