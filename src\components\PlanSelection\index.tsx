import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import PlansPageLayout from "../../components/PlansPageLayout";
import styles from "./plan-selection.module.scss";
import PlanTile from "../../components/PlanTile";
import Button from "../../components/Button";
import { motion } from "framer-motion";
import { v4 as uuidv4 } from "uuid";
import { Swiper, SwiperSlide } from "swiper/react";
import SwiperCore, { Navigation, Pagination } from "swiper";
import "swiper/css";
import { Collapse, useMediaQuery } from "@mui/material";
import { CaretLeft, CaretRight } from "../svgs";
import { SwitchTransition, CSSTransition } from "react-transition-group";

SwiperCore.use([Navigation, Pagination]);

const getPlanType = (path: string) => {
  if (path.includes("combo")) {
    return "COMBO";
  } else if (path.includes("data")) {
    return "DATA";
  } else if (path.includes("phone")) {
    return "PHONE";
  } else if (path.includes("credit")) {
    return "CREDIT";
  }
};

const PlanSelection = ({
  planTypes,
  pageTitle,
  pageText = "",
  showNumberType,
}: any) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { countryCode } = useParams();

  const [activePlan, setActivePlan] = useState(-1);

  const { selectedCountry, toastersOnLoad } = useSelector(
    (state: any) => state
  );

  const [activeNumberType, setActiveNumberType] = useState("mobile");

  const handleAddToCart = () => {
    let planToAdd = planTypes.find(
      (planItem: any) => planItem.planId === activePlan
    ) as any;
    planToAdd.iconUrl = selectedCountry.iconUrl;
    planToAdd.countryName = selectedCountry.countryName;
    planToAdd.numberType = activeNumberType;
    planToAdd.basketId = uuidv4();
    planToAdd.planType = getPlanType(location.pathname);
    planToAdd.autoRenew = false;
    if (showNumberType) {
      if (activeNumberType === "landline") {
        planToAdd.didType = "LOCAL";
      } else if (activeNumberType === "mobile") {
        planToAdd.didType = "MOBILE";
      }
    } else {
      planToAdd.didType = "NA";
    }

    let currentBasketStorage = localStorage.getItem("basket");

    let currentBasket = currentBasketStorage
      ? JSON.parse(currentBasketStorage)
      : [];

    let newBasket = [...currentBasket, planToAdd];

    localStorage.setItem("basket", JSON.stringify(newBasket));

    dispatch({
      type: "addToBasket",
      payload: planToAdd,
    });

    dispatch({
      type: "set",
      toastersOnLoad: {
        ...toastersOnLoad,
        addedToBasket: true,
      },
    });

    navigate(`/shop/select-plan/${countryCode}`);
  };

  // store controlled swiper instance
  const [controlledSwiper, setControlledSwiper] = useState(null as any);
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    if (planTypes.length) {
      setActivePlan(planTypes[activeIndex].planId);
    }
  }, [activeIndex, planTypes]);

  useEffect(() => {
    if (planTypes.length) {
      setActiveIndex(0);
    }
  }, [planTypes]);

  useEffect(() => {
    if (showNumberType && activePlan >= 0) {
      const active = planTypes.find((item: any) => item.planId === activePlan);
      if (!active.hasMobileNumber && activeNumberType === "mobile") {
        setActiveNumberType("landline");
      } else if (!active.hasLocalNumber && activeNumberType === "landline") {
        setActiveNumberType("mobile");
      }
    }
  }, [activePlan]);

  const under1090 = useMediaQuery("(max-width: 1090px)");

  return (
    <PlansPageLayout backLink={`/shop/select-plan/${countryCode}`}>
      {(location.pathname.includes("data") ||
        location.pathname.includes("combo")) && (
        <div className={styles.esimWarning}>
          <svg
            width="30"
            height="26"
            viewBox="0 0 30 26"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M15 10V16.8744"
              stroke="#FF7378"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M13.1413 2.04379L1.28873 21.8696C1.0999 22.1863 1.00034 22.5455 1 22.9112C0.999666 23.2769 1.09858 23.6363 1.28682 23.9534C1.47506 24.2704 1.74603 24.534 2.07258 24.7177C2.39912 24.9013 2.76978 24.9987 3.14742 25H26.8526C27.2302 24.9987 27.6009 24.9013 27.9274 24.7177C28.254 24.534 28.5249 24.2704 28.7132 23.9534C28.9014 23.6363 29.0003 23.2769 29 22.9112C28.9997 22.5455 28.9001 22.1863 28.7113 21.8696L16.8587 2.04379C16.6713 1.72667 16.4007 1.46312 16.0743 1.27983C15.7479 1.09653 15.3773 1 15 1C14.6227 1 14.2521 1.09653 13.9257 1.27983C13.5993 1.46312 13.3287 1.72667 13.1413 2.04379V2.04379Z"
              stroke="#FF7378"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M15 22.0234C15.5523 22.0234 16 21.5757 16 21.0234C16 20.4712 15.5523 20.0234 15 20.0234C14.4477 20.0234 14 20.4712 14 21.0234C14 21.5757 14.4477 22.0234 15 22.0234Z"
              fill="#FF7378"
            />
          </svg>
          <div>
            To avoid an error message, check out our{" "}
            <a
              href="https://gistmobile.com/esim-compatibility/"
              target="_blank"
            >
              eSIM-compatible phone list
            </a>{" "}
            and check that your device carrier is unlocked.
          </div>
        </div>
      )}
      <div className={styles.mobilePlanTypeInfo}>
        <h5>{pageTitle}</h5>
        <p>{pageText}</p>
      </div>
      <div
        className={
          showNumberType
            ? styles.mainContainer
            : styles.mainContainerNoNumberType
        }
      >
        {showNumberType && (
          <div className={styles.numberTypeSelector}>
            <div className={styles.heading}>I need a</div>
            <div
              className={`${styles.numberType} ${
                activeNumberType === "mobile" && styles.active
              } ${
                !planTypes.find((item: any) => item.planId === activePlan)
                  ?.hasMobileNumber && styles.disabled
              }`}
              style={{ marginBottom: 12 }}
              onClick={() => {
                setActiveNumberType("mobile");
              }}
            >
              <div className={styles.button}>Mobile Number</div>
              {activeNumberType === "mobile" && (
                <motion.img
                  src="/images-int/shop/purple_background.svg"
                  className={styles.purpleBackground}
                  layoutId="purple_background"
                />
              )}
            </div>
            <div
              className={`${styles.numberType} ${
                activeNumberType === "landline" && styles.active
              } ${
                !planTypes.find((item: any) => item.planId === activePlan)
                  ?.hasLocalNumber && styles.disabled
              }`}
              onClick={() => {
                setActiveNumberType("landline");
              }}
            >
              <div className={styles.button}>Local Number</div>
              {activeNumberType === "landline" && (
                <motion.img
                  src="/images-int/shop/purple_background.svg"
                  className={styles.purpleBackground}
                  layoutId="purple_background"
                />
              )}
            </div>
          </div>
        )}
        {planTypes.length > 4 || under1090 ? (
          <div className={styles.desktopCarousel}>
            <div className={`${styles.arrowPrev} swiper-button-prev`}>
              <CaretLeft />
            </div>
            <div className={`${styles.arrowNext} swiper-button-next`}>
              <CaretRight />
            </div>
            <Swiper
              spaceBetween={0}
              breakpoints={{
                0: {
                  slidesPerView: 2.3,
                },
                900: {
                  slidesPerView: 3,
                },
                1100: {
                  slidesPerView: 3.8,
                },
                1280: {
                  slidesPerView: 4.8,
                },
              }}
              speed={700}
              centeredSlides
              onSwiper={(swiper: any) => setControlledSwiper(swiper)}
              onSlideChange={(swiper: any) => {
                setActiveIndex(swiper.activeIndex);
              }}
              initialSlide={0}
              navigation={{
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
              }}
              pagination
            >
              {planTypes.map((planItem: any, index: any) => (
                <SwiperSlide
                  key={"plan-tile-" + index}
                  onClick={() => {
                    controlledSwiper.slideTo(index);
                  }}
                >
                  <PlanTile
                    price={planItem.prices[0]}
                    data={planItem.dataAllowance}
                    minutes={planItem.voiceAllowance}
                    sms={planItem.smsAllowance}
                    credit={planItem.credit}
                    validity={planItem.validity}
                    active
                    popular={planItem.popular}
                    style={{
                      maxWidth: 264,
                      transform: activeIndex === index ? "" : "scale(0.75)",
                      opacity: activeIndex === index ? "1" : "0.7",
                      boxShadow: activeIndex === index ? "" : "none",
                    }}
                    containerStyle={{
                      overflow: activeIndex === index ? "visible" : "hidden",
                    }}
                  />
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        ) : (
          <div className={styles.plansContainer}>
            {planTypes.map((planItem: any, index: number) => (
              <PlanTile
                price={planItem.prices[0]}
                data={planItem.dataAllowance}
                minutes={planItem.voiceAllowance}
                sms={planItem.smsAllowance}
                credit={planItem.credit}
                validity={planItem.validity}
                active={activePlan === planItem.planId}
                onClick={() => {
                  setActivePlan(planItem.planId);
                }}
                popular={planItem.popular}
                key={"plan-tile-" + planItem.planId}
              />
            ))}
          </div>
        )}
        <div className={`${styles.mobilePlansContainer} plans-carousel`}>
          <Swiper
            spaceBetween={0}
            breakpoints={{
              0: {
                slidesPerView: 1.4,
              },
              405: {
                slidesPerView: 1.6,
              },
              470: {
                slidesPerView: 1.8,
              },
              545: {
                slidesPerView: 2,
              },
              660: {
                slidesPerView: 2.5,
              },
            }}
            speed={700}
            centeredSlides
            onSwiper={(swiper: any) => setControlledSwiper(swiper)}
            onSlideChange={(swiper: any) => {
              setActiveIndex(swiper.activeIndex);
            }}
            initialSlide={0}
          >
            {planTypes.map((planItem: any, index: any) => (
              <SwiperSlide
                key={"plan-tile-" + index}
                onClick={() => {
                  setActiveIndex(index);
                  controlledSwiper.slideTo(index);
                }}
              >
                <PlanTile
                  price={planItem.prices[0]}
                  data={planItem.dataAllowance}
                  minutes={planItem.voiceAllowance}
                  sms={planItem.smsAllowance}
                  credit={planItem.credit}
                  validity={planItem.validity}
                  active
                  popular={planItem.popular}
                  style={{
                    transform: activeIndex === index ? "" : "scale(0.75)",
                    opacity: activeIndex === index ? "1" : "0.7",
                    boxShadow: activeIndex === index ? "" : "none",
                  }}
                  containerStyle={{
                    overflow: activeIndex === index ? "visible" : "hidden",
                  }}
                />
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
      <div className={styles.bottomContainer}>
        <div className={styles.planTypeInfo}>
          <h5>{pageTitle}</h5>
          <p>{pageText}</p>
        </div>
        <Button
          onClick={handleAddToCart}
          disabled={(showNumberType && !activeNumberType) || !activePlan}
          style={{ whiteSpace: "nowrap" }}
        >
          Add to cart
        </Button>
      </div>
    </PlansPageLayout>
  );
};

export default PlanSelection;
