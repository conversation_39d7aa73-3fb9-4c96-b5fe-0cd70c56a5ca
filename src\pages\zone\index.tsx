import styles from "../../styles/zone.module.scss";
import { useSelector } from "react-redux";
import { Link, useParams } from "react-router-dom";
import Country from "../../components/Country";
import { ArrowBack } from "../../components/svgs";

const Zone = () => {
  const { zones } = useSelector((state: any) => state);

  const { id } = useParams();

  return (
    <div className={styles.container}>
      <div
        className={styles.topSection}
        style={{
          backgroundImage: zones.length
            ? `url(${
                zones.find((zone: any) => zone.zoneName.toLowerCase() === id)
                  .zoneImage
              }`
            : "",
        }}
      >
        <div className={styles.background}>
          <h2>{id!.slice(0, 1).toUpperCase() + id!.slice(1)} Countries</h2>
          <Link to="/shop">
            <button className={styles.backButton}>
              <ArrowBack />
            </button>
          </Link>
        </div>
      </div>
      <div className={styles.countries}>
        {zones.length
          ? zones
              .find((zone: any) => zone.zoneName.toLowerCase() === id)
              .countries.map((country: any) => (
                <Country
                  key={`country-tile-${country.countryName}`}
                  name={country.countryName}
                  image={country.countryImage}
                  flag={country.iconUrl}
                  countryCode={country.countryCode}
                />
              ))
          : ""}
      </div>
    </div>
  );
};

export default Zone;
