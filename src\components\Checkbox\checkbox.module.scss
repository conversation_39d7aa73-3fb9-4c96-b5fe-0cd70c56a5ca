@use "../../styles/theme.scss" as *;

.container {
  position: relative;
  width: 20px;
  height: 20px;
}
.box {
  border: 1px solid #aaaaaa;
  border-radius: 3px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.1s ease;
  width: 100%;
  height: 100%;
  background: #fafafa;
  &.disabled {
    cursor: auto;
    &.indeterminate {
      &:hover {
        background: $light-primary;
        border-color: $light-primary;
        .tick {
          opacity: 1;
        }
      }
    }
    &:hover {
      background: none;
      border-color: #aaaaaa;
      .tick {
        opacity: 0;
      }
    }
  }
  &.indeterminate {
    .tick {
      opacity: 1;
    }
  }
  &:hover {
    border-color: rgba(0, 0, 0, 0.05);
  }
  svg {
    width: 100%;
  }
}
.checked {
  &:hover {
    border-color: #aaaaaa;
  }
}
.checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.tick {
  opacity: 0;
  transition: all 0.1s ease;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    width: 16px;
    height: 16px;
  }
  &.tickChecked {
    opacity: 1;
  }
}
