import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { ApiGet } from "../api/api";
import styles from "../../styles/regional-plans.module.scss";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import PlanTile from "../../components/PlanTile";
import PlanTileSkeleton from "../../components/PlanTileSkeleton";
import { t } from "i18next";
import { productSchema } from "../../components/utils/schemaMarkups";
import { Helmet } from "react-helmet-async";

export type regionTypes = {
  countries?: [];
  zoneName?: String;
  zoneId?: Number;
  zoneImage?: String;
};

const RegionPlans = () => {
  const { i18n } = useTranslation();
  const { customZones: zones, loggedIn } = useSelector((state: any) => state);
  const [plans, setPlans] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [selectedZone, setSelectedZone] = useState<regionTypes>({});
  const [loading, setLoading] = useState(true);

  const { id } = useParams();

  useEffect(() => {
    if (zones.length > 0) {
      let filteredZone = zones.filter(
        (zone: any) => zone.zoneId === Number(id)
      );
      let sortedCountries = filteredZone[0].countries.sort((a:any,b:any) => {
        if (a.countryName < b.countryName) {
          return -1
        }
        return 1
      })
      setSelectedZone(filteredZone[0]);
      setSearchResults(sortedCountries);
    }
  }, [zones, id]);

  const getPlans = () => {
    ApiGet(`/plans?customZoneId=${id}`, null, null, i18n.language).then((response: any) => {
      setLoading(false);
      if (response) {
        setPlans(
          response.data.esimPlans.map((plan: any) => ({
            ...plan,
          }))
        );
      }
    });
  };

  useEffect(getPlans, [i18n, i18n.language]);

  const returnPlans = () => {
    return (
      <div className={styles.plans}>
        {loading ? (
          Array.from({ length: 10 }).map((x, i) => (
            <PlanTileSkeleton index={i} />
          ))
        ) : plans.length > 0 ? (
          plans.map((plan: any, index) => {
            return (
              <PlanTile
                plan={plan}
                key={`plantile-${index}`}
                index={index}
                zoneImage={selectedZone?.zoneImage}
              />
            );
          })
        ) : (
          <p>No Plans Found</p>
        )}
      </div>
    );
  };

  return (
    <>
      <Helmet>
        <title>
          {t("general.orbit")} | {i18n.language === "en" ? selectedZone.zoneName + ' Plans' : selectedZone.zoneName}
        </title>
        <script type="application/ld+json">
          {plans.length > 0 ? JSON.stringify(productSchema(plans, `plans/region/${id}/select-plan`)) : ''}
        </script>
      </Helmet>
      <div
        className={styles.plansView}
        style={{
          backgroundImage: `url(${selectedZone?.zoneImage})`,
        }}
      >
        {i18n.language === "en" ? (
          <h2>{selectedZone.zoneName} Plans</h2>
        ) : (
          <h2>{selectedZone.zoneName}</h2>
        )}
      </div>
      <div className={"flex " + styles.container}>{returnPlans()}</div>
      <div className={styles.countries}>
        <h4>{t("plan.countriesInclud")}</h4>
        <p>{t("plan.ifTravelCovered")}</p>
        <div className={styles.includedCountryContainer}>
          {searchResults?.map((country: any) => {
            return (
              <div className={styles.includedCountry}>
                <div
                  className={styles.flag}
                  style={{ backgroundImage: `url(${country.iconUrl})` }}
                />
                {country.countryName}
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default RegionPlans;
