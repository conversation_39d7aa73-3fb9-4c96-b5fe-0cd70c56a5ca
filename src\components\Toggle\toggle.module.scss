.main {
  width: 400px;
  height: 50px;
  border-radius: 35px;
  position: relative;
  overflow: hidden;
  margin: 25px auto;
  @media (max-width: 425px) {
    width: 290px
  }
  @media (max-width: 350px) {
    width: 240px
  }
  div {
    display: flex;
    height: 50px;
    width: 200px;
    justify-content: center;
    align-items: center;
    color: #0F133A;
    font-size: 14px;
  }
}

.thumb {
  transition: all 0.35s ease;
  border-radius: 50px;
  font-weight: 700;
  background: linear-gradient(90deg, rgba(54, 226, 188, 1) 0%, rgba(57, 161, 227, 1) 100%);
}
