import { Fade } from "@mui/material";
import { useDispatch } from "react-redux";
import { Close, Copy } from "../svgs";
import styles from "./share-number.module.scss";
import { parsePhoneNumber } from "libphonenumber-js";

const ShareNumber = ({ show, setShow, number }: any) => {
  const dispatch = useDispatch();

  const handleCopy = () => {
    if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
      dispatch({
        type: "notify",
        payload: {
          error: false,
          heading: "Copied to clipboard",
          message: "",
        },
      });
      return navigator.clipboard.writeText(number);
    }
  };

  const getFormattedPhone = (number: any) => {
    try {
      let phone = number;
      if (phone[0] !== "+") {
        phone = "+" + phone;
      }
      return parsePhoneNumber(phone).formatInternational();
    } catch (e) {
      return number;
    }
  };

  const shareMessage = `Hey, my number is ${getFormattedPhone(number).replace(
    "+",
    "%2B"
  )}`;

  return (
    <Fade in={show} unmountOnExit>
      <div className={styles.container}>
        <div className={styles.modal}>
          <div className={styles.main} id="modal-scroll">
            <button
              onClick={() => {
                setShow(false);
              }}
              className={styles.closeButton}
            >
              <Close />
            </button>
            <h4>Share Method</h4>
            <div className={styles.buttonContainer}>
              <a
                href={"https://api.whatsapp.com/send?text=" + shareMessage}
                target="_blank"
              >
                <img src="/images-int/social/_WhatsApp.svg" />
              </a>
              {/*<a
                href="https://www.facebook.com/dialog/send?app_id=3251380751779748&link=hello&redirect_uri=https://gist.mobiliseconnect.com/dashboard/home"
                target="_blank"
              >
                <img src="/images-int/social/_Facebook Messenger.svg" />
            </a>*/}
              <a
                href={
                  "https://twitter.com/intent/tweet?text=Hello%20world" +
                  shareMessage
                }
                target="_blank"
              >
                <img
                  className={styles.xLogo}
                  src="/images-int/social/x-logo.png"
                />
              </a>
              {/*<a>
                <img src="/images-int/social/_Instagram.svg" />
              </a>*/}
              <a
                href={`https://www.linkedin.com/feed/?shareActive=true&text=${shareMessage}`}
                target="_blank"
              >
                <img src="/images-int/social/_Linkedin.svg" />
              </a>
              {/*<a>
                <img src="/images-int/social/_Snapchat.svg" />
            </a>*/}
            </div>
            <div className={styles.numberContainer}>
              <div className={styles.number}>{getFormattedPhone(number)}</div>
              <div className={styles.copy} onClick={handleCopy}>
                <Copy />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Fade>
  );
};

export default ShareNumber;
