import { useState, useEffect } from "react";
import SearchBar from "../SearchBar";
import { NoSearchResults } from "../NoSearchResults/NoSearchResults";
import { useSelector } from "react-redux";
import styles from "./plansSection.module.scss";
import { ArrowBack, ArrowNext, CaretDown } from "../svgs";
import { ApiGet } from "../../pages/api/api";
import { Link, useNavigate } from "react-router-dom";
import Button from "../Button";
import Toggle from "../Toggle";
import PlanTileSkeleton from "../PlanTileSkeleton";
import PlanTile from "../PlanTile";
import { t } from "i18next";
import { useTranslation } from "react-i18next";
import searchStyles from "../../styles/region-plans.module.scss";
import { useMediaQuery } from "@mui/material";

type view = "regions" | "countries" | '' | 'الدول' | 'المناطق';

export const PlansSection = ({ selectedView, toggleViewFn } : { selectedView: view, toggleViewFn: Function }) => {
  const { countries, zones, customZones } = useSelector((state: any) => state);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [selectedZone, setSelectedZone] = useState<'top' | 'all' | ''>("");
  const [plans, setPlans] = useState([]);
  const [view, setView] = useState<view>("countries");
  const [selectedIndex, setSelectedIndex] = useState(null);
  const [plansLoading, setPlansLoading] = useState(false);
  const [topDest, setTopDest] = useState([])
  const { i18n } = useTranslation();

  const navigate = useNavigate();

  useEffect(() => {
    ApiGet('/plans/top/countries', null, null, i18n.language).then((response: any) => {
      setTopDest(response.data.countries)
    })
  }, [i18n, i18n.language])

  useEffect(() => {
    if (searchQuery === "") {
      clearSearch();
    } else {
      handleSearch();
    }
  }, [searchQuery]);

  useEffect(() => {
    clearSearch()
  }, [view])

  useEffect(() => {
    if (selectedView) {
      setView(selectedView)
      toggleViewFn()
    }
  }, [selectedView])

  useEffect(() => {
    if (i18n.language === 'ar') {
      if (view === 'countries') {
        setView('الدول')
      } else if (view === 'regions') {
        setView('المناطق')
      }
    } else {
      if (view === 'الدول') {
        setView('countries')
      } else if (view === 'المناطق') {
        setView('regions')
      }
    }
  }, [i18n.language])

  const handleSearchChange = (e: any) => {
    setSearchQuery(e.target.value);
  };

  const clearSearch = () => {
    setSearchQuery("");
    setSearchResults([]);
  };

  const returnPlans = (type = "") => {
    return (
      <div
        className={
          type === "search"
            ? searchStyles.selectedCountry
            : styles.selectedCountry
        }>
        {plansLoading ? (
          Array.from({ length: 5 }).map((x, i) => (
            <PlanTileSkeleton index={i} />
          ))
        ) : plans.length > 0 ? (
          plans.map((plan: any, index) => {
            return <PlanTile plan={plan} index={index} />;
          })
        ) : (
          <p>No Plans Found</p>
        )}
      </div>
    );
  };

  const handleView = (view: any) => {
    setView(view.toLowerCase());
  };

  const handleSearch = () => {
    if (searchQuery === "") {
      clearSearch();
      return;
    }
    let filteredCountries = countries.filter((country: any) =>
      country.countryName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      country.countryCode.toLowerCase().includes(searchQuery.toLowerCase())
    );

    let filteredZones = customZones.filter((zone: any) => zone.zoneName.toLowerCase().includes(searchQuery.toLowerCase()));

    const filteredCustomZones = customZones.filter((zone: any) => zone.countries.some((country:any) => country.countryName.toLowerCase().includes(searchQuery.toLowerCase())));
    filteredZones = filteredZones.concat(filteredCustomZones);
    filteredZones = filteredZones.filter((zone:any, index: number, self:any) => 
      index === self.findIndex((t:any) => {
          return t.zoneName === zone.zoneName
      }
      )
    );
    filteredCountries = filteredCountries.concat(filteredZones)
    setSearchResults(filteredCountries);
  };

  const handleSelect = (code: any, type: '' | 'top' | 'all', index: any, zone?: string) => {
    if (zone && !code) {
      navigate(`/plans/region/${zone}/select-plan`)
    } else if (code === selectedCountry) {
      setIsOpen(false);
      setSelectedCountry("");
      setSelectedZone("");
      setSelectedIndex(null);
    } else {
      setPlansLoading(true);
      setSelectedCountry(code);
      setSelectedZone(type);
      setSelectedIndex(index);
      setIsOpen(true);
      ApiGet(`/plans?countryCode=${code}`).then((response: any) => {
        if (response) {
          setPlansLoading(false);
          setPlans(
            response.data.esimPlans.map((plan: any) => ({
              ...plan,
              countryCode: code,
            }))
          );
        }
      });
    }
  };

  const under768 = useMediaQuery("(max-width: 768px)");

  return (
    <div>
      <SearchBar
        handleSearch={handleSearch}
        id="plan-search-input"
        searchQuery={searchQuery}
        placeholder={t("placeholders.searchCountryRegion")}
        handleSearchChange={handleSearchChange}
        clearSearch={clearSearch}
        maxWidth={420}
      />
      <Toggle
        choices={[t("home.countries"), t("home.regions")]}
        defaultChoice={view !== 'regions' ? t("home.countries") : t("home.regions")}
        handleSelected={(choice: string) => handleView(choice)} />

      {searchResults.length > 0 ? (
        <div className={"flex " + searchStyles.contianer}>
          <div
            className={
              "flex justify-content-between flex-wrap " +
              searchStyles.countryContainer
            }
          >
            {searchResults.map((country: any, index) => {
              return (
                <>
                  <div
                    key={country.countryCode}
                    onClick={() =>
                      handleSelect(country.countryCode, '', index, country.zoneName ? country.zoneId : '')
                    }
                    className={
                      (selectedCountry === country.countryCode
                        ? searchStyles.open
                        : "") +
                      " " +
                      searchStyles.countryBox +
                      " " +
                      searchStyles.countryBoxRegion
                    }
                  >
                    <div
                      className={searchStyles.flag}
                      style={{ backgroundImage: `url(${country.countryName ? country.iconUrl : country.zoneImage})` }}
                    />
                    <div>
                      <p>{country.countryName ? country.countryName : country.zoneName}</p>
                      {
                        country && country.zoneName && (
                          <span>
                            {country.countries.length}{" "}
                            {country.countries.length === 1
                              ? t("home.desCoveredOne")
                              : t("home.desCovered")}
                          </span>
                        )
                      }
                    </div>
                    <CaretDown />
                  </div>
                  {under768
                    ? selectedCountry === country.countryCode && (
                        <>{isOpen && returnPlans()}</>
                      )
                    : ((index + 1) % 5 === 0 ||
                        index + 1 === searchResults.length) && (
                        <div
                          className="w-full"
                          style={{
                            display:
                              selectedIndex !== null &&
                              Math.ceil((index + 1) / 5) ===
                                Math.ceil((selectedIndex + 1) / 5)
                                ? "block"
                                : "none",
                          }}
                          key={country.countryCode + index}
                        >
                          {isOpen && returnPlans("search")}
                        </div>
                      )}
                </>
              );
            })}
          </div>
        </div>
      ) : searchQuery && searchResults.length === 0 ? (
        <NoSearchResults query={searchQuery} />
      ) : ((view === "countries") || (view === 'الدول')) ? (
          <div className={"flex " + styles.contianer}>
            <div className="flex justify-content-between items-baseline">
              <h4>{t("home.topDest")}</h4>
              <Link to={"/plans/top-destinations"}>
                <div className={styles.seeAllLink}>
                  {t("general.seeAll")} { i18n.language === 'ar' ?  <ArrowBack /> : <ArrowNext /> }
                </div>
              </Link>
            </div>
            <div className={"flex justify-content-between " + styles.countryContainer}>
              {topDest.map((country: any, index: number) => {
                if (index < 5) {
                  return (
                    <div onClick={() => handleSelect(country.countryCode, 'top', null) }
                        key={index}
                        className={
                          (selectedCountry === country.countryCode ? styles.open : "") + " " + styles.countryBox }>
                          <div
                            className={styles.flag}
                            style={{ backgroundImage: `url(${country.iconUrl})` }}
                          />
                          <p>{country.countryName}</p>
                          <CaretDown />
                        </div>
                      );
                    }
                  })}
            </div>
            {isOpen && selectedZone === 'top' && returnPlans()}
              <div className="flex justify-content-between items-baseline">
                <h4>{t("general.allCountries")}</h4>
              </div>
              <div className={"flex justify-content-between " + styles.countryContainer}>
              {countries.map((country: any, index: number) => {
                if (index < 10) {
                  return (
                    <>
                    <div onClick={() => handleSelect(country.countryCode, 'all', index) }
                        key={index}
                        className={
                          (selectedCountry === country.countryCode ? styles.open : "") + " " + styles.countryBox }>
                          <div
                            className={styles.flag}
                            style={{ backgroundImage: `url(${country.iconUrl})` }}
                          />
                          <p>{country.countryName}</p>
                        <CaretDown />
                      </div>
                      { under768 ? <></>
                      : index === 4 ? (
                            <div
                            className={styles.rowOne}
                            key={country.countryCode + index}>
                            {isOpen && selectedIndex !== null && (selectedIndex === 0 || selectedIndex < 5) && selectedZone === 'all' && returnPlans()}
                        </div>
                        )
                      : index === 9 ? (
                            <div
                            className={styles.rowOne}
                            key={country.countryCode + index}>
                            {isOpen && selectedIndex && selectedIndex < 10 && selectedIndex > 4 && selectedZone === 'all' && returnPlans()}
                        </div>
                        )
                      : '' }
                    </>
                      );
                    }
                  })}
            </div>
            { under768 && selectedCountry && selectedZone === 'all' && (
                        <>{isOpen && returnPlans()}</>
            )}
              <div className={"flex justify-content-center " + styles.select_all}>
                <Button>
                  <Link to="/plans/countries">{t("general.seeAll")}</Link>
                </Button>
              </div>
            </div>
      ) : (
        <div className={styles.contianer}>
          <div
            className={
              "flex justify-content-start flex-wrap " + styles.regionContainer
            }
          >
            {customZones.map((zone: any, index: number) => {
              return (
                <>
                  <div
                    key={index}
                    onClick={() =>
                      navigate(`/plans/region/${zone.zoneId}/select-plan`)
                    }
                    className={styles.regionBox}
                    style={{ backgroundImage: `url(${zone.zoneImage})` }}
                  >
                    <h2>{zone.zoneName}</h2>
                    <p>
                      {
                        i18n.language === 'en' ? (
                          <p>
                            {zone.countries.length}{" "}
                            {zone.countries.length === 1
                              ? t("home.desCoveredOne")
                              : t("home.desCovered")}
                          </p>
                        ) : (
                          <p>
                            {zone.countries.length === 1
                              ? t("home.desCoveredOne")
                              : t("home.desCovered")}
                            {" "}{zone.countries.length}
                        </p>
                        )
                      }

                    </p>
                  </div>
                </>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
