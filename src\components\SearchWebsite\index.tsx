import Button from "../Button";
import { MagnifyingGlass, Clear } from "../svgs";
import styles from "./search-website.module.scss";
import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import $ from "jquery";
import { Fade } from "@mui/material";
import { Link, useLocation } from "react-router-dom";
import {
  BlogSearchResult,
  CountrySearchResult,
  FaqSearchResult,
  PageSearchResult,
} from "../SearchResult";
import { useSelector } from "react-redux";
import lunr from "lunr";
import Shimmer from "../Shimmer";
import { pageIndex } from "../utils/pageIndex";
import { t } from "i18next";

const SearchWebsite = ({ show, setShow }: any) => {
  const { faqs, blogs, plans } = useSelector((state: any) => state);

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (/*faqs.length !== 0 && */ blogs.length !== 0 && "comboPlans" in plans) {
      setLoading(false);
    }
  }, [faqs, blogs, plans]);

  const location = useLocation();

  const searchInput: any = useRef(null);

  const [isFocused, setIsFocused] = useState(false);

  const [searchQuery, setSearchQuery] = useState("");

  const [searchQueryDisplay, setSearchQueryDisplay] = useState("");

  const handleSearchChange = (e: any) => {
    setSearchQuery(e.target.value);
  };

  const clearSearch = () => {
    setSearchQuery("");
  };

  useEffect(() => {
    $("#search-input").on("focus", () => {
      setIsFocused(true);
    });
    $("#search-input").on("blur", () => {
      setIsFocused(false);
    });
  }, []);

  useEffect(() => {
    if (show) {
      searchInput.current!.focus();
      $(document.body).css("overflow-y", "hidden");
    } else {
      searchInput.current!.blur();
      $(document.body).css("overflow-y", "scroll");
    }
  }, [show]);

  const [activeSection, setActiveSection] = useState("All");

  const resultSections = ["All", "Blogs", "Shop", "Help"];

  const [blogResults, setBlogResults] = useState([]);
  const [planResults, setPlanResults] = useState([]);
  const [faqResults, setFaqResults] = useState([]);
  const [pageResults, setPageResults] = useState([]);
  const [allResults, setAllResults] = useState([]);

  const [showTopSearches, setShowTopSearches] = useState(true);

  const reset = () => {
    setBlogResults([]);
    setPlanResults([]);
    setFaqResults([]);
    setPageResults([]);
    setAllResults([]);
    setActiveSection("All");
    setShowTopSearches(true);
    setSearchQuery("");
    setSearchQueryDisplay("");
    setShow(false);
  };

  useEffect(reset, [location]);

  const sortByScore = (a: any, b: any) => {
    return b.score - a.score;
  };

  const removeHtml = (str: any) => {
    return str.replace(/<\/?[^>]+(>|$)/g, "");
  };

  const handleSearch = () => {
    if (searchQuery.length === 0) return;
    // Search pages for search query
    const pageSearch = lunr(function () {
      (this as any).ref("link");
      (this as any).field("name");
      (this as any).field("keywords");
      pageIndex.forEach((pageItem: any) => {
        (this as any).add(pageItem);
      });
    });

    const pageSearchResults = pageSearch.search(searchQuery);
    let pageResultArray = [] as any;
    pageSearchResults.forEach((result: any) => {
      const pageToAdd: any = pageIndex.find(
        (page: any) => page.link == result.ref
      )!;
      pageToAdd.score = result.score;
      pageToAdd.type = "page";
      pageResultArray.push(pageToAdd);
    });
    setPageResults(pageResultArray);

    // Search blogs for search query
    const blogSearch = lunr(function () {
      (this as any).ref("ID");
      (this as any).field("title");
      (this as any).field("content");
      blogs.forEach((blogItem: any) => {
        (this as any).add(blogItem);
      });
    });

    const blogSearchResults = blogSearch.search(searchQuery);
    let blogResultArray = [] as any;
    blogSearchResults.forEach((result: any) => {
      const blogToAdd = blogs.find((blog: any) => blog.ID == result.ref);
      blogToAdd.score = result.score;
      blogToAdd.type = "blog";
      blogResultArray.push(blogToAdd);
    });
    setBlogResults(blogResultArray);

    const allPlans = [
      ...plans.comboPlans,
      ...plans.creditPlans,
      ...plans.esimPlans,
      ...plans.didPlans,
    ];

    // Search plans for search query
    const planSearch = lunr(function () {
      (this as any).ref("planId");
      (this as any).field("planName");
      plans.comboPlans.forEach((planItem: any) => {
        let planToAdd = planItem;
        planToAdd.displayType = "Combo";
        planToAdd.shopLink = "combo";
        (this as any).add(planToAdd);
      });
      plans.esimPlans.forEach((planItem: any) => {
        let planToAdd = planItem;
        planToAdd.displayType = "Data";
        planToAdd.shopLink = "data";
        (this as any).add(planToAdd);
      });
      plans.creditPlans.forEach((planItem: any) => {
        let planToAdd = planItem;
        planToAdd.displayType = "Global Credit";
        planToAdd.shopLink = "credit";
        (this as any).add(planToAdd);
      });
      plans.didPlans.forEach((planItem: any) => {
        let planToAdd = planItem;
        planToAdd.displayType = "Phone Number";
        planToAdd.shopLink = "phone";
        (this as any).add(planToAdd);
      });
    });

    const planSearchResults = planSearch.search(searchQuery);
    let planResultArray = [] as any;
    planSearchResults.forEach((result: any) => {
      const planToAdd = allPlans.find((plan: any) => plan.planId == result.ref);
      planToAdd.score = result.score;
      planToAdd.type = "plan";
      planResultArray.push(planToAdd);
    });
    setPlanResults(planResultArray);

    // Search FAQs for search query
    const faqSearch = lunr(function () {
      (this as any).ref("id");
      (this as any).field("answer");
      (this as any).field("question");
      this.metadataWhitelist = ["position"];
      faqs.forEach((faqItem: any) => {
        (this as any).add(faqItem);
      });
    });

    const faqSearchResults = faqSearch.search(searchQuery);
    let faqResultArray = [] as any;
    faqSearchResults.forEach((result: any) => {
      const faqToAdd = faqs.find((faq: any) => faq.id == result.ref);
      faqToAdd.score = result.score;
      faqToAdd.type = "help";

      const termsFound = Object.keys(result.matchData.metadata);
      let answerString = [] as any;
      termsFound.forEach((term: any) => {
        let re = new RegExp(term, "ig");

        // Remove HTML tags from string
        let longString = removeHtml(faqToAdd.answer);

        // Split string into sentences
        const sentenceArray = longString
          .replace(/([.?!])\s*(?=[A-Z])/g, "$1|")
          .split("|");

        // Filter sentences to ones containing search query
        const filteredSentenceArray = sentenceArray.filter((sentence: string) =>
          sentence.toLowerCase().includes(term.toLowerCase())
        );

        filteredSentenceArray.forEach((sentence: string) => {
          if (answerString.some((item: any) => removeHtml(item) === sentence)) {
            let index = answerString.findIndex(
              (item: any) => removeHtml(item) === sentence
            );
            answerString[index] = answerString[index].replaceAll(
              re,
              `<b>${term}</b>`
            );
          } else {
            answerString.push(sentence.replaceAll(re, `<b>${term}</b>`));
          }
        });
      });

      faqToAdd.strings = answerString;

      faqResultArray.push(faqToAdd);
    });
    setFaqResults(faqResultArray);

    // Combine and sort by score for "All" section
    const allUnorderedResults = [
      ...pageResultArray,
      ...blogResultArray,
      ...planResultArray,
      ...faqResultArray,
    ] as any;
    setAllResults(allUnorderedResults.sort(sortByScore));

    setSearchQueryDisplay(searchQuery);
    setShowTopSearches(false);
    setActiveSection("All");
  };

  const getNumberOfResults = () => {
    switch (activeSection) {
      case "All":
        return allResults.length;
      case "Blogs":
        return blogResults.length;
      case "Shop":
        return planResults.length;
      case "Help":
        return faqResults.length;
    }
  };

  return (
    <div
      className={`${styles.container} ${show && styles.showContainer}`}
      id="search-scroll"
    >
      <div
        style={{
          borderBottom: isFocused
            ? "3px solid #7448B0"
            : "3px solid transparent",
          opacity: loading ? 0.5 : 1,
        }}
        className={styles.topSection}
      >
        <div className={styles.inputContainer}>
          <div style={{ width: 24, height: 24 }}>
            {show && (
              <motion.div
                style={{ width: 24, height: 24 }}
                transition={{
                  type: "spring",
                  mass: 1,
                  stiffness: 80,
                  damping: 20,
                }}
                layoutId="mag-glass"
              >
                <MagnifyingGlass />
              </motion.div>
            )}
          </div>
          <form style={{ width: "100%", height: "100%" }} autoComplete="off">
            <input
              id="search-input"
              ref={searchInput}
              placeholder="Start typing"
              value={searchQuery}
              onChange={(e) => handleSearchChange(e)}
              onKeyDown={(event) => {
                if (event.key === "Enter") {
                  event.preventDefault();
                  handleSearch();
                }
              }}
              disabled={loading}
            />
          </form>
          <Fade in={searchQuery.length ? true : false}>
            <button onClick={clearSearch} className={styles.clearButton}>
              <Clear />
            </button>
          </Fade>
        </div>
        <div className={styles.buttons}>
          <Button style={{ marginRight: 8 }} color="tertiary" onClick={reset}>
          { t('buttons.cancel') }
          </Button>
          <Button disabled={loading} onClick={handleSearch} color="primary">
            Search
          </Button>
        </div>
      </div>
      <div className={styles.mobileButtons}>
        <Button style={{ marginRight: 8 }} color="tertiary" onClick={reset}>
          Cancel
        </Button>
        <Button disabled={loading} onClick={handleSearch} color="primary">
          Search
        </Button>
      </div>
      <div className={styles.resultsContainer}>
        {showTopSearches ? (
          <div className={styles.topSearches}>
            {loading ? (
              <>
                <div
                  className={styles.loadingBox}
                  style={{
                    width: 150,
                    height: 12,
                    margin: "12px 0px 33px 0px",
                    borderRadius: 2,
                  }}
                >
                  <Shimmer />
                </div>
                <div className={styles.loadingBox}>
                  <Shimmer />
                </div>
                <div className={styles.loadingBox}>
                  <Shimmer />
                </div>
                <div className={styles.loadingBox}>
                  <Shimmer />
                </div>
                <div className={styles.loadingBox}>
                  <Shimmer />
                </div>
              </>
            ) : (
              <>
                <h3>Top Searches</h3>
                <Link to="/esim-compatible-devices">
                  eSIM compatible phones list
                </Link>
              </>
            )}
          </div>
        ) : (
          <div className={styles.results}>
            <div className={styles.sections}>
              {resultSections.map((section) => (
                <div
                  className={`${styles.sectionButton} ${
                    activeSection === section && styles.active
                  }`}
                  onClick={() => {
                    setActiveSection(section);
                  }}
                  key={`search-section-${section}`}
                >
                  <span>{section}</span>
                  {activeSection === section && (
                    <motion.div
                      layoutId="results-highlight"
                      className={styles.highlight}
                    />
                  )}
                </div>
              ))}
            </div>
            <div className={styles.numberOfResults}>
              {getNumberOfResults()} results for "{searchQueryDisplay}" in{" "}
              {activeSection} categor
              {activeSection === "All" ? "ies" : "y"}
            </div>
            {activeSection === "All" ? (
              allResults.length ? (
                allResults.map((allResult: any) => {
                  if (allResult.type === "blog") {
                    return (
                      <BlogSearchResult
                        key={`blog-result-${allResult.ID}`}
                        data={allResult}
                      />
                    );
                  } else if (allResult.type === "plan") {
                    return (
                      <CountrySearchResult
                        key={`plan-result-${allResult.planId}`}
                        data={allResult}
                      />
                    );
                  } else if (allResult.type === "help") {
                    return (
                      <FaqSearchResult
                        key={`faq-result-${allResult.id}`}
                        data={allResult}
                      />
                    );
                  } else if (allResult.type === "page") {
                    return (
                      <PageSearchResult
                        key={`page-result-${allResult.name}`}
                        data={allResult}
                      />
                    );
                  }
                })
              ) : (
                <div className={styles.noResults}>
                  <img
                    className={styles.noResultImage}
                    src="/search/no_search_results.svg"
                  />
                  <div className={styles.suggestions}>
                    <div className={styles.text}>Suggestions:</div>
                    <ul>
                      <li>Double check the spelling of the words</li>
                      <li>Change or adjust the keywords</li>
                      <li>Try more general keywords</li>
                    </ul>
                  </div>
                </div>
              )
            ) : activeSection === "Blogs" ? (
              blogResults.length ? (
                blogResults.map((blogResult: any) => (
                  <BlogSearchResult
                    key={`blog-result-${blogResult.id}`}
                    data={blogResult}
                  />
                ))
              ) : (
                <img
                  className={styles.noResultImage}
                  src="/search/no_search_results.svg"
                />
              )
            ) : activeSection === "Shop" ? (
              planResults.length ? (
                planResults.map((planResult: any) => (
                  <CountrySearchResult
                    key={`plan-result-${planResult.planId}`}
                    data={planResult}
                  />
                ))
              ) : (
                <img
                  className={styles.noResultImage}
                  src="/search/no_search_results.svg"
                />
              )
            ) : activeSection === "Help" ? (
              faqResults.length ? (
                faqResults.map((faqResult: any) => (
                  <FaqSearchResult
                    key={`faq-result-${faqResult.id}`}
                    data={faqResult}
                  />
                ))
              ) : (
                <img
                  className={styles.noResultImage}
                  src="/search/no_search_results.svg"
                />
              )
            ) : (
              ""
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchWebsite;
