@use "./theme.scss" as *;

.blogHeaderContainer {
  background-image: url("/images-int/about-us/storyHeroImage.jpg");
  background-size: cover;
  background-position: center;
}

.blogHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 150px;
  margin-bottom: 40px;
  @media (max-width: 768px) {
    flex-direction: column;
    padding: 0px 70px
  }
  @media (max-width: 425px) {
    padding: 0px 30px
  }
  h1 {
    font-size: 64px;
    margin-bottom: 0;
    @media (max-width: 425px) {
      font-size: 48px
    }
  }
  p {
    font-size: 18px;
    line-height: 30px;
    font-weight: 400;
    @media (max-width: 425px) {
      font-size: 15px
    }
  }
  div,
  img {
    position: relative;
  }
  img {
    bottom: -55px;
    width: 500px;
    @media (max-width: 768px) {
      bottom: -20px;
      width: 320px
    }
    @media (max-width: 425px) {
      bottom: 10px;
      width: 230px
    }
  }
}
.blogs {
  padding: 40px 150px;
  @media (max-width: 768px) {
    padding: 40px 20px;
  }
  .titleBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    @media (max-width: 425px) {
      flex-direction: column;
    }
    > div {
      width: 50%;
      justify-content: end;
      @media (max-width: 425px) {
        width: 100%
      }
      > div {
        margin: 0;
      }
    }
  }
  .blogArticles {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      overflow: hidden;
    }
    @media (max-width: 425px) {
      grid-template-columns: repeat(1, 1fr);
      gap: 5px
    }
  }
}

.faqSection {
  padding: 60px 200px;
  text-align: center;
  @media (max-width: 425px) {
    padding: 60px 20px;
  }
  h1 {
    font-size: 48px;
    font-weight: 700;
    color: $secondary;
    @media (max-width: 768px) {
      font-size: 40px;
    }
    @media (max-width: 425px) {
      font-size: 30px;
    }
  }
  p {
    color: $secondary;
    font-size: 18px;
    font-weight: 400;
    width: 50%;
    margin: 10px auto 50px;
    @media (max-width: 768px) {
      width: 72%;
    }
    @media (max-width: 425px) {
      width: 85%;
      font-size: 15px;
    }
  }
  a {
    display: inline-block;
    text-decoration: none;
    margin-top: 25px;
  }
  button {
    margin: auto;
    padding: 0 30px;
  }
}

.blogsContainer {
  padding: 24px 50px 80px 50px;
  max-width: 1600px;
  margin: 0 auto;
  @media (max-width: 768px) {
    padding: 24px 24px 100px 24px;
  }
}

.featuredBlog {
  width: 100%;
  height: 460px;
  border-radius: 12px;
  margin-bottom: 80px;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  @media (max-width: 768px) {
    height: auto;
  }
  .featuredBackground {
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      rgba(22, 11, 42, 0.6) 0%,
      rgba(22, 11, 42, 0.3) 50%,
      rgba(22, 11, 42, 0.6) 100%
    );
    border-radius: 12px;
    padding: 50px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    color: #fff;
    @media (max-width: 768px) {
      padding: 150px 24px 50px 24px;
    }
    .heading {
      font-weight: 700;
      font-size: 40px;
      line-height: 60px;
      margin: 0 0 8px 0;
      width: 100%;
      max-width: 750px;
      @media (max-width: 768px) {
        font-size: 30px;
        line-height: 45px;
      }
    }
    p {
      font-size: 20px;
      line-height: 30px;
      margin: 0;
      width: 100%;
      max-width: 658px;
      @media (max-width: 768px) {
        font-size: 16px;
        line-height: 24px;
      }
    }
  }
}

.paginationContainer {
  width: 100%;
  display: flex;
  justify-content: center;
}

.blogScrollMarker {
  position: relative;
  top: -150px;
  @media (max-width: 768px) {
    top: -100px;
  }
}
