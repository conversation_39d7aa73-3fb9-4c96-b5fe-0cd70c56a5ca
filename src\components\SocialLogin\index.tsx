import { useEffect } from "react";
import styles from "./social-login.module.scss";
import jwtDecode from "jwt-decode";
import auth from "../../components/utils/firebaseAuth";
import { GoogleAuthProvider, signInWithCredential } from "firebase/auth";
import { t } from "i18next";
import { useTranslation } from "react-i18next";
import { v4 as uuid } from "uuid";

const SocialLogin = ({
  register,
  handleGoogleNext,
  handleFacebookNext,
  handleAppleNext,
}: any) => {
  const { i18n } = useTranslation();
  useEffect(() => {
    (window as any).AppleID.auth.init({
      clientId: "com.orbitmobile.orbit",
      scope: "name email",
      redirectURI:
        "https://orbit-web.mobilisedev.co.uk/finish-registration-social",
      state: uuid(),
      usePopup: true,
    });
    try {
      (window as any).google.accounts.id.initialize({
        use_fedcm_for_prompt: false,
        client_id:
          "************-cvnpsp60vc35plo5ior5jkfbkjl936ia.apps.googleusercontent.com",
        callback: async (res: any) => {
          const token = res.credential;
          let decoded = null as any;
          try {
            decoded = jwtDecode(token);
          } catch (e) {}
          if (token && decoded) {
            if (register) {
              handleGoogleNext({
                googleToken: token,
                firstName: decoded.given_name,
                lastName: decoded.family_name,
                tacAccepted: true,
              });
            } else {
              try {
                const credential = GoogleAuthProvider.credential(token);
                const result = await signInWithCredential(auth, credential);
                const user = result?.user;
              } catch (error) {
                console.error("Error signing in with Google: ", error);
              }
              handleGoogleNext(token);
            }
          }
        },
      });
      (window as any).google.accounts.id.renderButton(
        document.getElementById("buttonDiv"),
        {
          theme: "outline",
          size: "large",
          width: 327,
          locale: i18n.language,
          text: "continue_with",
          shape: "pill",
          logo_alignment: "center",
        }
      );
      (window as any).google.accounts.id.prompt();
    } catch (e) {
      console.log(e);
    }
  }, [i18n, i18n.language]);

  const AppleResponse = async () => {
    try {
      const authorizationResponse = await (window as any).AppleID.auth.signIn();
      console.log(authorizationResponse)
      let decoded = null as any;
      if (register) {
        handleAppleNext({
          token: authorizationResponse.authorization.id_token,
          email: authorizationResponse.user.email,
          firstName: authorizationResponse.user.name.firstName,
          lastName: authorizationResponse.user.name.lastName,
          tacAccepted: true
        })
      } else {
        try {
          decoded = jwtDecode(authorizationResponse.authorization.id_token);
        } catch (e) {}
        handleAppleNext({
          email: decoded.email,
          token: authorizationResponse.authorization.id_token,
        });
      }
    } catch (error) {
      console.error("Error signing in with Apple:", error);
    }
  };

  const facebookResponse = () => {
    (window as any).FB.login(
      function (response: any) {
        const authResponse = response.authResponse;
        if (register) {
          (window as any).FB.api(
            "/me",
            { fields: "first_name,last_name" },
            function (res: any) {
              handleFacebookNext({
                fbUserId: authResponse.userID,
                fbAccessToken: authResponse.accessToken,
                firstName: res.first_name,
                lastName: res.last_name,
                tacAccepted: true,
              });
            }
          );
        } else {
          handleFacebookNext({
            userId: authResponse.userID,
            accessToken: authResponse.accessToken,
          });
        }
      },
      { scope: "email" }
    );
  };

  return (
    <>
      <div className={styles.divider}>
        <p>{t("account.continueSocial")}</p>
      </div>
      <div className={styles.buttonsContainer}>
        <div id="buttonDiv" />
        <button
            className={`${styles.appleButton} ${styles.socialButton}`}
            onClick={AppleResponse}>
            <img src="/images-int/social/apple.svg" />
            { t('account.continueApple') }
        </button>
        <button
          className={`${styles.fbButton} ${styles.socialButton}`}
          onClick={facebookResponse}
        >
          <img src="/images-int/social/facebook.svg" />
          {t("account.continueFaceBook")}
        </button>
      </div>
    </>
  );
};

export default SocialLogin;
