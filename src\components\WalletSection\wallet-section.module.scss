@use "../../styles/theme.scss" as *;

.topBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  @media (max-width: 850px) {
    button {
      width: 100%;
      max-width: 326px;
      margin: 0 auto;
    }
  }
  h3 {
    color: $secondary;
    margin: 0;
    font-weight: 800;
    font-size: 26px;
    line-height: 32px;
    @media (max-width: 850px) {
      display: none;
    }
  }
}

.cardsContainer {
  display: flex;
  flex-wrap: wrap;
  justify-items: center;
  grid-row-gap: 20px;
  grid-column-gap: 20px;
  @media (max-width: 1350px) {
    grid-template-columns: 1fr 1fr;
  }
  @media (max-width: 850px) {
    grid-template-columns: 1fr;
    padding: 24px 0;
  }
}

.noCards {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-bottom: 150px;
  color: $dark-dark-purple;
  grid-column: 1 / -1;
  text-align: center;
  @media (max-width: 850px) {
    padding-top: 0px;
    padding-bottom: 50px;
  }
  h5 {
    font-weight: 800;
    font-size: 20px;
    line-height: 28px;
    margin: 20px 0 6px 0;
  }
  p {
    font-size: 14px;
    line-height: 20px;
    margin: 0 0 32px 0;
    max-width: 511px;
  }
}
