import { CaretDown } from "../../components/svgs";
import { useEffect, useState } from "react";
import { ApiGet } from "../api/api";
import styles from "../../styles/allCountires.module.scss";
import plansStyles from "../../styles/region-plans.module.scss";
import PlanTile from "../../components/PlanTile";
import PlanTileSkeleton from "../../components/PlanTileSkeleton";
import { t } from "i18next";
import { Helmet } from "react-helmet-async";
import { useMediaQuery } from "@mui/material";
import { useTranslation } from "react-i18next";
import { productSchema, countriesServedMarkUp } from "../../components/utils/schemaMarkups";

const TopDestPlans = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [plans, setPlans] = useState([]);
  const { i18n } = useTranslation();
  const [selectedCountry, setSelectedCountry] = useState("");
  const [plansLoading, setPlansLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(null);
  const [topDest, setTopDest] = useState([])

  const under768 = useMediaQuery("(max-width: 768px)");

  useEffect(() => {
    ApiGet('/plans/top/countries', null, null, i18n.language).then((response: any) => {
      setTopDest(response.data.countries)
    })
  }, [i18n, i18n.language])

  const handleSelect = (code: any, index: any) => {
    if (code === selectedCountry) {
      setIsOpen(false);
      setSelectedCountry("");
      setSelectedIndex(null);
    } else {
      setPlansLoading(true);
      setSelectedCountry(code);
      setSelectedIndex(index);
      setIsOpen(true);
      ApiGet(`/plans?countryCode=${code}`).then((response: any) => {
        if (response) {
          setPlansLoading(false);
          setPlans(response.data.esimPlans);
        }
      });
    }
  };

  const returnPlans = () => {
    return (
      <div className={plansStyles.selectedCountry}>
        {plansLoading ? (
          Array.from({ length: 5 }).map((x, i) => (
            <PlanTileSkeleton index={i} />
          ))
        ) : plans.length > 0 ? (
          plans.map((plan: any, index) => {
            return <PlanTile plan={plan} index={index} />;
          })
        ) : (
          <p>No Plans Found</p>
        )}
      </div>
    );
  };

  return (
    <>
      <Helmet>
        <title>
          {t("general.orbit")}| {t("plan.discoverPlan")}
          </title>
          <script type="application/ld+json">
            {plans.length > 0 ? JSON.stringify(productSchema(plans ,'plans/countries')) :
              JSON.stringify(countriesServedMarkUp(topDest, 'all countries' ,'plans/countries'))}
          </script>
          <meta name="description" content={t("plan.ifTravelCovered")} />
      </Helmet>
      <div className={styles.plansView}>
        <h2
          dangerouslySetInnerHTML={{ __html: t("slogans.stayConnected") }}
        ></h2>
        <p>{ t('home.topDest') }</p>
        <p></p>
      </div>
      <div className={"flex " + plansStyles.contianer}>
        <div
          className={
            "flex justify-content-center flex-wrap " +
            plansStyles.countryContainer
          }
        >
          {
            topDest.map((country: any, index: number) => {
              return (
                <>
                  <div
                    key={country.countryCode}
                    onClick={() => handleSelect(country.countryCode, index)}
                    className={
                      (selectedCountry === country.countryCode
                        ? plansStyles.open
                        : "") +
                      " " +
                      plansStyles.countryBox +
                      " " +
                      plansStyles.countryBoxRegion
                    }
                  >
                    <div
                      className={plansStyles.flag}
                      style={{ backgroundImage: `url(${country.iconUrl})` }}
                    />
                    <p>{country.countryName}</p>
                    <CaretDown />
                  </div>
                  {under768
                    ? selectedCountry === country.countryCode && (
                        <>{isOpen && returnPlans()}</>
                      )
                    : ((index + 1) % 5 === 0 ||
                        index + 1 === topDest.length) && (
                        <div
                          className="w-full"
                          style={{
                            display:
                              selectedIndex !== null &&
                              Math.ceil((index + 1) / 5) ===
                                Math.ceil((selectedIndex + 1) / 5)
                                ? "block"
                                : "none",
                          }}
                          key={country.countryCode + index}
                        >
                          {isOpen && returnPlans()}
                        </div>
                      )}
                </>
              );
            })
         }
        </div>
      </div>
    </>
  );
};

export default TopDestPlans;
