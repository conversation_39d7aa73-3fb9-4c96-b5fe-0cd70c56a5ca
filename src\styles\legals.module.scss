@use "./theme.scss" as *;

.main {
  position: relative;
  display: flex;
}

.container {
  max-width: 873px;
  width: 100%;
  margin: 0 auto;
  padding: 76px 50px;
  @media (max-width: 768px) {
    padding: 76px 24px;
    max-width: initial;
    width: 100%;
  }
}

.heading {
  font-weight: 700;
  font-size: 40px;
  line-height: 60px;
  color: $dark-dark-purple;
  margin: 0 0 50px 0;
  @media (max-width: 768px) {
    font-size: 30px;
    line-height: 45px;
  }
}

.text {
  width: 100%;
  font-size: 16px;
  line-height: 24px;
  color: $dark-dark-purple;
  overflow-wrap: break-word;
  @media (max-width: 768px) {
    font-size: 16px;
    line-height: 24px;
  }
}

.backButton {
  padding: 0;
  background: none;
  border: none;
  cursor: pointer;
  position: absolute;
  top: 27px;
  left: 55px;
  color: $dark-dark-purple;
  @media (max-width: 768px) {
    top: 24px;
    left: 24px;
  }
  svg {
    vertical-align: middle;
  }
}
