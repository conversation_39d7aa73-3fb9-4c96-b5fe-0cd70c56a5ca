import { useEffect, useState } from "react";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
} from "../../components/utils/InputHandlers";
import { Helmet } from "react-helmet-async";
import styles from "../../styles/register.module.scss";
import { Input } from "../../components/Input";
import PhoneInput from "../../components/PhoneInput";
import Button from "../../components/Button";
import { validateAll } from "indicative/validator";
import { useSearchParams } from "react-router-dom";
import { ApiPost } from "../api/api";
import RegSuccessModal from "../../components/RegSuccessModal";
import { updatePassword } from "firebase/auth";
import auth from "../../components/utils/firebaseAuth";
import useHandleRegistrationSuccess from "../../hooks/useHandleRegistrationSuccess";
import { t } from "i18next";
import { useDispatch } from "react-redux";

const fields = [
  "firstName",
  "lastName",
  "countryCode",
  "phoneNumber",
  "regPassword",
  "confirmRegPassword",
];
const rules = getRules(fields);
const messages = getMessages(fields);

const FinishRegistration = () => {
  const [data, setData] = useState(createStateObject(fields));
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState(null as any);
  const [searchParams, setSearchParams] = useSearchParams();
  const [successModal, setSuccessModal] = useState(false);

  const { handleRegistrationSuccess } = useHandleRegistrationSuccess();

  const dispatch = useDispatch();

  useEffect(() => {
    const emailQuery = searchParams.get("email");
    if (emailQuery) {
      setEmail(emailQuery);
    }
  }, []);

  const handleResetPassword = async () => {
    setLoading(true);
    try {
      await updatePassword(auth.currentUser, data.regPassword);
    } catch (err) {
      console.log(err);
      setLoading(false);
    }
  };

  const submitReg = () => {
    validateAll(data, rules, messages)
      .then(async () => {
        setLoading(true);
        let token: string = searchParams.get("accessToken") || "";
        if ((data.phoneNumber && !data.countryCode) || (!data.phoneNumber && data.countryCode)) {
            displayErrors(
              [
                {
                  message: !data.countryCode ? "Enter Country code" : "Enter Phone number",
                  field: !data.countryCode ? "countryCode" : "phoneNumber",
                  validation: !data.countryCode ? "countryCode" : "phoneNumber",
                },
              ],
              setData
            );
            setLoading(false)
          return
        }
        ApiPost("/users/register", {
          emailId: email,
          phoneNumber: data.countryCode + data.phoneNumber,
          password: data.regPassword,
          firstName: data.firstName,
          lastName: data.lastName,
          tacAccepted: true,
          otpToken: token,
        })
          .then((response) => {
            successRegister(response);
            localStorage.removeItem("accessToken");
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                heading: "Something went wrong!",
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const successRegister = (response: any) => {
    handleRegistrationSuccess(response);
    handleResetPassword();
    setSuccessModal(true);
  };

  return (
    <div className={styles.finishReg}>
      <Helmet>
        <title>
          {t("general.orbit")}| {t("account.verifyMsg")}
        </title>
        <meta name="description" content={t("account.verifyMsg")} />
      </Helmet>
      <RegSuccessModal show={successModal} />
      <img
        className={styles.image}
        src="/images-int/help/help_activating.svg"
      />
      <h2>{t("account.mailVerified")}</h2>
      <p className={styles.text}>{t("account.verifyMsg")}</p>
      <div className={styles.inputs}>
        {fields.map((prop: any) => {
          if (prop === "phoneNumber") {
            return (
              <PhoneInput
                state={data}
                setState={setData}
                onKeyDown={submitReg}
                disabled={loading}
              />
            );
          } else if (prop !== "countryCode") {
            return (
              <Input
                label={t("placeholders." + prop)}
                value={data[prop]}
                onChange={(e: any) => {
                  handleInputChange(prop, e, data, setData);
                }}
                error={data.errors[prop]}
                clear={() => {
                  clearInput(prop, data);
                }}
                onKeyDown={submitReg}
                key={`input-${prop}`}
                password={
                  prop === "regPassword" || prop === "confirmRegPassword"
                }
                disabled={loading}
              />
            );
          }
        })}
        <Button style={{ width: "100%", height: 48 }} onClick={submitReg}>
          {t("buttons.allDone")}
        </Button>
      </div>
    </div>
  );
};

export default FinishRegistration;
