export const androidSteps = [
  {
    stepNumber: "1",
    title: "Access Preferred SIM Settings:",
    content: (
      <ul>
        <li>Open "Settings."</li>
        <li>Tap on "Connections."</li>
        <li>Choose "SIM Card Manager."</li>
      </ul>
    ),
  },
  {
    stepNumber: "2",
    title: "Set Preferred SIM for Data:",
    content: (
      <ul>
        <li>Under "Preferred SIM card," select "Mobile data."</li>
        <li>Set your preferred SIM to 'Gist.'</li>
      </ul>
    ),
  },
  {
    stepNumber: "3",
    title: "Activate eSIM:",
    content: (
      <ul>
        <li>Find the 'eSIMs' section; usually labeled 'eSIM 1.'</li>
        <li>Make sure to switch ON the eSIM for data usage.</li>
      </ul>
    ),
  },
  {
    stepNumber: "4",
    title: "Prevent Roaming Charges:",
    content: (
      <ul>
        <li>
          In SIM Card Manager, turn OFF "Auto data switching" to avoid roaming
          charges from your home network.
        </li>
      </ul>
    ),
  },
  {
    stepNumber: "5",
    title: "Enable Data Roaming (For Roaming Service Activation):",
    content: (
      <ul>
        <li>Return to "Settings."</li>
        <li>Tap on "Connections."</li>
        <li>Open "Mobile networks."</li>
        <li>Turn ON data roaming.</li>
      </ul>
    ),
  },
];

export const androidImages = {
  "1": "/esim-devices/android_step_1.png",
  "2": "/esim-devices/android_step_2.png",
  "3": "/esim-devices/android_step_3.png",
  "4": "/esim-devices/android_step_4.png",
  "5": "/esim-devices/android_step_5.png",
} as any;

export const iosImages = {
  "1": "/esim-devices/ios_step_1.png",
  "2": "/esim-devices/ios_step_2.png",
  "3": "/esim-devices/ios_step_3.png",
  "4": "/esim-devices/ios_step_4.png",
  "5": "/esim-devices/ios_step_5.png",
} as any;

export const iosSteps = [
  {
    stepNumber: "1",
    title: "Access Cellular Settings:",
    content: (
      <ul>
        <li>Open the "Settings" app on your iOS device.</li>
        <li>
          Tap on "Cellular" or "Mobile Data," depending on your device's
          settings.
        </li>
      </ul>
    ),
  },
  {
    stepNumber: "2",
    title: "Scan QR Code:",
    content: (
      <ul>
        <li>Select "Add Cellular Plan."</li>
        <li>Choose the option to "Scan QR Code."</li>
        <li>Follow the onscreen instructions.</li>
      </ul>
    ),
  },
  {
    stepNumber: "3",
    title: "Set Preferred Data SIM:",
    content: (
      <ul>
        <li>Go back to "Cellular" settings.</li>
        <li>
          Under "Cellular Plans," make sure the 'Gist' eSIM is selected as the
          preferred data SIM.
        </li>
      </ul>
    ),
  },
  {
    stepNumber: "4",
    title: "Enable Data Roaming (For Roaming Service Activation)",
    content: (
      <ul>
        <li>Return to the main "Settings" menu.</li>
        <li>Tap on "Cellular" or "Mobile Data."</li>
        <li>Turn ON "Data Roaming."</li>
      </ul>
    ),
  },
  {
    stepNumber: "5",
    title: "Avoid Roaming Charges:",
    content: (
      <ul>
        <li>
          To prevent data roaming charges from your home network, ensure "Auto
          Data Switching" is set to OFF, if applicable.
        </li>
      </ul>
    ),
  },
];
