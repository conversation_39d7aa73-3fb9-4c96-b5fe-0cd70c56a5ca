import HelpHeader from "../../components/helpHeader/HelpHeader";
import { useEffect, useState } from "react";
import { CmsApiGet } from "../../pages/api/cms-api";
import { useParams } from "react-router-dom";
import styles from "../../styles/help.module.scss";
import { Helmet } from "react-helmet-async";
import Button from "../../components/Button";
import { EnvelopeWhite } from "../../components/svgs";
import { Link } from "react-router-dom";
import { resolveRichText } from "../../components/utils/richTextConverter";
import Toggle from "../../components/Toggle";
import Faqs from "../../components/Faqs/Faqs";
import { CircularProgress } from "@mui/material";
import { useTranslation } from "react-i18next";
import { faqSchema } from "../../components/utils/schemaMarkups";

type view = "Joining" | "Activating" | "تسجيل" | "تفعيل";

type relatedQuestions = {
  attributes: {
    Question: String;
    Answer: String;
  };
};

type questionType = {
  Answer?: string;
  Question?: string;
  faqs_category?: any;
  faq?: {
    data: relatedQuestions[];
  };
  metaDescription: string;
};

const HelpPage = () => {
  const [question, setQuestion] = useState<questionType>({} as any);
  const { slug } = useParams();

  const { t, i18n } = useTranslation();

  const [topQuestionsTypes, setTopQuestionTypes] = useState([] as any);

  useEffect(() => {
    setQuestion({} as any);
    CmsApiGet(`/api/slugify/slugs/faq/${slug}?populate=deep`).then(
      (response: any) => {
        setQuestion(response.data.data.attributes);
      }
    );
  }, [slug]);

  useEffect(() => {
    CmsApiGet(`/api/top-faqs-types?locale=${i18n.language}&populate=deep`).then(
      (response: any) => {
        setView(response.data.data[0]?.attributes?.label);
        setTopQuestionTypes(response.data.data);
      }
    );
  }, [i18n, i18n.language]);

  const [faqs, setFaqs] = useState([]);
  const [view, setView] = useState<view>("Joining");

  const handleView = (view: any) => {
    setView(view);
  };

  useEffect(() => {
    if (i18n.language === "ar") {
      if (view === "Joining") {
        setView("تسجيل");
      } else {
        setView("تفعيل");
      }
    } else {
      if (view === "تسجيل") {
        setView("Joining");
      } else {
        setView("Activating");
      }
    }
  }, [i18n.language]);

  return (
    <div>
      {question && question.Question && (
        <Helmet>
          <title>
            {t("general.orbit")} | {question.Question}
          </title>
          <script type="application/ld+json">
            {JSON.stringify(faqSchema(question.Question, resolveRichText(question.Answer), slug))}
          </script>
          {question.metaDescription && (
            <meta name="description" content={question.metaDescription} />
          )}
        </Helmet>
      )}
      <HelpHeader />
      <div className={styles.helpInteralPage}>
        {question.Question ? (
          <div>
            <div className={styles.breadcrumb}>
              <Link to="/help">{t("help.help")}</Link> <Arrow />{" "}
              {question.faqs_category?.data?.attributes?.name} <Arrow />{" "}
              {question.Question}
            </div>
            <h1>{question.Question}</h1>
            <div
              dangerouslySetInnerHTML={{
                __html: question.Answer ? resolveRichText(question.Answer) : "",
              }}
            />
          </div>
        ) : (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              padding: "200px 0px 400px 0px",
            }}
          >
            <CircularProgress style={{ color: "#05BFBE" }} />
          </div>
        )}
        <div className={styles.divider} />

        {/*<h1>{t("help.relatedQues")}</h1>
          {question.faq &&
            question.faq.data.length > 0 &&
            question.faq.data.map((ques: relatedQuestions) => (
              <FaqQuestion
                key={`faq-${slug}`}
                question={ques.attributes.Question}
                answer={ques.attributes.Answer}
              />
            ))}*/}
      </div>

      <div className={styles.topQuestions}>
        <h2>{t("help.topQuestions")}</h2>
        <Toggle
          choices={topQuestionsTypes.map((item: any) => item.attributes?.label)}
          defaultChoice={view}
          handleSelected={(choice: string) => handleView(choice)}
        />
        <div>
          <Faqs
            getQuestions={(questions: []) => setFaqs(questions)}
            filter={view}
            filterType="topQuestionsType"
          />
        </div>
      </div>
      <div className={styles.contact}>
        <EnvelopeWhite />
        <h2>{t("contact.title")}</h2>
        <p>{t("contact.text")}</p>
        <p>{t("contact.subline")}</p>
        <div className="flex justify-content-center">
          <Link
            style={{ textDecoration: "none", color: "inherit" }}
            to="/contact-us"
          >
            <Button>{t("general.sendMessage")}</Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HelpPage;

const Arrow = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_10955_35953)">
      <path d="M6 3L11 8L6 13" fill="#05BFBE" />
    </g>
    <defs>
      <clipPath id="clip0_10955_35953">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
