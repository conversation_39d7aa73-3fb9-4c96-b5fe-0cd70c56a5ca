import { Link } from "react-router-dom";
import Button from "../../components/Button";
import { Helmet } from 'react-helmet-async';
import styles from "../../styles/404.module.scss";
import { t } from "i18next";

const NotFound = () => {
  return (
    <div className={styles.main}>
      <Helmet>
        <title>{t("general.orbit")}| 404 Page not found</title>
      </Helmet>
      <div className={styles.error}>
        <h5>404</h5>
        <div className={styles.notFound}>Page not found</div>
        <div className={styles.text}>
          The page you are looking for doesn't exist.
        </div>
        <Link to="/" style={{ textDecoration: "none" }}>
          <Button>Back to home</Button>
        </Link>
      </div>
    </div>
  );
};

export default NotFound;
