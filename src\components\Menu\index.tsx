import styles from "./menu.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { useRef } from "react";
import Button from "../Button";
import { ArrowDownDark } from "../svgs";

const Menu = ({ data, style = {} }: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  return (
    <div className={styles.menu}>
      <Button forwardRef={ref} onClick={() => toggleMenu(true)} style={style}>
        {data.label}
        <ArrowDownDark />
      </Button>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => toggleMenu(false)}
        align="end"
      >
        {data.items.map((item: any) => (
          <MenuItem
            key={"menu-" + item.label}
            onClick={item.onClick}
            className={styles.menuItem}
          >
            {item.icon}
            {item.label}
          </MenuItem>
        ))}
      </ControlledMenu>
    </div>
  );
};

export default Menu;
