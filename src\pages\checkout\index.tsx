import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useSearchParams } from "react-router-dom";
import Button from "../../components/Button";
import { Helmet } from "react-helmet-async";
import CryptoJS from 'crypto-js';
import {
  ArrowNext,
  CreditCardSm,
  FrequencyTick,
  Plus,
} from "../../components/svgs";
import styles from "../../styles/checkout.module.scss";
import { ApiGet, ApiPostAuth, ApiDelete } from "../api/api";
import { Collapse } from "@mui/material";
import Shimmer from "../../components/Shimmer";
import PaymentFail from "../../components/PaymentFail";
import PaymentLoading from "../../components/PaymentLoading";
import { getCardNetworkImage } from "../../components/utils/getCardNetworkImage";
import PaymentSuccess from "../../components/PaymentSuccess";
import PlanApplyFail from "../../components/PlanApplyFail";
import { v4 as uuid } from "uuid";
import { t } from "i18next";
import i18n from "../../i18n";

let stripe: any;

const stripeKey = import.meta.env.VITE_APP_STRIPE_KEY;

const Checkout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { basket, toastersOnLoad, userInfo, creditSelection, loggedIn } =
    useSelector((state: any) => state);

  useEffect(() => {
    try {
      stripe = (window as any).Stripe(stripeKey);
    } catch (e) {
      console.log(e);
    }
  }, []);

  const [searchParams, setSearchParams] = useSearchParams();

  const [noExpressButtons, setNoExpressButtons] = useState(true);
  const [expressLoading, setExpressLoading] = useState(false);

  useEffect(() => {
    if (basket !== null && loggedIn) {
      ApiDelete("/users/abandonedbasket", {})
      ApiPostAuth("/users/abandonedbasket", {
        planId: basket?.planId
      })
    }
  }, [basket])

  useEffect(() => {
    (window as any).fcWidget?.hide();

    return () => {
      (window as any).fcWidget?.show();
    };
  }, []);

  useEffect(() => {
    if (stripe) {
      const expressCheckoutOptions = {
        paymentMethods: {
          link: "never",
          applePay: "always",
          amazonPay: "never",
          googlePay: "always",
          paypal: "never",
        },
        buttonHeight: 50,
        layout: {
          overflow: "never",
        },
      };
      const elements = stripe.elements({
        locale: localStorage.getItem("i18nextLng") ?? "gb",
        mode: "payment",
        amount: basket?.prices[0].cost * 100,
        currency: "usd",
        captureMethod: "manual",
      });
      const expressCheckoutElement = elements.create(
        "expressCheckout",
        expressCheckoutOptions
      );
      expressCheckoutElement.on("ready", ({ availablePaymentMethods }: any) => {
        setExpressLoading(false);
        if (availablePaymentMethods) {
          setNoExpressButtons(false);
        }
      });
      expressCheckoutElement.on("confirm", (event: any) => {
        console.log(event);
        ApiPostAuth("/payment/create/intent", {
          planId: basket?.planId,
          currency: "usd",
        })
          .then((response) => {
            if (response.data.clientSecret && response.data.paymentIntentId) {
              // call Stripe function to initiate payment confirmation
              stripe
                .confirmPayment({
                  elements,
                  clientSecret: response.data.clientSecret,
                  confirmParams: {
                    return_url: `https://${
                      import.meta.env.VITE_APP_WEB_URL
                    }/process-payment?payment_intent=${
                      response.data.paymentIntentId
                    }&payment_type=${event.expressPaymentType}`,
                  },
                })
                .then((result: any) => {
                  console.log(result);
                  if (result.error) {
                    // Inform the customer that there's an error.
                  }
                });
            } else {
              dispatch({
                type: "notify",
                payload: {
                  error: true,
                  heading: "Oops!",
                  message: "Something went wrong, please try again",
                },
              });
            }
          })
          .catch((error) => {});
      });
      expressCheckoutElement.mount("#express-checkout-element");
    }
  }, [stripe]);

  // Check for success of failure
  useEffect(() => {
    const success = searchParams.get("success");
    const failure = searchParams.get("failure");
    const paySuccess = searchParams.get("pay_success");
    const payFailure = searchParams.get("pay_failure");
    if (success) {
      dispatch({
        type: "notify",
        payload: {
          error: false,
          heading: "Your card was saved successfully!",
          message: "You can now proceed to payment",
        },
      });
    } else if (failure) {
      dispatch({
        type: "notify",
        payload: {
          error: true,
          heading: "That didn't work",
          message:
            "Something went wrong while saving your card, please try again",
        },
      });
    } else if (paySuccess) {
      ApiDelete("/users/abandonedbasket", {})
      setPaymentSuccess(true);
    } else if (payFailure) {
      setPaymentFailed(true);
    }
  }, []);

  const handleOpenAddCard = () => {
    ApiGet("/payments/cards/creationscreen").then((response) => {
      const { url, sessionId } = response.data;
      localStorage.setItem("sessionId", sessionId);
      localStorage.setItem("stripeLocation", "checkout");
      window.location.href = url;
    });
  };

  // Check if just registered to show toaster message
  useEffect(() => {
    if (toastersOnLoad.registerToCheckout) {
      dispatch({
        type: "notify",
        payload: {
          error: false,
          heading: "Registration Successful!",
          message: "You can now complete your purchase",
        },
      });
      dispatch({
        type: "set",
        toastersOnLoad: {
          ...toastersOnLoad,
          registerFromCheckout: false,
        },
      });
    }
  }, []);

  const [loading, setLoading] = useState(false);

  const [addNewCard, setAddNewCard] = useState(false);

  // Get Saved Cards
  const [cardsLoading, setCardsLoading] = useState(true);
  const [cards, setCards] = useState([] as any);
  const [activeCard, setActiveCard] = useState({ id: null } as any);

  useEffect(() => {
    ApiGet("/payments/cards")
      .then((response) => {
        if ("cards" in response.data) {
          setCards(response.data.cards);
          if (response.data.cards.length) setActiveCard(response.data.cards[0]);
        }
        setCardsLoading(false);
      })
      .catch((error) => {
        setCardsLoading(false);
        console.log(error);
      });
  }, []);

  const handleError = () => {
    setLoading(false);
    setPaymentFailed(true);
  };

  const purchasePlan = () => {
    setLoading(true);
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: "smooth",
    });

    let planData = {
      id: basket.planId,
      currencyCode: basket.prices[0].currencyCode,
      planType: "DATA",
      automaticPayment: false,
      didType: "NA",
    } as any;

    const paymentData = {
      paymentType: "savedCard",
    };

    const overallData = {
      plans: [planData],
      cardId: activeCard.id,
    };

    ApiPostAuth("/payment/stripe/make", overallData)
      .then((response: any) => {
        if (response.data.result === "succeeded") {
          createProductPlan(
            planData,
            paymentData,
            response.data.paymentIntentId
          );
        } else if (response.data.result === "requires_action") {
          challengeFlow(
            response.data,
            planData,
            paymentData,
            response.data.paymentIntentId
          );
        } else {
          handleError();
        }
      })
      .catch((error: any) => {
        console.log(error);
        handleError();
      });
  };

  const googleAnalyticsPurchaseEvent = () => {
    window.gtag('event', 'purchase', {
      orderId: uuid(),
      customerId: CryptoJS.SHA256(userInfo.email).toString(CryptoJS.enc.Hex),
      category: basket.planName,
      sku: CryptoJS.SHA256(basket.dataAllowance + 'GB Data Plan').toString(CryptoJS.enc.Hex),
      subTotal: basket.prices[0].discountedCost ? Number(basket.prices[0].discountedCost) : basket.prices[0].cost.toFixed(2),
      quantity: 1,
      payment_status: 'payment_success'
    });
  } 

  // Create plan if payment successful
  const createProductPlan = (
    planData: any,
    paymentData: any,
    paymentId: any
  ) => {
    const overallData = {
      plans: [planData],
      payment: paymentData,
      paymentId: paymentId,
    };

    if (basket.isRenew) {
      ApiPostAuth("/subscriptions/renew/manual", {
        subscriptionId: basket.subscriptionId.toString(),
        payment: paymentData,
      })
        .then((response) => {
          dispatch({
            type: "set",
            failedPlans: [],
            basket: null,
            creditSelection: null,
          });
          localStorage.removeItem("basket");
          localStorage.removeItem("creditSelection");
          if (response.data.failedPlans.length > 0) {
            dispatch({
              type: "set",
              failedPlans: response.data.failedPlans,
            });
          }
          // If success navigate to success page
          setLoading(false);
        })
        .catch((error) => {
          handleError();
        });
    } else {
      ApiPostAuth("/plans/create", overallData)
        .then((response) => {
          setLoading(false);
          if (response.data.failedPlans.length > 0) {
            setPlanFailed(true);
          } else {
            setPaymentSuccess(true);
          }
        })
        .catch((error) => {
          handleError();
        });
    }
  };

  const challengeFlow = (
    paymentResponse: any,
    planData: any,
    paymentData: any,
    paymentId: any
  ) => {
    stripe
      .confirmCardPayment(paymentResponse.clientSecret)
      .then((result: any) => {});

    const authChecker = setInterval(() => {
      ApiPostAuth(
        "/payment/stripe/check?paymentIntentId=" +
          paymentResponse.paymentIntentId,
        {}
      )
        .then((response) => {
          if (response.data.result === "succeeded") {
            stopChecker();
            createProductPlan(planData, paymentData, paymentId);
          } else if (response.data.result === "requires_payment_method") {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: "Authentication failed, please try again.",
              },
            });
            stopChecker();
          }
        })
        .catch((error) => {
          handleError();
          stopChecker();
        });
    }, 3000);

    const stopChecker = () => {
      clearInterval(authChecker);
    };
  };

  const [paymentFailed, setPaymentFailed] = useState(false);
  const [planFailed, setPlanFailed] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  useEffect(() => {
    if (!paymentFailed && !paymentSuccess && !planFailed) {
      navigate('/checkout')
    }

  }, [paymentFailed, paymentSuccess, planFailed])

  const oneTimePay = () => {
    let planData = {
      id: basket.planId,
      currencyCode: basket.prices[0].currencyCode,
      planType: "DATA",
      automaticPayment: false,
      didType: "NA",
    } as any;

    ApiPostAuth("/payment/stripe/paymentpage", {
      plans: [planData],
    }).then((response) => {
      const { url, sessionId } = response.data;
      localStorage.setItem("sessionId", sessionId);
      localStorage.setItem("stripeLocation", "processPayment");
      window.location.href = url;
      console.log(response);
    });
  };

  return (
    <div className={styles.main}>
      <Helmet>
        <title>
          {t("general.orbit")} | {t("general.dashboard")} |{" "}
          {t("account.pages.checkout")}
        </title>
      </Helmet>
      <PaymentLoading loading={loading} />
      <PaymentFail show={paymentFailed} setShow={setPaymentFailed} />
      <PlanApplyFail show={planFailed} setShow={setPlanFailed} />
      {
        paymentSuccess && <PaymentSuccess show={paymentSuccess} trackPaymentFn={() => googleAnalyticsPurchaseEvent()} />
      }
      <div className={styles.container}>
        <h2 className={styles.heading}>{t("account.pages.checkout")}</h2>
        <div className={styles.content}>
          <div className={styles.summary}>
            <h4>{t("account.checkout.planSummary")}</h4>
            {basket && (
              <>
                <div className={styles.row}>
                  <div className={styles.planName}>
                    {basket.countryCode && (
                      <div
                        className={`${styles.flag} ${
                          basket?.zoneImage ? styles.zonal : ""
                        }`}
                        style={{
                          backgroundImage: basket?.zoneImage
                            ? `url(${basket.zoneImage})`
                            : `url(https://orbit-public-media.s3.eu-north-1.amazonaws.com/flag-icons/png/${basket.countryCode}.png)`,
                        }}
                      />
                    )}
                    {basket.planName}
                  </div>
                  <div>
                    {basket.prices[0].currencySymbol}
                    {basket.prices[0].discountedCost ? basket.prices[0].discountedCost.toFixed(2) : basket.prices[0].cost.toFixed(2)}
                  </div>
                </div>
                <div className={styles.planInfo}>
                  {basket.dataAllowance} {t("plan.gbData")} • {basket.validity}{" "}
                  {
                    i18n.language === 'en' ? (
                     <>{ basket.validity > 1 ? (t('plan.days')+ ' ' + t('plan.validity')) : 'day' + ' ' + t('plan.validity') }</>
                    ) : (
                     <>{t('plan.validity')} {basket.validity > 1 && basket.validity} { basket.validity > 10 ?  'يوماً' : basket.validity === 1 ? 'يوم واحد' : t('plan.days')}</>
                    )
                  }
                </div>
              </>
            )}
            <div className={styles.divider} />
            <div className={styles.row} style={{ fontSize: 16 }}>
              <b>{t("account.checkout.total")}</b>
              <b>
                {basket?.prices[0].currencySymbol}
                {basket.prices[0].discountedCost ? basket.prices[0].discountedCost.toFixed(2) : basket.prices[0].cost.toFixed(2)}
              </b>
            </div>
          </div>
          <div className={styles.paymentForm}>
            <div className={styles.paymentTitle}>
              {t("account.checkout.payment")}
            </div>
            <div className={styles.alternativeMethods}>
              <Collapse in={expressLoading}>
                <div className={styles.loaderButton}>
                  <Shimmer />
                </div>
                <div className={styles.loaderButton}>
                  <Shimmer />
                </div>
              </Collapse>
              <div id="express-checkout-element" />
            </div>
            <Collapse in={!noExpressButtons}>
              <div className={styles.orDivider}>
                <div className={styles.line} />
                {t("general.or")}
                <div className={styles.line} />
              </div>
            </Collapse>
            <div
              className={`${styles.savedCardsContainer} ${
                addNewCard && styles.back
              }`}
            >
              <div className={styles.addCardButton} onClick={oneTimePay}>
                <CreditCardSm />
                {t("account.checkout.byCard")}
                <span className={styles.arrow}>
                  <ArrowNext />
                </span>
              </div>
              <Collapse in={!cardsLoading && cards.length > 0}>
                <div className={styles.savedCardsTitle}>{ t('account.paymentCards.savedCards') }</div>
              </Collapse>
              <div className={styles.addCardButton} onClick={handleOpenAddCard}>
                <Plus />
                {t("account.paymentCards.addCard")}
              </div>
              {cardsLoading
                ? [0, 1].map((i) => (
                    <div
                      key={`card-skeleton-${i}`}
                      className={styles.savedCard}
                    >
                      <div className={styles.imageSkeleton}>
                        <Shimmer />
                      </div>
                      <div className={styles.numberSkeleton}>
                        <Shimmer />
                      </div>
                      &nbsp;&nbsp;|&nbsp;&nbsp;
                      <div className={styles.dateSkeleton}>
                        <Shimmer />
                      </div>
                    </div>
                  ))
                : cards.length
                ? cards.map((singleCard: any) => (
                    <div
                      key={`saved-skeleton-${singleCard.id}`}
                      className={`${styles.savedCard} ${
                        activeCard.id === singleCard.id && styles.active
                      }`}
                      onClick={() => {
                        setActiveCard(singleCard);
                      }}
                    >
                      <div className={styles.imageContainer}>
                        {getCardNetworkImage(singleCard)}
                      </div>
                      <div className={styles.cardNumber}>
                        •••• {singleCard.last4Digits}&nbsp;&nbsp;|&nbsp;&nbsp;
                        {singleCard.expiry}
                      </div>
                      {activeCard.id === singleCard.id && (
                        <div className={styles.tick}>
                          <FrequencyTick />
                        </div>
                      )}
                    </div>
                  ))
                : ""}
              <Collapse in={activeCard.id !== null}>
                <Button
                  style={{ width: "100%", height: 48 }}
                  onClick={purchasePlan}
                  disabled={cardsLoading || cards.length === 0}
                >
                  {t("account.checkout.pay")} {basket?.prices[0].currencySymbol}
                  {basket.prices[0].discountedCost ? basket.prices[0].discountedCost.toFixed(2) : basket.prices[0].cost.toFixed(2)}
                </Button>
              </Collapse>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
