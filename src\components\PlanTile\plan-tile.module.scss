@use "../../styles/theme.scss" as *;

.planBox {
  padding: 40px 16px 16px 16px;
  border-radius: 24px;
  text-align: center;
  display: inline-block;
  margin: 0px 18px 39px 0px;
  position: relative;
  min-width: 215px;
  @media (max-width: 768px) {
    width: 100%;
    margin: 0 auto 5px auto;
    max-width: 350px;
  }
  &.color-0 {
    background: #cdf7ee;
  }

  &.color-1 {
    background: #9bf0de;
  }

  &.color-2 {
    background: #9be5e4;
  }

  &.color-3 {
    background: #9ddaeb;
  }

  &.color-4 {
    background: #9dd0f1;
  }
  &.color-special {
    background: #F8CEDB;
  }

  .dataAmount {
    font-size: 26px;
    line-height: 32px;
    font-weight: 800;
    margin: 0px;
    margin-bottom: 8px;
  }

  .discount {
    position: absolute;
    top: 10px;
    left: -6px;
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: baseline;
    &:dir(rtl) {
      right: 0;
      width: 93%;
    }
    .discountAmount {
      margin: 0;
      font-size: 13px;
      color: #fff;
      font-weight: bold;
      background-size: contain;
      display: flex;
      width: 120px;
      height: 35px;
      justify-content: center;
      align-items: baseline;
      background-repeat: no-repeat;
      direction: ltr;
      span {
        padding-top: 5px;
      }
    }
    .oldPrice {
      color: #6F7189;
      font-size: 13px
    }
  }

  .validity {
    font-size: 18px;
    font-weight: 400;
    line-height: 28px;
    margin: 0px;
    margin-bottom: 24px;
  }
}
