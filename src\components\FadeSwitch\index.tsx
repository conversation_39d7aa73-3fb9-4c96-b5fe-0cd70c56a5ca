import { SwitchTransition, CSSTransition } from "react-transition-group";

const FadeSwitch = ({ key, children }: any) => {
  return (
    <SwitchTransition>
      <CSSTransition
        key={key}
        addEndListener={(node, done) =>
          node.addEventListener("transitionend", done, false)
        }
        classNames="fade"
      >
        {children}
      </CSSTransition>
    </SwitchTransition>
  );
};

export default FadeSwitch;
