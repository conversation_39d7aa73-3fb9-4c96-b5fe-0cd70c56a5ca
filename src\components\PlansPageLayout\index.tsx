import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useParams } from "react-router-dom";
import { ArrowBack } from "../svgs";
import styles from "./plans-page-layout.module.scss";
import { isNumeric } from "../utils/CardDetailsCheckers";

const PlansPageLayout = ({ backLink, children }: any) => {
  const dispatch = useDispatch();

  const { countryCode }: any = useParams();
  const { countries, zones } = useSelector((state: any) => state);

  const [selectedCountry, setSelectedCountry] = useState({
    countryCode: "",
    iconUrl: "",
    countryImage: "",
    countryName: "",
  });

  useEffect(() => {
    if (countries.length) {
      let country = countries.find(
        (item: any) => item.countryCode === countryCode.toUpperCase()
      );

      if (!country) {
        if (isNumeric(countryCode)) {
          let zone = zones[parseInt(countryCode) - 1];

          if (zone) {
            country = {
              countryCode: "GB",
              countryImage: zone.zoneImage,
              countryName: zone.zoneName,
              iconUrl: zone.zoneImage,
            };
          }
        }
      }

      if (country) {
        setSelectedCountry(country);

        dispatch({
          type: "set",
          selectedCountry: country,
        });
      }
    }
  }, [countries, countryCode]);

  return (
    <div
      className={styles.container}
      style={{
        backgroundImage:
          selectedCountry.countryImage === "NoImage"
            ? "url(/shop/no_background.svg)"
            : `url(${encodeURI(selectedCountry.countryImage)})`,
      }}
    >
      <div className={styles.background}>
        <div className={`${styles.widthContainer} max-width-container`}>
          <div className={styles.topBar}>
            <Link to={backLink}>
              <button className={styles.backButton}>
                <ArrowBack />
              </button>
            </Link>
            <div className={styles.countryName}>
              <div
                className={styles.flag}
                style={{ backgroundImage: `url(${selectedCountry.iconUrl})` }}
              />
              <div className={styles.text}>{selectedCountry.countryName}</div>
            </div>
          </div>
          {children}
        </div>
      </div>
    </div>
  );
};

export default PlansPageLayout;
