import { useEffect, useState } from "react";
import { ApiGet } from "../../pages/api/api";
import styles from "./rates-section.module.scss";
import { useDispatch } from "react-redux";
import CountryRateSkeleton from "../CountryRateSkeleton";
import CountryRate from "../CountryRate";
import SearchBar from "../SearchBar";
import Button from "../Button";
import Pagination from "../Pagination";

const RatesSection = () => {
  const dispatch = useDispatch();

  const [rates, setRates] = useState([]);

  const [page, setPage] = useState(1);

  useEffect(() => {
    setRates([]);
    ApiGet("/rates", page)
      .then((response) => {
        console.log(response);
        setRates(response.data.countryRates);
      })
      .catch((error) => {});
  }, [page]);

  const [searchQuery, setSearchQuery] = useState("");
  const [queryDisplay, setQueryDisplay] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const handleSearchChange = (e: any) => {
    setSearchQuery(e.target.value);
  };

  const clearSearch = () => {
    setSearchQuery("");
  };

  useEffect(() => {
    if (searchQuery === "") {
      setSearchResults([]);
      setShowSearchResults(false);
    }
  }, [searchQuery]);

  const checkIfFirstWord = (item: any, query: any) => {
    const name = item.countryName;
    return query.some((queryStr: any) => {
      const combinedRegex = new RegExp("^" + queryStr.toLowerCase(), "gi");
      return combinedRegex.test(name.toLowerCase());
    });
  };

  const checkIfAnyWord = (item: any, query: any) => {
    const name = item.countryName;
    return query.some((queryStr: any) => {
      const combinedRegex = new RegExp("\\b" + queryStr.toLowerCase(), "gi");
      return combinedRegex.test(name.toLowerCase());
    });
  };

  const [searchLoading, setSearchLoading] = useState(false);

  const handleSearch = () => {
    if (searchQuery === "") return;

    setQueryDisplay(searchQuery);

    setSearchResults([]);
    setSearchLoading(true);
    ApiGet("/rates", 1, searchQuery)
      .then((response) => {
        console.log(response);
        setSearchResults(response.data.countryRates);
        setShowSearchResults(true);
        setSearchLoading(false);
      })
      .catch((error) => {
        setSearchLoading(false);
      });
  };

  return (
    <div>
      <div className={styles.topBar}>
        <h3>Global Rates</h3>
        <div className={styles.searchContainer}>
          <SearchBar
            handleSearch={handleSearch}
            id="rates-search-input"
            searchQuery={searchQuery}
            placeholder="Search country"
            handleSearchChange={handleSearchChange}
            clearSearch={clearSearch}
          />
        </div>
      </div>
      <div className={styles.ratesContainer}>
        {searchLoading ? (
          Array.from({ length: 10 }).map((x, i) => (
            <CountryRateSkeleton key={`country-rate-skeleton-${i}`} />
          ))
        ) : showSearchResults ? (
          searchResults.length ? (
            searchResults.map((rate: any) => (
              <CountryRate
                key={`country-rate-${rate.country.countryCode}`}
                rate={rate}
              />
            ))
          ) : (
            <div className={styles.noResults}>
              <h4>No Results</h4>
              <p>
                We didn’t find any countries named “{queryDisplay}”.
                <br />
                Please double-check your text and try again or browse all our
                countries.
              </p>
              <Button onClick={clearSearch}>View all countries</Button>
            </div>
          )
        ) : rates.length > 0 ? (
          rates.map((rate: any) => (
            <CountryRate
              key={`country-rate-${rate.country.countryCode}`}
              rate={rate}
            />
          ))
        ) : (
          Array.from({ length: 10 }).map((x, i) => (
            <CountryRateSkeleton key={`country-rate-skeleton-${i}`} />
          ))
        )}
      </div>
      {!showSearchResults && (
        <div className={styles.pagination}>
          <Pagination
            currentPage={page}
            setCurrentPage={setPage}
            disabled={rates.length === 0}
          />
        </div>
      )}
    </div>
  );
};

export default RatesSection;
