import { useMediaQuery } from "@mui/material";
import { useEffect, useState } from "react";
import Button from "../Button";
import Modal from "../Modal";
import OtpInput from "../OtpInput";
import styles from "./delete-number.module.scss";
import { parsePhoneNumber } from "libphonenumber-js";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { t } from "i18next";

const DeleteNumber = ({ show, setShow, activeNumber, repopulate }: any) => {
  const dispatch = useDispatch();
  const [buttonDisabled, setButtonDisabled] = useState(true);

  const [loading, setLoading] = useState(false);

  const getFormattedPhone = (number: any) => {
    try {
      let phone = number;
      if (phone[0] !== "+") {
        phone = "+" + phone;
      }
      return parsePhoneNumber(phone).formatInternational();
    } catch (e) {
      return number;
    }
  };

  const [value, setValue] = useState("");

  useEffect(() => {
    if (value.length < 4) {
      setButtonDisabled(true);
    }
  }, [value]);

  const codeEntered = (value: any) => {
    if (
      value ===
      activeNumber.toString().slice(activeNumber.toString().length - 4)
    ) {
      setButtonDisabled(false);
    }
  };

  const under768 = useMediaQuery("(max-width: 768px)");

  const handleDelete = () => {
    setLoading(true);
    ApiPostAuth("/subscriptions/did/delete", {
      didNumber: activeNumber,
    })
      .then((response) => {
        repopulate(() => {
          setLoading(false);
          setShow(false);
          setValue("");
          dispatch({
            type: "notify",
            payload: {
              error: false,
              heading: t('account.msgs.success'),
              message: response.data.message,
            },
          });
        });
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: t('buttons.oops'),
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Modal
      show={show}
      style={{ maxWidth: 1124, height: under768 ? "auto" : 550 }}
    >
      <div className={styles.main}>
        <Button
          onClick={() => {
            setShow(false);
            setValue("");
          }}
          style={{ marginLeft: "auto" }}
          color="tertiary"
          disabled={loading}
        >
          { t('buttons.cancel') }
        </Button>
        <h4>Delete phone number {getFormattedPhone(activeNumber)}?</h4>
        <p>
          To delete this phone number, please type in the last 4 digits of the
          phone number.
        </p>
        <OtpInput
          value={value}
          setValue={setValue}
          codeLength={4}
          handleSuccess={codeEntered}
          loading={loading}
        />
        <div className={styles.buttons}>
          <Button
            onClick={() => {
              setShow(false);
              setValue("");
            }}
            color="secondary"
            disabled={loading}
          >
            No, I've changed my mind
          </Button>
          <Button
            style={{ marginLeft: 16 }}
            onClick={handleDelete}
            disabled={buttonDisabled}
            loading={loading}
          >
            Yes, delete phone number
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteNumber;
