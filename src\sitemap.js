import { SitemapStream, streamToPromise }  from 'sitemap'
import { Readable } from 'stream'
import axios from "axios";
import fs from 'fs'

// Define your static routes
const links = [
    { url: '/',  changefreq: 'monthly', priority: 1  },
    { url: '/about-us',  changefreq: 'monthly', priority: 1  },
    { url: '/offers',  changefreq: 'monthly', priority: 1  },
    { url: '/how-it-works',  changefreq: 'monthly', priority: 1  },
    { url: '/blog',  changefreq: 'monthly', priority: 0.9  },
    { url: '/help',  changefreq: 'monthly', priority: 0.9  },
    { url: '/terms-and-conditions',  changefreq: 'monthly', priority: 0.8  },
    { url: '/privacy-policy',  changefreq: 'monthly', priority: 0.8  },
    { url: '/contact-us',  changefreq: 'monthly', priority: 0.3  },
    { url: '/partnerships',  changefreq: 'monthly', priority: 0.2  },
    { url: '/register',  changefreq: 'monthly', priority: 0.4  },
    { url: '/plans/countries',  changefreq: 'monthly', priority: 0.5  },
    { url: '/plans/region/1',  changefreq: 'monthly', priority: 0.5  },
    { url: '/plans/region/2',  changefreq: 'monthly', priority: 0.5  },
    { url: '/plans/region/3',  changefreq: 'monthly', priority: 0.5 },
    { url: '/plans/region/4',  changefreq: 'monthly', priority: 0.5  },
    { url: '/plans/region/5',  changefreq: 'monthly', priority: 0.5  },
    { url: '/plans/region/6',  changefreq: 'monthly', priority: 0.5  },
]

// Example of dynamic routes (these should come from your backend or database)
let getRoutes = (endpoint) => axios.get(import.meta.env.VITE_APP_STRAPI_URL + endpoint);

let allLinks = [...links]

getRoutes("/api/faqs?fields=slug").then(async (response) => {
    if (response) {
        let data = await response.data.data.map((route, index) => {
            return { url: `/help/${route.id}`, changefreq: 'monthly', priority: 0.8 }
        })
        allLinks = [...allLinks, ...data]
    }
})


    const dynamicRoutes = async () => {
        const blogsResponse = await getRoutes("/api/blogs?fields=slug");
        const faqsResponse = await getRoutes("/api/faqs?fields=slug");
        const blogRoutes = blogsResponse.data.data.map((route) => {
            return { url: `/blog/${route.id}`, changefreq: 'monthly', priority: 0.8 }
        });
        const faqRoutes = faqsResponse.data.data.map((route) => {
            return { url: `/help/${route.id}`, changefreq: 'monthly', priority: 0.8 }
        });
        const allRoutes = [...blogRoutes, ...faqRoutes]
        return allRoutes;
    };
    
    // Usage example
    (async () => {
        let routes = await dynamicRoutes()
        allLinks.concat(routes);
        const stream = new SitemapStream( { hostname: 'https://orbitmobile.com/' } )
        const sm = streamToPromise(Readable.from(allLinks).pipe(stream)).then((data) =>
            data.toString()
          ).then((data) => {
            fs.writeFileSync('../public/sitemap.xml', data);
        })
    })();

