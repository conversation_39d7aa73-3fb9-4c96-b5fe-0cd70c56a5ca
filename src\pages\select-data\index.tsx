import PlanSelection from "../../components/PlanSelection";
import { useEffect, useState } from "react";
import { ApiGet } from "../api/api";
import { useParams } from "react-router-dom";
import { isNumeric } from "../../components/utils/CardDetailsCheckers";

const SelectData = () => {
  const { countryCode } = useParams();

  const [plans, setPlans] = useState([] as any);

  const sortPlans = (a: any, b: any) => {
    const aCost = a.prices[0].cost,
      bCost = b.prices[0].cost,
      aData = a.dataAllowance,
      bData = b.dataAllowance,
      aValid = a.validity,
      bValid = b.validity;

    if (aCost === bCost) {
      if (aData === bData) {
        if (aValid === bValid) {
          return 0;
        } else {
          return aValid - bValid;
        }
      } else {
        return aData - bData;
      }
    } else {
      return aCost - bCost;
    }
  };

  useEffect(() => {
    setPlans([] as any);
    let url = isNumeric(countryCode)
      ? `/plans?planType=esim&zoneId=${countryCode}`
      : `/plans?planType=esim&countryCode=${countryCode}`;
    ApiGet(url)
      .then((response) => {
        console.log(response.data.esimPlans);
        setPlans(response.data.esimPlans.sort(sortPlans));
      })
      .catch((error) => {
        console.log(error);
      });
  }, [countryCode]);

  return (
    <PlanSelection
      planTypes={plans}
      pageTitle="Worldwide Data"
      pageText="Select the data plan where internet connection is never in question wherever you go."
    />
  );
};

export default SelectData;
