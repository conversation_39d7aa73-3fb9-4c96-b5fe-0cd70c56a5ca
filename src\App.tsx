import React, { useEffect } from "react";
import {
  BrowserRouter as Router,
  Route,
  Routes,
  Outlet,
  Navigate,
} from "react-router-dom";
import ScrollToTop from "./components/ScrollToTop";
import { HelmetProvider } from "react-helmet-async";
import Layout from "./components/Layout";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import jwt_decode from "jwt-decode";
import AllCountriesPlans from "./pages/all-countries-plans";
import RegionPlans from "./pages/regionPlans";
import { AboutUs } from "./pages/about-us";
import ResetPassword from "./pages/reset-password";
import TopDestPlans from "./pages/top-destinations";
import LoadingLayout from "./components/LoadingLayout";

const loading = <LoadingLayout />;

const MainLayout = () => {
  return (
    <Layout>
      <Outlet />
    </Layout>
  );
};

function MyApp() {
  const { i18n } = useTranslation();
  const dispatch = useDispatch();

  // Pages
  const Home = React.lazy(() => import("./pages/home"));
  const Offers = React.lazy(() => import("./pages/offers"));
  const HowItWorks = React.lazy(() => import("./pages/how-it-works"));
  const Help = React.lazy(() => import("./pages/help"));
  const HelpPage = React.lazy(() => import("./pages/help-page"));
  const Blog = React.lazy(() => import("./pages/blog"));
  const Checkout = React.lazy(() => import("./pages/checkout"));
  const BlogPage = React.lazy(() => import("./pages/blog-page"));
  const TermsAndConditions = React.lazy(
    () => import("./pages/terms-and-conditions")
  );
  const PrivacyPolicy = React.lazy(() => import("./pages/privacy-policy"));
  const Login = React.lazy(() => import("./pages/login"));
  const ForgotPassword = React.lazy(() => import("./pages/forgot-password"));
  const Register = React.lazy(() => import("./pages/register"));
  const FinishReg = React.lazy(() => import("./pages/finish-registration"));
  const FinishRegSocial = React.lazy(
    () => import("./pages/finish-registration-social")
  );
  const RegisterSuccess = React.lazy(() => import("./pages/register-success"));
  const Dashboard = React.lazy(() => import("./pages/dashboard"));
  const StripeResponse = React.lazy(() => import("./pages/stripe-response"));
  const CrmLogin = React.lazy(() => import("./pages/crm-login"));
  const NotFound = React.lazy(() => import("./pages/404"));
  const ContactUs = React.lazy(() => import("./pages/contact-us"));
  const SelectRegionalPlan = React.lazy(
    () => import("./pages/select-regional-plan")
  );
  const ProcessPayment = React.lazy(() => import("./pages/process-payment"));
  const FirebaseAction = React.lazy(() => import("./pages/firebase-action"));
  const DeleteAccount = React.lazy(() => import("./pages/delete-account"));

  useEffect(() => {
    const dir = i18n.dir(i18n.language);
    document.documentElement.dir = dir;
  }, [i18n, i18n.language]);

  useEffect(() => {
    let currentBasketStorage = localStorage.getItem("basket");
    let currentCreditStorage = localStorage.getItem("creditSelection");

    let currentBasket = currentBasketStorage
      ? JSON.parse(currentBasketStorage)
      : null;

    let currentCredit = currentCreditStorage
      ? JSON.parse(currentCreditStorage)
      : null;

    dispatch({
      type: "set",
      basket: currentBasket,
      creditSelection: currentCredit,
    });
  }, []);

  const Protected = ({ children }: any) => {
    const dispatch = useDispatch();
    const { loggedIn } = useSelector((state: any) => state);
    if (!loggedIn) {
      if (localStorage.getItem("token")) {
        let jwt = jwt_decode(localStorage.getItem("token")!) as any;
        if ("exp" in jwt && jwt.exp * 1000 > new Date().getTime()) {
          dispatch({
            type: "set",
            loggedIn: true,
          });
          return children;
        } else {
          return <Navigate to="/login" replace />;
        }
      } else {
        return <Navigate to="/login" replace />;
      }
    }
    return children;
  };

  return (
    <HelmetProvider>
      <Router>
        <React.Suspense fallback={loading}>
          <ScrollToTop>
            <Routes>
              <Route path="/stripe-response" element={<StripeResponse />} />
              <Route path="/__/auth/action" element={<FirebaseAction />} />
              <Route path="/crm-login" element={<CrmLogin />} />
              <Route element={<MainLayout />}>
                <Route path="*" element={<NotFound />} />
                <Route path="/" element={<Home />} />
                <Route path="/contact-us" element={<ContactUs />} />
                <Route
                  path="/plans/top-destinations"
                  element={<TopDestPlans />}
                />
                <Route
                  path="/plans/countries"
                  element={<AllCountriesPlans />}
                />
                <Route path="/plans/region/:id" element={<RegionPlans />} />
                <Route
                  path="/plans/region/:id/select-plan"
                  element={<SelectRegionalPlan />}
                />
                <Route path="/offers" element={<Offers />} />
                <Route path="/how-it-works" element={<HowItWorks />} />
                <Route path="/about-us" element={<AboutUs />} />
                <Route path="/help" element={<Help />} />
                <Route path="/help/:slug" element={<HelpPage />} />
                <Route path="/blog" element={<Blog />} />
                <Route path="/blog/:slug" element={<BlogPage />} />
                <Route path="/checkout" element={<Checkout />} />
                <Route path="/process-payment" element={<ProcessPayment />} />
                <Route
                  path="/terms-and-conditions"
                  element={<TermsAndConditions />}
                />
                <Route path="/privacy-policy" element={<PrivacyPolicy />} />
                <Route path="/login" element={<Login />} />
                <Route path="/forgot-password" element={<ForgotPassword />} />
                <Route path="/set-password" element={<ResetPassword />} />
                <Route path="/reset-password" element={<ResetPassword />} />
                <Route path="/delete-account" element={<DeleteAccount />} />
                <Route path="/register" element={<Register />} />
                <Route path="/finish-registration" element={<FinishReg />} />
                <Route
                  path="/finish-registration-social"
                  element={<FinishRegSocial />}
                />
                <Route path="/register-success" element={<RegisterSuccess />} />
                <Route
                  path="/dashboard/:section"
                  element={
                    <Protected>
                      <Dashboard />
                    </Protected>
                  }
                />
              </Route>
            </Routes>
          </ScrollToTop>
        </React.Suspense>
      </Router>
    </HelmetProvider>
  );
}

export default MyApp;
