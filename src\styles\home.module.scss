@use "./theme.scss" as *;
.howitworksSec {
  background-color: #e8f8f8;
  padding: 50px 130px;
  text-align: center;
  @media (max-width: 1250px) {
    padding: 50px 24px;
  }
  @media (max-width: 768px) {
    padding: 50px 16px;
  }
  .howitworksMobiles {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    justify-content: center;
    justify-items: center;
    align-items: stretch;
    grid-column-gap: 16px;
    padding-bottom: 50px;
    @media (max-width: 768px) {
      grid-template-columns: 1fr 1fr;
      grid-column-gap: 24px;
      grid-row-gap: 82px;
    }
    .singleMobile {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
    }
    p {
      margin: 0px;
      margin-bottom: 24px;
      font-size: 16px;
      line-height: 28px;
      max-width: 190px;
      @media (max-width: 1250px) {
        font-size: 16px;
        line-height: 24px;
      }
    }
  }
  img {
    width: 100%;
    max-width: 190px;
  }
  p {
    margin-bottom: 20px;
    line-height: 23px;
  }
  h2 {
    font-size: 48px;
    font-weight: 700;
    color: $secondary;
    @media (max-width: 768px) {
      font-size: 30px;
    }
  }
}
.heroSection {
  .inner {
    width: 100%;
    height: 100%;
    display: flex;
    padding: 63px 44px 35px 50px;
    background: linear-gradient(
        180deg,
        rgba(22, 11, 42, 0.5) 0%,
        rgba(22, 11, 42, 0) 100%
      ),
      linear-gradient(
        90deg,
        rgba(22, 11, 42, 0.6) 0%,
        rgba(22, 11, 42, 0.3) 50%,
        rgba(22, 11, 42, 0.6) 100%
      );
    @media (max-width: 768px) {
      padding: 50px 24px 35px 24px;
    }
    .container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      @media (max-width: 900px) {
        flex-direction: column;
      }
      @media (max-width: 768px) {
        text-align: center;
      }
    }
    .left {
      .heading {
        font-weight: 700;
        font-size: 40px;
        line-height: 60px;
        color: #fff;
        margin: 0;
        @media (max-width: 768px) {
          font-size: 36px;
          line-height: 54px;
        }
      }
      .bodyText {
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        color: #fff;
        margin: 0;
        max-width: 541px;
        @media (max-width: 768px) {
          font-size: 16px;
          line-height: 24px;
        }
      }
      @media (max-width: 900px) {
        margin-bottom: 50px;
      }
      @media (max-width: 768px) {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }
    .right {
      display: flex;
      @media (max-width: 900px) {
        margin-top: 20px;
      }
    }
    .rightImageOne {
      max-width: 319px;
      width: 100%;
      align-self: flex-start;
      @media (max-width: 1220px) {
        width: 210px;
      }
      @media (max-width: 485px) {
        width: 53%;
      }
    }
    .rightImageTwo {
      max-width: 300px;
      width: 100%;
      margin-left: 17px;
      align-self: flex-end;
      @media (max-width: 1220px) {
        width: 200px;
      }
      @media (max-width: 485px) {
        width: 47%;
      }
    }
  }
}

.sectionTwo {
  display: flex;
  flex-direction: column;
  padding: 30px 50px 100px 50px;
  overflow: hidden;
  @media (max-width: 768px) {
    padding: 50px 24px 50px 24px;
  }
  .topContainer {
    display: grid;
    grid-template-columns: 352px auto 414px;
    margin-bottom: 80px;
    @media (max-width: 1250px) {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 50px;
    }
    .imageFiller {
      width: 100%;
      max-width: 900px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      @media (max-width: 1250px) {
        max-width: 600px;
      }
    }
    .imageFillerInner {
      padding-bottom: 105%;
      width: 100%;
    }
    .dualPhoneImage {
      width: 160%;
      position: absolute;
      top: 0px;
    }
    .tagline {
      color: $dark-dark-purple;
      font-weight: 700;
      font-size: 34px;
      line-height: 51px;
      margin: 0;
      align-self: center;
      @media (max-width: 1250px) {
        text-align: center;
      }
      @media (max-width: 768px) {
        font-size: 26px;
        line-height: 39px;
      }
      .purple {
        color: $secondary;
      }
    }
    .taglineOne {
      margin-bottom: 150px;
      @media (max-width: 1250px) {
        margin-bottom: 20px;
      }
    }
    .taglineTwo {
      margin-top: 400px;
      @media (max-width: 1250px) {
        margin-top: 20px;
      }
    }
  }
  .bottomContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    @media (max-width: 1160px) {
      width: 100%;
      justify-content: space-between;
      max-width: 825px;
    }
    @media (max-width: 768px) {
      margin-top: 30px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      justify-content: center;
      grid-row-gap: 20px;
    }
    .item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 170px;
      &:last-child {
        margin-right: 0;
      }
      @media (max-width: 1160px) {
        margin-right: 0;
      }
      @media (max-width: 768px) {
        margin-bottom: 40px;
        &:last-child {
          margin-right: 0;
        }
      }
      .name {
        margin-top: 34px;
        font-weight: 500;
        font-size: 24px;
        line-height: 36px;
        color: $dark-dark-purple;
        text-align: center;
        @media (max-width: 768px) {
          font-size: 16px;
          line-height: 27px;
        }
      }
    }
  }
}

.reviewsSection {
  padding: 80px 50px 50px 50px;
  @media (max-width: 768px) {
    padding: 50px 24px;
    text-align: center;
  }
  .container {
    display: grid;
    grid-template-columns: 465px auto;
    justify-content: space-between;
    @media (max-width: 950px) {
      display: flex;
      flex-direction: column;
    }
  }
  .reviewsImage {
    max-width: 804px;
    width: 100%;
  }
  .textGroup {
    margin-top: 148px;
    margin-right: 20px;
    @media (max-width: 950px) {
      margin-top: 0;
      margin-right: 0;
      margin-bottom: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    h3 {
      font-weight: 700;
      font-size: 34px;
      line-height: 51px;
      color: $dark-dark-purple;
      margin: 0;
      margin-bottom: 8px;
      @media (max-width: 768px) {
        font-size: 26px;
        line-height: 39px;
      }
    }
    p {
      font-weight: 400;
      font-size: 20px;
      line-height: 30px;
      color: $dark-dark-purple;
      margin: 0;
      @media (max-width: 768px) {
        white-space: initial;
        font-size: 16px;
        line-height: 24px;
      }
    }
  }
}
.faqSection {
  padding: 60px 200px;
  display: flex;
  flex-direction: column;
  .faqHeading {
    font-size: 48px;
    font-weight: 700;
    color: $secondary;
    text-align: center;
    @media (max-width: 768px) {
      font-size: 40px;
    }
    @media (max-width: 368px) {
      font-size: 30px;
    }
  }
  .faqText {
    color: $secondary;
    font-size: 18px;
    font-weight: 400;
    width: 50%;
    margin: 10px auto 50px;
    text-align: center;
    @media (max-width: 768px) {
      width: 72%;
    }
    @media (max-width: 425px) {
      width: 85%;
      font-size: 16px;
    }
  }
  a {
    display: inline-block;
    text-decoration: none;
    margin-top: 25px;
  }
  button {
    margin: auto;
    padding: 0 30px;
  }
  @media (max-width: 768px) {
    padding: 60px 20px;
  }
}

.plansSection {
  padding: 60px 16px;
  text-align: center;
  position: relative;
  @media (max-width: 425px) {
    padding-top: 30px;
  }
  h2 {
    font-size: 48px;
    font-weight: 700;
    @media (max-width: 600px) {
      font-size: 30px;
    }
  }
  p {
    font-size: 18px;
    font-weight: 300;
  }
}
