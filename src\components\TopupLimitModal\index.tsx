import Button from "../Button";
import Modal from "../Modal";
import { FadedAlert } from "../svgs";
import styles from "./topup-limit-modal.module.scss";

const TopupLimitModal = ({ show, setShow, type = "daily" }: any) => {
  return (
    <Modal
      show={show}
      setShow={setShow}
      style={{ height: "auto", maxWidth: 461 }}
    >
      <div className={styles.main}>
        <div className={styles.icon}>
          <FadedAlert />
        </div>
        <h5>
          {type === "daily" ? "Daily Limit Exceeded" : "Top-up Limit Reached"}
        </h5>
        <p>
          {type === "daily"
            ? "You have exceeded the daily limit of £30 for adding funds. Please try again tomorrow or contact our support team if you need further assistance."
            : "You have reached the weekly top-up limit of £100. Please wait until the start of the next week to add more funds. If you need assistance or have any questions, please contact our support team."}
        </p>
        <div className={styles.buttons}>
          <Button
            style={{ width: "100%", maxWidth: 165 }}
            onClick={() => {
              setShow(false);
            }}
          >
            Back
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default TopupLimitModal;
