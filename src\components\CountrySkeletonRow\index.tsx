import styles from "./country-skeleton-row.module.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import { CaretLeft, CaretRight } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import Shimmer from "../Shimmer";
import CountrySkeleton from "../CountrySkeleton";
import { v4 as uuid } from "uuid";

const CountrySkeletonRow = () => {
  return (
    <div className={styles.container}>
      <div className={styles.heading}>
        <div className={styles.title}>
          <Shimmer />
        </div>
        <div className={styles.swiperButtons}>
          <button
            style={{
              color: "#C1C1C1",
              cursor: "auto",
              marginRight: 7,
            }}
          >
            <CaretLeft />
          </button>
          <button
            style={{
              color: "#C1C1C1",
              cursor: "auto",
            }}
          >
            <CaretRight />
          </button>
        </div>
      </div>
      <Swiper
        spaceBetween={24}
        slidesPerView={1.3}
        breakpoints={{
          900: {
            slidesPerView: 3.5,
          },
          1500: {
            slidesPerView: 5.5,
          },
          2500: { slidesPerView: 10.5 },
        }}
        slidesOffsetBefore={50}
        slidesOffsetAfter={50}
        speed={700}
        style={{ overflow: "visible" }}
      >
        {Array.from({ length: 10 }, (v, i) => i).map(() => (
          <SwiperSlide
            style={{ overflow: "visible" }}
            key={`country-tile-${uuid()}`}
          >
            <CountrySkeleton />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default CountrySkeletonRow;
