@use "./theme.scss" as *;

.mainContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 32px 104px 0 104px;
  @media (max-width: 1250px) {
    padding: 32px 50px 0 50px;
  }
  @media (max-width: 768px) {
    padding: 32px 24px 0 24px;
  }
}

.planType {
  padding: 4px 12px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  color: $secondary;
  background-color: #854ce71a;
  border-radius: 200px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  .divider {
    margin: 0px 8px;
  }
  .countryName {
    display: flex;
    align-items: center;
    .flag {
      width: 16px;
      height: 16px;
      border-radius: 24px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      margin-right: 6px;
    }
  }
}

.heading {
  font-size: 48px;
  line-height: 72px;
  font-weight: 600;
  color: #1c1c1c;
  margin: 0 0 8px 0;
  @media (max-width: 768px) {
    font-size: 32px;
    line-height: 48px;
    text-align: center;
  }
}

.subHeading {
  font-size: 20px;
  line-height: 26px;
  font-weight: 400;
  margin: 0 0 32px 0;
  @media (max-width: 768px) {
    font-size: 16px;
    line-height: 24px;
    text-align: center;
  }
}

.selectsContainer {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  &.fourCol {
    grid-template-columns: 1fr 1fr 1fr 1fr;
    max-width: initial;
    @media (max-width: 1080px) {
      grid-template-columns: 1fr 1fr;
      max-width: 608px;
    }
    @media (max-width: 550px) {
      grid-template-columns: 1fr;
    }
  }
  &.threeCol {
    grid-template-columns: 1fr 1fr 1fr;
    max-width: 1013px;
    @media (max-width: 850px) {
      grid-template-columns: 1fr;
      max-width: 296px;
    }
  }
  &.twoCol {
    grid-template-columns: 1fr 1fr;
    max-width: 608px;
    @media (max-width: 550px) {
      grid-template-columns: 1fr;
      max-width: 296px;
    }
  }
  &.oneCol {
    grid-template-columns: 1fr;
    max-width: 296px;
  }
}

.plansContainer {
  padding: 40px 0 70px 0;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  width: 100%;
  grid-column-gap: 16px;
  grid-row-gap: 23px;
  align-content: stretch;
  @media (max-width: 1130px) {
    grid-template-columns: 1fr 1fr 1fr;
  }
  @media (max-width: 850px) {
    grid-template-columns: 1fr 1fr;
  }
  @media (max-width: 550px) {
    grid-template-columns: 1fr;
  }
}

.creditContainer {
  padding: 40px 0 70px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  flex-wrap: wrap;
}

.credit {
  height: 134px;
  background-color: #eff1f7;
  border-radius: 24px;
  width: 148px;
  margin: 8px;
  font-size: 36px;
  color: #000;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  &:hover {
    background-color: $light-primary;
  }
  &.active {
    background-color: $secondary;
    color: #fff;
  }
  &:last-of-type {
    margin-right: 0px;
  }
}

.bottomSection {
  width: 100%;
  padding: 42px 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f5efff;
  margin-top: auto;
  @media (max-width: 1120px) {
    padding: 42px 24px;
  }
  @media (max-width: 975px) {
    flex-direction: column;
    align-items: flex-start;
  }
  .left {
    display: grid;
    grid-template-columns: 28px 1fr;
    grid-column-gap: 12px;
    align-items: center;
    justify-content: space-between;
    max-width: 620px;
    @media (max-width: 975px) {
      margin-bottom: 24px;
    }
    .message {
      font-size: 16px;
      font-weight: 400;
      line-height: 22px;
      margin: 0;
      margin-right: 24px;
      @media (max-width: 768px) {
        font-size: 14px;
        line-height: 21px;
      }
    }
    svg {
      width: 28px;
      height: 24px;
    }
  }
  .right {
    display: flex;
    align-items: center;
    justify-content: space-between;
    @media (max-width: 975px) {
      margin-left: auto;
    }
    .price {
      font-size: 24px;
      line-height: 36px;
      font-weight: 700;
      margin-right: 32px;
      @media (max-width: 768px) {
        font-size: 20px;
        line-height: 30px;
      }
    }
  }
}

.countryDisplay {
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 22px;
  .flag {
    width: 26px;
    height: 20px;
    border-radius: 2px;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    margin-right: 8px;
    background-color: #cdcdcd;
  }
}

.autoRenewContainer {
  width: 100%;
  max-width: 662px;
  background-color: #eff1f7;
  border-radius: 24px;
  padding: 24px;
  color: $dark-dark-purple;
  .top {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .text {
      font-size: 20px;
      font-weight: 700;
      line-height: 30px;
    }
  }
  .description {
    margin: 8px 0 0 0;
    font-size: 16px;
    line-height: 24px;
  }
  .frequencyMain {
    margin-top: 16px;
    .frequencyTitle {
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
    }
    .frequenciesContainer {
      display: flex;
      flex-wrap: wrap;
      .frequency {
        background-color: #dadeec;
        border-radius: 100px;
        padding: 14px 0px;
        width: 170px;
        font-size: 16px;
        line-height: 24px;
        margin: 8px 8px 0 0;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.1s ease;
        svg {
          margin-right: 10px;
        }
        &:hover {
          background-color: rgb(217, 211, 226);
        }
        &.active {
          background-color: #cebee3;
          cursor: auto;
          &:hover {
            background-color: #cebee3;
          }
        }
      }
    }
  }
}
