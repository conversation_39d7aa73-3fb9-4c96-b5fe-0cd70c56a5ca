import { CircularProgress } from "@mui/material";
import styles from "./button.module.scss";

const Button = ({
  style,
  onClick,
  loading,
  disabled,
  children,
  color = "primary",
  id = "",
  forwardRef = null,
}: any) => {
  return (
    <button
      ref={forwardRef}
      id={id}
      disabled={loading || disabled}
      className={`${styles.button}
        ${
          color === "primary"
            ? styles.primary
            : color === "secondary"
            ? styles.secondary
            : color === "tertiary"
            ? styles.tertiary
            : color === "danger"
            ? styles.danger
            : color === "add-funds"
            ? styles.addFunds
            : ""
        }`}
      style={style}
      onClick={onClick}
    >
      {loading && (
        <CircularProgress
          style={{
            width: 20,
            height: 20,
            color: color === "primary" ? "#fff" : "#F47D27",
            gridArea: "1 / 1 / 2 / 2",
            margin: "0 auto",
          }}
        />
      )}
      <span
        style={{
          visibility: loading ? "hidden" : "visible",
          gridArea: "1 / 1 / 2 / 2",
          display: "flex",
          alignItems: "center",
        }}
        className={styles.children}
      >
        {children}
      </span>
    </button>
  );
};

export default Button;
