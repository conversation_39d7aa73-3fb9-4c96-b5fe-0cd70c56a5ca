@use "../../styles/theme.scss" as *;

.main {
  cursor: pointer;
  &:hover {
    .imageContainer {
      box-shadow: 0px 2.61451px 26.1451px rgba(116, 72, 176, 0.25);
    }
  }
}

.imageContainer {
  padding-bottom: 124.9%;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: box-shadow 0.3s ease-out;
  @media (max-width: 900px) {
    padding-bottom: 80%;
  }
  .stickyImageContainer {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      height: 100%;
      width: auto;
    }
  }
}

.nameContainer {
  display: grid;
  align-items: center;
  grid-template-columns: 24px auto;
  grid-column-gap: 12px;
  font-size: 16px;
  line-height: 24px;
  color: $dark-dark-purple;
  margin-top: 16px;
  overflow: visible;
  .flag {
    width: 24px;
    height: 24px;
    border-radius: 1000px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border: 1px solid rgba(0, 0, 0, 0.3);
  }
}
