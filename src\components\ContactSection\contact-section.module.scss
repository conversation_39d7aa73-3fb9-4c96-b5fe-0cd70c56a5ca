@use "../../styles/theme.scss" as *;

.mainContainer {
  width: 100%;
  margin: 0 auto;
  max-width: 1600px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: $light-primary;
  border-radius: 12px;
  color: $dark-dark-purple;
  padding: 73px 50px;
  @media (max-width: 768px) {
    padding: 50px 24px;
  }
  h4 {
    font-weight: 700;
    font-size: 34px;
    line-height: 51px;
    margin: 0 0 8px 0;
    text-align: center;
    @media (max-width: 768px) {
      font-size: 26px;
      line-height: 39px;
    }
  }
  p {
    font-size: 20px;
    line-height: 30px;
    margin: 0 0 39px 0;
    text-align: center;
    @media (max-width: 768px) {
      font-size: 16px;
      line-height: 24px;
    }
  }
  .buttons {
    display: flex;
    align-items: center;
    @media (max-width: 768px) {
      flex-direction: column;
      width: 100%;
      a {
        width: 100%;
        margin-bottom: 12px;
        max-width: 400px;
      }
      button {
        margin: 0;
        width: 100%;
      }
    }
  }
}
