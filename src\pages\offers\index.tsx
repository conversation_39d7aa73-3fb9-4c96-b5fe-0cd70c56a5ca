import Button from "../../components/Button";
import { PlanDataCalendar, Signal, Tag } from "../../components/svgs";
import { useSelector } from "react-redux";
import styles from "../../styles/offers.module.scss";
import { Link } from "react-router-dom";
import CountriesCovered from "../../components/CountriesCovered/CountriesCovered";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { t } from "i18next";
import { useMediaQuery } from "@mui/material";

const Offers = () => {
  const { countries } = useSelector((state: any) => state);
  const { i18n } = useTranslation();
  const [countriesCovered, setCountriesCovered] = useState([]);
  const [showModal, setShowModal] = useState(false);

  const handleModal = () => {
    setShowModal(true);
    setCountriesCovered(countries);
  };

  const under768 = useMediaQuery("(max-width: 768px)");

  return (
    <div className={styles.comingSoon}>
      <div className={`${styles.container} max-width-container`}>
        <div style={{ position: "relative" }}>
          <h1>
            More Data, <span className={styles.gradient}>Less Cost </span>
            {!under768 && <br />}
            Coming Soon!
          </h1>
          <img
            className={styles.starsLeft}
            src="/images-int/offers/stars_left.svg"
          />
          <img
            className={styles.starsRight}
            src="/images-int/offers/stars_right.svg"
          />
        </div>
        <p>
          Get ready to save big on your favorite eSIM data plans. Whether you're
          staying connected locally or traveling the globe, our upcoming
          discounts and promotions will make staying online even more
          affordable.
        </p>
        <img
          className={styles.manImage}
          src="/images-int/offers/man_with_phone.svg"
        />
      </div>
    </div>
    /*<div className={styles.main}>
      <h2>{ t('slogans.exclusiveOffers') }</h2>
      <p>{ t('slogans.takeDeals') }</p>
      <div className={styles.offersView}>
        <div className={styles.offerItem}>
          <div>
            <span className={styles.percentageSymbol}>%</span>
            <h4>Hoilday Magic 5GB</h4>
            <div className="flex justify-content-between items-center">
              <div>
                <Signal />
                <span>5 { t('general.gb') }</span>
              </div>
              <div>
                <PlanDataCalendar />
                <span>30 { t('plan.days') }</span>
              </div>
              <div>
                <Tag />
                <span>$9.99</span>
              </div>
            </div>
            <div className={styles.offerCta}>
              <div className={styles.countriesFlags}>
                {
                  countries.map((country:any, index:number) => {
                    if (index < 3) {
                      return (
                      <>
                        <img src={country.iconUrl} className={styles.countriesFlag} style={{ left: `${index * 13}px` }} />
                      </>
                      )
                    }
                  })
                }
                <span onClick={handleModal}>+56 { i18n.language === 'en' ? 'Countries' : 'دولة' }</span>
              </div>
              <Button color="primary">
                <Link to="/checkout">
                  { t('buttons.getTheOffer') }
                </Link>
              </Button>
            </div>
          </div>
        </div>
        <div className={styles.offerItem}>
          <div>
            <span className={styles.percentageSymbol}>%</span>
            <h4>Hoilday Magic 5GB</h4>
            <div className="flex justify-content-between items-center">
              <div>
                <Signal />
                <span>5 { t('general.gb') }</span>
              </div>
              <div>
                <PlanDataCalendar />
                <span>30 { t('plan.days') }</span>
              </div>
              <div>
                <Tag />
                <span>$9.99</span>
              </div>
            </div>
            <div className={styles.offerCta}>
              <div className={styles.countriesFlags}>
                {
                  countries.map((country:any, index:number) => {
                    if (index < 3) {
                      return (
                      <>
                        <img src={country.iconUrl} className={styles.countriesFlag} style={{ left: `${index * 13}px` }} />
                      </>
                      )
                    }
                  })
                }
                <span onClick={handleModal}>{ i18n.language === 'en' ? 'Countries' : 'دولة' }</span>
              </div>
              <Button color="primary">
                <Link to="/checkout">
                  { t('buttons.getTheOffer') }
                </Link>
              </Button>
            </div>
          </div>
        </div>
        <div className={styles.offerItem}>
          <div>
            <span className={styles.percentageSymbol}>%</span>
            <h4>Hoilday Magic 5GB</h4>
            <div className="flex justify-content-between items-center">
              <div>
                <Signal />
                <span>5 { t('general.gb') }</span>
              </div>
              <div>
                <PlanDataCalendar />
                <span>30 { t('plan.days') }</span>
              </div>
              <div>
                <Tag />
                <span>$9.99</span>
              </div>
            </div>
            <div className={styles.offerCta}>
              <div className={styles.countriesFlags}>
                {
                  countries.map((country:any, index:number) => {
                    if (index < 3) {
                      return (
                      <>
                        <img src={country.iconUrl} className={styles.countriesFlag} style={{ left: `${index * 13}px` }} />
                      </>
                      )
                    }
                  })
                }
                <span onClick={handleModal}>{ i18n.language === 'en' ? 'Countries' : 'دولة' }</span>
              </div>
              <Button color="primary">
                <Link to="/checkout">
                  { t('buttons.getTheOffer') }
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
      <CountriesCovered show={showModal} setShow={setShowModal} countries={countriesCovered} />
    </div>*/
  );
};

export default Offers;
