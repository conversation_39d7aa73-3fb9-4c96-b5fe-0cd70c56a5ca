import styles from "../../styles/home.module.scss";
import promotionalStyles from "../../styles/promotionalSection.module.scss";
import { Link } from "react-router-dom";
import { useEffect, useState } from "react";
import { cmsURL, CmsApiGet } from "../api/cms-api";
import Button from "../../components/Button";
import { Helmet } from "react-helmet-async";
import HomeSlider from "../../components/HomeSlider";
import { PlansSection } from "../../components/plansSection/plansSection";
import { useTranslation } from "react-i18next";
import { t } from "i18next";
import FaqAndSearchPlan from "../../components/FaqAndSearchPlan/FaqAndSearchPlan";
import { organizationSchema } from "../../components/utils/schemaMarkups";

const Home = () => {
  const { i18n } = useTranslation();
  const [data, setData] = useState<any>(null);
  const [plansView, setPlansView] = useState<'countries' | 'regions' | ''>("countries")

  useEffect(() => {
    CmsApiGet(`/api/home-page?locale=${i18n.language}&populate=deep`).then(
      (response: any) => {
        setData(response.data.data.attributes);
      }
    );
  }, [i18n, i18n.language]);

  return (
    <div>
      {data && (
        <Helmet>
          <title>
            {t("general.orbit")}| {data?.metaTitle}
          </title>
          <script type="application/ld+json">
            {JSON.stringify(organizationSchema(data))}
          </script>
          <meta name="description" content={data?.metaDescription} />
        </Helmet>
      )}
      {/****       Hero Section         ****/}
      <section className={styles.heroSection}>
        <HomeSlider data={data?.Carousal} toggleViewFn={(view: 'countries' | 'regions') => setPlansView(view)} />
      </section>
      {/****       Section 2         ****/}
      <section id="plans-section" className={`${styles.plansSection} max-width-container`}>
        <h2>{data?.choosePlan.title}</h2>
        <p>{data?.choosePlan.subtitle}</p>
        <PlansSection selectedView={plansView} toggleViewFn={() => setPlansView('')}  />
      </section>
      {/****       Video Section         ****/}
      <section className={styles.howitworksSec}>
        <div className={`${styles.howItWorksContainer} max-width-container`}>
          <h2>{data?.howItWorks.title}</h2>
          <div className={styles.howitworksMobiles}>
            {data?.howItWorks.steps.map((step: any) => (
              <div className={styles.singleMobile} key={step.id}>
                <p>{step.step}</p>
                <img src={cmsURL + step.stepImg.data.attributes.url} alt="" />
              </div>
            ))}
          </div>
          <div
            className="flex justify-content-center"
            style={{ marginTop: "20px" }}
          >
            <Link style={{ textDecoration: "none" }} to="/how-it-works">
              <Button>{t("general.learnMore")}</Button>
            </Link>
          </div>
        </div>
      </section>
      {/****       Reviews Section        ****/}
      <section className={promotionalStyles.promotionalSection}>
        <div className="max-width-container">
          <div className={promotionalStyles.mainSec}>
            <h2>{data?.whyWeAreTheBest.title}</h2>
            <p>{data?.whyWeAreTheBest.briefText}</p>
          </div>
          <div className={promotionalStyles.features + " flex"}>
            {data?.whyWeAreTheBest.values.map((value: any, index: number) => (
              <div key={index}>
                <img
                  src={cmsURL + value.valueIcon.data.attributes.url}
                  alt={`Orbit Mobile ESIM - ${value.title}`}
                />
                <div>
                  <h3>{value.title}</h3>
                  <p>{value.aboutText}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      <FaqAndSearchPlan faqOnly />
    </div>
  );
};

export default Home;
