import styles from "./latest-blogs.module.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import BlogPreview from "../BlogPreview";
import { CaretLeft, CaretRight } from "../svgs";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import BlogSkeleton from "../BlogSkeleton";
import { useMediaQuery } from "@mui/material";
import { CmsApiGet } from "../../pages/api/cms-api";

const LatestBlogs = () => {
  // store controlled swiper instance
  const [controlledSwiper, setControlledSwiper] = useState(null as any);

  const [swiperIsEnd, setSwiperIsEnd] = useState(false);
  const [swiperIsBeginning, setSwiperIsBeginning] = useState(true);

  const [blogs, setBlogs] = useState([] as any);

  useEffect(() => {
    CmsApiGet("/posts").then((response) => {
      setBlogs(response.data.posts);
    });
  }, []);

  const under768 = useMediaQuery("(max-width: 768px)");

  return (
    <section className={styles.blogSection}>
      <div className="max-width-container">
        <div className={styles.blogHeading}>
          <h3>Latest blogs</h3>
          <div className={styles.swiperButtons}>
            <button
              style={{
                color: swiperIsBeginning ? "#C1C1C1" : "#160B2A",
                cursor: swiperIsBeginning ? "auto" : "pointer",
                marginRight: 7,
              }}
              onClick={() => {
                if (controlledSwiper) {
                  controlledSwiper.slidePrev();
                }
              }}
            >
              <CaretLeft />
            </button>
            <button
              style={{
                color: swiperIsEnd ? "#C1C1C1" : "#160B2A",
                cursor: swiperIsEnd ? "auto" : "pointer",
              }}
              onClick={() => {
                if (controlledSwiper) {
                  controlledSwiper.slideNext();
                }
              }}
            >
              <CaretRight />
            </button>
          </div>
        </div>
        <Swiper
          spaceBetween={24}
          slidesPerView={1.3}
          breakpoints={{
            900: {
              slidesPerView: 2.5,
            },
            1500: {
              slidesPerView: 3.5,
            },
            2500: { slidesPerView: 4.5 },
          }}
          onSwiper={(swiper: any) => setControlledSwiper(swiper)}
          onSlideChange={(swiper: any) => {
            setSwiperIsBeginning(swiper.isBeginning);
            setSwiperIsEnd(swiper.isEnd);
          }}
          speed={700}
          centeredSlides={under768}
        >
          {blogs.length
            ? blogs.map((blogPost: any) => (
                <SwiperSlide key={`blog-post-${blogPost.summary}`}>
                  <BlogPreview
                    image={blogPost.featured_image}
                    title={blogPost.title}
                    link={blogPost.slug}
                  />
                </SwiperSlide>
              ))
            : [0, 1, 2].map((i) => (
                <SwiperSlide
                  style={{ overflow: "hidden" }}
                  key={`blog-post-skeleton-${i}`}
                >
                  <BlogSkeleton />
                </SwiperSlide>
              ))}
        </Swiper>
      </div>
    </section>
  );
};

export default LatestBlogs;
