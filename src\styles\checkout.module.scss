@use "./theme.scss" as *;

.main {
  position: relative;
  display: flex;
  width: 100%;
  padding: 0 24px;
  @media (max-width: 450px) {
    padding: 0 16px;
  }
}

.backButton {
  padding: 0;
  background: none;
  border: none;
  cursor: pointer;
  position: absolute;
  top: 27px;
  left: 55px;
  color: $dark-dark-purple;
  @media (min-width: 1700px) {
    left: calc((100vw - 1600px) / 2);
  }
  @media (max-width: 790px) {
    top: 24px;
    left: 24px;
  }
  svg {
    vertical-align: middle;
  }
}

.container {
  width: 100%;
  max-width: 832px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 0 58px 0;
}

.content {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 56px;
  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
  }
}

.heading {
  font-size: 30px;
  font-weight: 800;
  line-height: 38px;
  text-align: center;
  color: #000;
  margin: 0 0 65px 0;
}

.subtitle {
  margin: 0 0 29px 0;
  font-size: 14px;
  line-height: 20px;
}

.summary {
  width: 100%;
  color: $secondary;
  font-size: 14px;
  line-height: 20px;
  @media (max-width: 768px) {
    margin-bottom: 50px;
  }
  h4 {
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    margin: 0px 0px 16px 0px;
  }
  .row {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    justify-content: space-between;
    &:last-of-type {
      margin-bottom: 0px;
    }
  }
}
.divider {
  width: 100%;
  height: 1px;
  background-color: #bfc4ce;
  margin: 24px 0px;
}

.planName {
  display: flex;
  align-items: center;
}

.flag {
  height: 24px;
  width: 24px;
  border-radius: 100px;
  margin-inline-end: 8px;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  &.zonal {
    background-position: bottom;
  }
}

.betweenText {
  width: 100%;
  max-width: 524px;
  margin-bottom: 32px;
  color: $dark-dark-purple;
  font-size: 16px;
  line-height: 24px;
}

.paymentForm {
  width: 100%;
  max-width: 526px;
  @media (max-width: 768px) {
    max-width: 100%;
  }
  .paymentTitle {
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    margin-bottom: 16px;
  }
  .checkContainer {
    display: flex;
    align-items: center;
    p {
      font-size: 12px;
      line-height: 18px;
      margin: 0 0 0 12px;
    }
  }
  .formColumn {
    width: 100%;
    @media (max-width: 768px) {
      margin-bottom: 45px;
      &:last-child {
        margin-bottom: 0px;
      }
    }
    .columnHeading {
      color: $dark-dark-purple;
      font-size: 14px;
      line-height: 21px;
      margin-bottom: 16px;
      display: flex;
      justify-content: center;
    }
    .paymentMethods {
      padding-top: 3px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 45px;
    }
    .paymentMethodSelect {
      width: 73px;
      height: 73px;
      border-radius: 1000px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24px;
      background: #eff1f7;
      border: 2px solid #eff1f7;
      cursor: pointer;
      transition: all 0.2s ease;
      &.active {
        border: 2px solid $secondary;
        box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
        cursor: auto;
      }
      svg {
        vertical-align: middle;
      }
      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.savedCardsContainer {
  width: 100%;
  margin-bottom: 15px;
  position: relative;
  z-index: 1000;
  transition: all 0.4s ease;
  &.back {
    padding: 12px 24px;
    background: $light-primary;
  }
  .savedCard {
    display: flex;
    align-items: center;
    border-radius: 100px;
    margin-bottom: 8px;
    height: 56px;
    font-size: 14px;
    line-height: 20px;
    color: $secondary;
    width: 100%;
    padding: 0 16px;
    border: 2px solid #bfc4ce;
    cursor: pointer;
    transition: all 0.1s ease;
    &:hover {
      border-color: #a3a9b8;
    }
    .imageContainer {
      width: 40px;
      margin-right: 8px;
      display: flex;
      align-items: center;
      svg {
        width: 40px;
        vertical-align: middle;
      }
      img {
        width: 40px;
      }
    }
    &.active {
      .tick {
        display: flex;
        align-items: center;
        margin-left: auto;
        svg {
          color: #088bdd;
        }
      }
      border-color: #088bdd;
    }
  }
  .addCardButton {
    display: flex;
    background-color: none;
    border-radius: 100px;
    width: 100%;
    align-items: center;
    justify-content: flex-start;
    font-size: 14px;
    line-height: 20px;
    color: $secondary;
    height: 56px;
    cursor: pointer;
    transition: all 0.1s ease;
    margin-bottom: 8px;
    padding: 0 16px;
    border: 2px solid #bfc4ce;
    &:hover {
      border-color: #a3a9b8;
    }
    svg {
      margin-inline-end: 8px;
      color: #088bdd;
    }
    .arrow {
      display: flex;
      align-items: center;
      margin-inline-start: auto;

      &:dir(rtl) {
        transform: rotate(180deg);
      }
    }
    &.backToSavedButton {
      height: auto;
      svg {
        margin-right: 12px;
        width: 16px;
        height: 16px;
      }
    }
  }
}

.savedCardsTitle {
  margin-bottom: 6px;
  font-size: 14px;
  color: #0f133a;
  line-height: 20px;
}

.savedBilling {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #eff1f7;
  border-radius: 8px;
  padding: 17.5px 24px;
  font-size: 14px;
  line-height: 21px;
  color: $dark-dark-purple;
}

.imageSkeleton,
.numberSkeleton,
.dateSkeleton {
  position: relative;
  overflow: hidden;
  background: $skeleton;
  border-radius: 4px;
}

.imageSkeleton {
  width: 40px;
  margin-right: 8px;
  height: 25px;
}

.numberSkeleton {
  width: 55px;
  height: 20px;
}

.dateSkeleton {
  width: 45px;
  height: 20px;
}

.bottomSection {
  width: 100%;
  padding: 42px 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f5efff;
  margin-top: auto;
  @media (max-width: 1120px) {
    padding: 42px 24px;
  }
  @media (max-width: 975px) {
    flex-direction: column;
    align-items: flex-start;
  }
  .left {
    display: grid;
    grid-template-columns: 28px 1fr;
    grid-column-gap: 12px;
    align-items: center;
    justify-content: space-between;
    max-width: 620px;
    @media (max-width: 975px) {
      margin-bottom: 24px;
    }
    .message {
      font-size: 16px;
      font-weight: 400;
      line-height: 22px;
      margin: 0;
      margin-right: 24px;
      @media (max-width: 768px) {
        font-size: 14px;
        line-height: 21px;
      }
    }
    svg {
      width: 28px;
      height: 24px;
    }
  }
  .right {
    display: flex;
    align-items: center;
    justify-content: space-between;
    @media (max-width: 975px) {
      margin-left: auto;
    }
    .price {
      font-size: 24px;
      line-height: 36px;
      font-weight: 700;
      margin-right: 32px;
      @media (max-width: 768px) {
        font-size: 20px;
        line-height: 30px;
      }
    }
  }
}

.alternativeMethods {
  display: flex;
  flex-direction: column;
  //justify-content: space-between;
  //align-items: stretch;
  width: 100%;
  //grid-template-columns: auto auto;
  margin-top: 16px;
  //grid-column-gap: 18px;
  .loaderButton {
    background: $skeleton;
    position: relative;
    overflow: hidden;
    border-radius: 6px;
    width: 100%;
    height: 50px;
    margin-bottom: 8px;
    &:last-of-type {
      margin-bottom: 0px;
    }
  }
}

.appleButton {
  background: white;
  width: 100%;
  height: 50px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
}

.orDivider {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  margin: 24px 0px;
  grid-column-gap: 16px;
  font-size: 14px;
  .line {
    width: 100%;
    height: 1px;
    background-color: #bfc4ce;
  }
}
