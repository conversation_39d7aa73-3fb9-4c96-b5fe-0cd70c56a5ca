import { t } from "i18next";
import Button from "../Button";
import Modal from "../Modal";
import { ExclamationMark } from "../svgs";
import styles from "./delete-card-modal.module.scss";
import i18n from "../../i18n";

const DeleteCardModal = ({
  show,
  setShow,
  confirmDelete,
  loading,
  digits,
  result,
  handleAfterDeleteAction,
}: any) => {
  return (
    <Modal show={show} setShow={setShow} style={{ height: 400, maxWidth: 700 }}>
      {!result && (
        <div className={styles.main}>
          <div>
            <ExclamationMark />
          </div>
          <h5>{ t('account.paymentCards.removeCard') }</h5>
          <p>
            { t('account.paymentCards.deletingCardMsg') } •••• {digits} {i18n.language === 'ar' ? '؟' : '?'}
          </p>
          <div className={styles.buttons}>
            <Button
              loading={loading}
              onClick={confirmDelete}
              style={{ marginInlineEnd: 16 }}
            >
              { t('buttons.remove') }
            </Button>
            <Button
              color="secondary"
              onClick={() => {
                setShow(false);
              }}
              disabled={loading}
            >
              { t('buttons.cancel') }
            </Button>
          </div>
        </div>
      )}
      {result && (
        <div className={styles.main}>
          <div>
            <img src="/images-int/help/help_activating.svg" />
          </div>
          <h5>{result === "success" ? t('account.msgs.success') : t('buttons.oops')}</h5>
          <p>
            {result === "success"
              ? t('account.paymentCards.successRemove')
              : t('account.errors.somethingWrong') }
          </p>
          <div className={styles.buttons}>
            <Button loading={loading} onClick={handleAfterDeleteAction}>
              {result === "success" ? t('buttons.finish') : t('buttons.tryAgain')}
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default DeleteCardModal;
