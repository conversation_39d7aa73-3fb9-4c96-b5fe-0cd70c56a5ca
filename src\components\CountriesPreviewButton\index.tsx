import styles from "./countries-preview-button.module.scss";

const CountriesPreviewButton = ({ onClick }: any) => {
  return (
    <div className={styles.main} onClick={onClick}>
      <div className={styles.flags}>
        <div className={`${styles.flag} ${styles.flag1}`} />
        <div className={`${styles.flag} ${styles.flag2}`} />
        <div className={`${styles.flag} ${styles.flag3}`} />
      </div>
      <div className={styles.remaining}>+ 56</div>
    </div>
  );
};

export default CountriesPreviewButton;
