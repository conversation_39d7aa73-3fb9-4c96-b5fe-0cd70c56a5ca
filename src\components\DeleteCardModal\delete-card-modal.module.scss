@use "../../styles/theme.scss" as *;

.main {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50px;
  height: 100%;
  text-align: center;
  @media (max-width: 768px) {
    padding: 24px;
  }
  .image {
    img,
    svg {
      height: 120px;
      width: 120px;
    }
  }
  h5 {
    color: $secondary;
    margin: 0 0 24px 0;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
  }
  p {
    color: $dark-dark-purple;
    font-size: 16px;
    line-height: 24px;
    margin: 0 0 25px 0;
    @media (max-width: 768px) {
      margin: 0 0 30px 0;
    }
  }
}

.buttons {
  display: flex;
  align-items: center;
  @media (max-width: 768px) {
    flex-direction: column-reverse;
    width: 100%;
    button {
      width: 100%;
      margin: 0 0 12px 0 !important;
    }
  }
}
