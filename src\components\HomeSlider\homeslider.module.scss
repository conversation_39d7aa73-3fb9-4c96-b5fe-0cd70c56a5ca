.bgStyle {
  background-repeat: no-repeat;
  background-position: center;
  height: 600px;
  @media (max-width: 768px) {
    height: 350px;
  }
  @media (max-width: 500px) {
    height: 208px;
  }
  @media (min-width: 1500px) {
    height: 850px;
  }
}
.inner {
  width: 100%;
  height: 100%;
  padding: 100px 44px 100px 80px;
  position: relative;
  max-width: 1600px;
  margin: 0 auto;
  .appButtons {
    display: flex;
    @media (max-width: 500px) {
      justify-content: center;
    }
    a {
      border-radius: 25px;
      padding: 10px 22px;
      background-color: #0f133a;
    }
  }
  > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    @media (max-width: 500px) {
      flex-direction: column;
      text-align: center;
      justify-content: center;
    }
    button {
      @media (max-width: 500px) {
        margin: 0 auto;
      }
    }
  }
  @media (max-width: 768px) {
    padding: 50px 24px 35px 24px;
  }
  p {
    position: relative;
  }
  a {
    color: #fff;
    text-decoration: none;
  }
  .downloadTextWithArrow {
    position: relative;
    width: fit-content;
    @media (max-width: 500px) {
      width: auto;
    }
    img {
      position: absolute;
      right: -42%;
      left: auto;
      bottom: -35px;

      @media (max-width: 768px) {
        display: none;
      }

      &:dir(rtl) {
        transform: scale(-1, 1);
        right: auto;
        left: -42%;
      }
    }
  }
  .arrowAfter::after {
    content: url("/../images-int/home/<USER>");
    height: 20px;
    width: 20px;
    position: absolute;
    right: 49%;
    bottom: -3px;
    @media (max-width: 768px) {
      right: 0;
    }
    @media (max-width: 500px) {
      height: 15px;
      width: 15px;
    }

    &:dir(rtl) {
      transform: scale(-1, 1);
    }
  }
  h1 {
    font-size: 64px;
    font-weight: 800;
    margin: 0;
    @media (max-width: 768px) {
      font-size: 48px;
    }
    @media (max-width: 500px) {
      font-size: 30px;
    }
  }
  h2 {
    font-size: 48px;
    font-weight: 700;
    margin: 0;
    @media (max-width: 768px) {
      font-size: 35px;
    }
    @media (max-width: 500px) {
      font-size: 30px;
    }
  }
  .rightImageOne {
    width: 550px;
    @media (max-width: 1220px) {
      width: 210px;
    }
    @media (max-width: 500px) {
      width: 400px;
    }
  }
  .rightImageTwo {
    width: 400px;
    @media (max-width: 1220px) {
      width: 210px;
    }
    @media (max-width: 500px) {
      width: 300px;
    }
  }
}

.container:dir(rtl) {
  ul {
    text-align: left !important;
  }
}
