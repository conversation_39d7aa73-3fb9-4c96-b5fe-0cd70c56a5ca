import Button from "../Button";
import styles from "./payment-success.module.scss";
import Modal from "../Modal";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import { useEffect } from "react";

const PaymentSuccess = ({ show, trackPaymentFn }: any) => {
  useEffect(() => {
    trackPaymentFn()
  }, [])

  useEffect(() => {
    const img = document.createElement('img');
    img.src = "https://Visionadsmedia.o18.link/p?o=********&m=2088&t=i";
    img.width = 0;
    img.height = 0;
    
    // Append the image to the <head>
    const head = document.head || document.getElementsByTagName('head')[0];
    head.appendChild(img);
    
  }, [])

  const navigate = useNavigate();
  return (
    <Modal show={show} style={{ maxWidth: 490 }}>
      <div className={styles.container}>
        <img src="/images-int/payment/payment_success.svg" />
        <h4>{ t('account.msgs.awesome') }</h4>
        <p>{ t('account.checkout.successPayment') }</p>
        <Button
          style={{ height: 48, padding: "0px 80px" }}
          onClick={() => {
            navigate("/dashboard/home");
          }}
        >
          { t('general.continue') }
        </Button>
      </div>
    </Modal>
  );
};

export default PaymentSuccess;
