import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import styles from "./homeslider.module.scss";
import Slider from "react-slick";
import Button from "../Button";
import { useMediaQuery } from "@mui/material";
import { cmsURL } from "../../pages/api/cms-api";
import { useSelector } from "react-redux";
import { t } from "i18next";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useState } from "react";

const HomeSlider = ({data, toggleViewFn}: any) => {
  const [isDragging, setIsDragging] = useState(false);
  const { i18n } = useTranslation();
  
  const settings = {
    dots: true,
    infinite: true,
    speed: 1000,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 5000,
    rtl: i18n.language === 'ar',
    beforeChange: () => setIsDragging(true),
    afterChange: () => setIsDragging(false),
  };

  const under768 = useMediaQuery("(max-width: 768px)");
  const { isSmartBannerOpen } = useSelector((state: any) => state);
  const navigate = useNavigate()

  return (
    <div className={styles.container} style={{ marginTop: under768 && isSmartBannerOpen ? '65px' : 0 }}>
    <Slider {...settings}>
      {
        data && 
        data.map((slide:any, index:number) => (
          <div key={slide.id}>
            <div
              className={styles.bgStyle}
            style={{ backgroundImage: `url(${cmsURL + (under768 && slide.bgMobileVersion.data ? slide.bgMobileVersion.data?.attributes.url : slide.backgroundImg.data?.attributes.url)})`, 
              cursor: (slide.ctaLink && !slide.ctaText) && 'pointer',
              backgroundSize: under768 && slide.bgMobileVersion.data ? 'contain' : 'cover'
            }}
              onClick={ (e) => { 
                if (isDragging) {
                  e.preventDefault(); // Prevent click action if the slider is being dragged
                } else if (slide.ctaLink && !slide.ctaText) {
                  if (slide.ctaLink[0] === '/') {
                    navigate(slide.ctaLink)
                  } else if (slide.ctaLink === 'countries' || slide.ctaLink === 'regions') {
                    const element = document.getElementById('plans-section');
                    element?.scrollIntoView({
                      behavior: 'smooth'
                    });
                    toggleViewFn(slide.ctaLink)
                  }
                }
              }}>
              <div className={styles.inner}>
                {/* set direction here as slick slider sets 'ltr' from parent */}
                <div dir={i18n.dir()}>
                  <div style={{ marginTop: isSmartBannerOpen ? '50px' : '0' }}>
                    <h1>{slide.title}</h1>
                    <h2>{slide.subtitle}</h2>
                    <h4>{slide.about}</h4>
                    {
                      slide.ctaText && slide.ctaLink && (
                        <Button style={{ marginTop: 9 }} onClick={ () => { const element = document.getElementById(slide.ctaLink);
                          element?.scrollIntoView({
                            behavior: 'smooth'
                          });
                          toggleViewFn()
                        }}>
                          {slide.ctaText}
                        </Button>
                      )}
                      {(slide?.iOSButton || slide?.androidButton) && (
                        <>
                          <div className={styles.downloadTextWithArrow}>
                            <p>{t("general.downloadApp")}</p>
                            <img src="/../images-int/home/<USER>" />
                          </div>
                          <div className={styles.appButtons}>
                            {slide?.androidButton && (
                              <a href="https://play.google.com/store/apps/details?id=com.mobilise.orbit_mobile">
                                <img
                                  src="/images-int/app-stores/googleStoreNoBorder.svg"
                                  alt={`${t(
                                    "general.downloadApp"
                                  )} - Google play store`}
                                />
                              </a>
                            )}
                            {slide?.iOSButton && (
                              <a
                                href="https://apps.apple.com/us/app/orbit-mobile/id6738754899"
                                style={{ marginInlineStart: "15px" }}
                              >
                                <img
                                  src="/images-int/app-stores/appleStoreNoBorder.svg"
                                  alt={`${t(
                                    "general.downloadApp"
                                  )} - Apple store`}
                                />
                              </a>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                    <div>
                      {
                        slide.rightImg.data && (
                          <img
                            src={cmsURL + slide.rightImg.data?.attributes.url}
                            alt="Get Orbit Mobile app"
                            className={
                              index + 1 === data.length
                                ? styles.rightImageTwo
                                : styles.rightImageOne
                            }
                          />
                        )
                      }
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
      </Slider>
    </div>
  );
};

export default HomeSlider;
