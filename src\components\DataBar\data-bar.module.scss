.container {
  width: 100px;
  height: 100px;
  position: relative;
}
.amountContainer {
  width: 100%;
  height: 100%;
  border-radius: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 100;
}
.amount {
  font-size: 16px;
  font-weight: 700;
  margin: 0;
}
.available {
  margin: 0;
  font-size: 10px;
  @media (max-width: 750px) {
    font-size: 14px;
  }
}
.progressBar {
  position: absolute;
  width: 120px;
  height: 120px;
  top: -10px;
  left: -10px;
  transform: rotate(-90deg);
  z-index: 200;
}

.progress__meter,
.progress__value {
  fill: none;
}

.progress__meter {
  stroke: #efeeed;
}

.progress__value {
  /* stroke: blue; */
  stroke-linecap: round;
}
