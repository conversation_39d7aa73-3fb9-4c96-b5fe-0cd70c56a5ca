import styles from "./header.module.scss";
import Button from "../Button";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  List,
  LogOut,
  User,
  PaymentHistory,
  PaymentCard,
  Esim,
  Tick,
} from "../svgs";
import { useSelector, useDispatch } from "react-redux";
import { useEffect, useState } from "react";
import Menu from "../Menu";
import { Smartbanner } from "custom-react-smartbanner";
import LogoutModal from "../LogoutModal";
import { useMediaQuery } from "@mui/material";
import MobileNavMenu from "../MobileNavMenu";
import { useTranslation } from "react-i18next";
import $ from "jquery";
import { Banner } from "../Banner";

// Navigation Links - width is used to stop alignment jumps when active link is bold
export const navLinks = [
  {
    label: "store",
    link: "/",
  },
  {
    label: "howItWorks",
    link: "/how-it-works",
  },
  {
    label: "aboutus",
    link: "/about-us",
  },
  {
    label: "blog",
    link: "/blog",
  },
  {
    label: "help",
    link: "/help",
  },
];

const Header = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const { localesList, loggedIn, userInfo, isSmartBannerOpen } = useSelector(
    (state: any) => state
  );
  const [toggleLocale, setToggleLocale] = useState(false);
  const handleClose = () => dispatch({ type: "set", isSmartBannerOpen: false });
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const under750 = useMediaQuery("(max-width: 750px)");
  const under850 = useMediaQuery("(max-width: 850px)");
  const [top, setTop] = useState(0);
  const [lastScroll, setLastScroll] = useState(0);
  const [scroll, setScroll] = useState(0);

  useEffect(() => {
    if (under750) {
      document.addEventListener("scroll", function (e) {
        const st = window.pageYOffset;
        setScroll(st);
      });
    }
  }, [under750]);

  useEffect(() => {
    $(window).on("resize", () => {
      setTop(0);
    });
  }, []);

  useEffect(() => {
    if (under750) {
      const st = window.pageYOffset;
      if (Math.abs(lastScroll - st) <= 5) {
        return;
      }
      if (st > lastScroll && st > 100) {
        setTop(-100);
      } else {
        setTop(0);
      }
      setLastScroll(st);
    }
  }, [scroll]);

  const handleActiveLink = (link: any) => {
    if (
      location.pathname.length > 1 &&
      link.length > 1 &&
      location.pathname.includes(link)
    ) {
      return styles.active;
    } else if (
      location.pathname.length === 1 &&
      location.pathname.length === link.length
    ) {
      return styles.active;
    } else {
      return "";
    }
  };

  const handleLocale = (locale: string) => {
    i18n.changeLanguage(locale);
  };

  /*const openSearch = () => {
    setMobileMenuOpen(false);
    setShowSearch(true);
    const newurl =
      window.location.protocol +
      "//" +
      window.location.host +
      window.location.pathname +
      "?q=s";
    window.history.pushState({ path: newurl }, "", newurl);
    let queryStringChange = new Event("popstate");
    window.dispatchEvent(queryStringChange);
  };*/

  /*useEffect(() => {
    if (location.search === "?q=s") {
      setMobileMenuOpen(false);
      setShowSearch(true);
    } else {
      setShowSearch(false);
    }
  }, [location]);*/

  return (
    <>
      <MobileNavMenu
        open={mobileMenuOpen}
        setOpen={setMobileMenuOpen}
        setShowLogoutConfirm={setShowLogoutConfirm}
      />
      <LogoutModal show={showLogoutConfirm} setShow={setShowLogoutConfirm} />
      {/*<SearchWebsite show={showSearch} setShow={setShowSearch} />*/}
      <header
        className={`${styles.header} ${
          location.pathname.includes("dashboard") &&
          !under850 &&
          styles.dashboard
        }`}
        style={{
          top: top,
        }}
      >
        <div className={styles.smartbanner}>
          <Smartbanner
            isOpen={isSmartBannerOpen}
            onClose={handleClose}
            title={ t('general.orbit') }
            iconUrl="/images-int/logos/orbit_logo.svg"
            appleUrl="https://apps.apple.com/us/app/orbit-mobile/id6738754899"
            androidUrl="https://play.google.com/store/apps/details?id=com.mobilise.orbit_mobile"
            appleDescription={ t('general.appleDescription') }
            androidDescription={ t('general.androidDescription') }
            buttonLabel={ t('general.getApp') }
          />
        </div>
        <Banner />
        <div className={`max-width-container ${styles.main}`}>
          <Link to="/">
            <img
              src="/images-int/logos/orbit_logo.svg"
              alt="Orbit mobile logo"
              className={styles.logo}
            />
          </Link>
          <nav className={styles.linksContainer}>
            {navLinks.map((navLink: any, index) => (
              <div
                style={{
                  //width: navLink.width,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  marginRight: index !== navLinks.length - 1 ? 8 : 0,
                }}
                key={navLink.link}
                className={`${styles.link} ${handleActiveLink(navLink.link)}`}
              >
                <Link to={navLink.link}>{t("pages." + navLink.label)}</Link>
              </div>
            ))}
          </nav>
          {/*<button onClick={openSearch} className={styles.searchButton}>
              {!showSearch && (
                <motion.div
                  transition={{
                    type: "spring",
                    mass: 1,
                    stiffness: 80,
                    damping: 20,
                  }}
                  layoutId="mag-glass"
                  layoutRoot
                >
                  <MagnifyingGlass />
                </motion.div>
              )}
                </button>*/}
          {/*<Link
              style={{ textDecoration: "none", color: "inherit" }}
              to="/cart-summary"
            >
              <div className={styles.basket}>
                <Trolley />
                <span>{basket.length}</span>
              </div>
              </Link>*/}
          <div className={styles.mainButton + " flex"}>
            <div
              className={
                styles.localeSelect +
                " flex items-center justify-content-center"
              }
              onClick={() => setToggleLocale(!toggleLocale)}
            >
              <img src={`/images-int/locales/${i18n.language}.png`} />
              <span>
                {i18n.language === "en" ? i18n.language.toUpperCase() : "عربي"}
              </span>
              {toggleLocale && (
                <div>
                  {localesList.map((locale: any) => {
                    return (
                      <div
                        className={`${styles.locale} ${
                          i18n.language === locale && styles.active
                        } flex items-center`}
                        onClick={() => handleLocale(locale)}
                      >
                        <img src={`/images-int/locales/${locale}.png`} />
                        <span style={{ marginRight: "auto" }}>
                          {locale === "en" ? "English" : "عربي"}
                        </span>
                        {i18n.language === locale && <Tick />}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
            {loggedIn ? (
              <Menu
                menuClassName="dropdownnMenu"
                style={{ width: "100%" }}
                data={{
                  label: userInfo.firstName,
                  items: [
                    {
                      icon: <Esim />,
                      label: t("account.pages.myesims"),
                      onClick: () => {
                        navigate("/dashboard/home");
                      },
                    },
                    {
                      icon: <PaymentCard />,
                      label: t("account.pages.paymentCards"),
                      onClick: () => {
                        navigate("/dashboard/cards");
                      },
                    },
                    {
                      icon: <PaymentHistory />,
                      label: t("account.pages.paymentHistory"),
                      onClick: () => {
                        navigate("/dashboard/payments-history");
                      },
                    },
                    {
                      icon: <User />,
                      label: t("account.account"),
                      onClick: () => {
                        navigate("/dashboard/settings");
                      },
                    },
                    {
                      icon: <LogOut />,
                      label: t("account.logout"),
                      onClick: () => {
                        setShowLogoutConfirm(true);
                      },
                    },
                  ],
                }}
              />
            ) : (
              <Link
                style={{ textDecoration: "none", color: "inherit" }}
                to="/register"
              >
                <Button
                  style={{ position: "relative", zIndex: 10, width: "100%" }}
                  color="primary"
                >
                  {t("account.login")}/{t("account.register")}
                </Button>
              </Link>
            )}
          </div>
          <button
            className={styles.openMenu}
            onClick={() => {
              setMobileMenuOpen(true);
            }}
          >
            <List />
          </button>
        </div>
      </header>
    </>
  );
};

export default Header;
