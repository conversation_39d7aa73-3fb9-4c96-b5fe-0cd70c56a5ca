import styles from "./plan-tile.module.scss";
import Shimmer from "../Shimmer";

const PlanTileSkeleton = ({ index }: any) => {
  return (
    <div className={`${styles.planTile} ${styles[`color-${index % 5}`]}`}>
      <div className={styles.bottom}>
        <div>
          <div className={styles.box} style={{ marginBottom: 8, width: 150 }}>
            <Shimmer />
          </div>
        </div>
        <div>
          <div className={styles.box} style={{ height: 28, marginBottom: 24 }}>
            <Shimmer />
          </div>
        </div>
      </div>
      <div className={styles.button}>
        <Shimmer />
      </div>
    </div>
  );
};

export default PlanTileSkeleton;
