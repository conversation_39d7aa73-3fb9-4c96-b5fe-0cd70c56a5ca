@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 57px 50px;
  @media (max-width: 768px) {
    padding: 65px 24px 50px 24px;
  }
  h4 {
    color: $primary;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    margin: 0 0 81px 0;
    @media (max-width: 768px) {
      margin: 0 0 45px 0;
    }
  }
}

.cancelMain {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 81px 24px 50px 24px;
  text-align: center;
  @media (max-width: 768px) {
    padding: 65px 24px 50px 24px;
  }
  h4 {
    margin: 0 0 24px 0;
    color: $primary;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
  }
  p {
    margin: 0 0 65px 0;
  }
  .buttons {
    display: flex;
    @media (max-width: 768px) {
      flex-direction: column-reverse;
      width: 100%;
      button {
        width: 100%;
        margin: 0 0 16px 0 !important;
      }
    }
  }
}

.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 100px;
  align-items: center;
  justify-items: flex-end;
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    grid-row-gap: 40px;
    justify-items: center;
    width: 100%;
    .buttons {
      width: 100%;
    }
    button {
      width: 100%;
    }
  }
}

.picture {
  width: 232px;
  height: 232px;
  border-radius: 1000px;
  background-color: $primary;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-size: cover;
  background-position: center;
  overflow: hidden;
  video {
    height: 100%;
    position: relative;
    z-index: 1000;
    transform: scaleX(-1);
    @media (max-width: 900px) {
      width: 100%;
      height: auto;
    }
  }
  svg {
    width: 93px;
    height: 93px;
    position: absolute;
    z-index: 900;
  }
}

.close {
  position: absolute;
  width: 40px;
  height: 40px;
  top: 50px;
  right: 50px;
  cursor: pointer;
  @media (max-width: 768px) {
    top: 24px;
    right: 12px;
  }
  svg {
    width: 25px;
    height: 25px;
  }
}
