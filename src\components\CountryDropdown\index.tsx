/*
==========================================================================================

                                      CountryDropdown

Description: A select component for user to provide billing country

Parameters: cardData (obj) - billing info object, containing the state 
                                for selected country
            setcardData (func) - function to set billing info state
            disabled (boolean) - whether select input is disabled

Pages: /dashboard/add-data (AddCard component)
       /account (AddCard and EditBilling components)

==========================================================================================
*/

import styles from "./country-dropdown.module.scss";
import { useState, useEffect } from "react";
import Select from "react-select";
import { Fade } from "@mui/material";
import { useSelector } from "react-redux";

const CountryDropdown = ({
  cardData,
  setCardData,
  disabled,
  mobile,
  account,
}: any) => {
  const [countries, setCountries] = useState([] as any);

  const countriesRedux = useSelector((state: any) => state.countries);

  // Sets selected country in cardData
  const handleChange = (option: any) => {
    setCardData({
      ...cardData,
      country: option,
      errors: {
        ...cardData.errors,
        country: "",
      },
    });
  };

  useEffect(() => {
    if (countriesRedux.length) {
      let countryStore = [] as any;
      // Formats countries correctly for react-select component
      countriesRedux.forEach((country: any) => {
        countryStore.push({
          value: country.countryCode,
          label: country.countryName,
          flag: country.iconUrl,
        });
      });
      setCountries(countryStore);
    }
  }, [countriesRedux]);

  // Returns flag styles
  const flag = (src: any, faded = false) => {
    return {
      alignItems: "center",
      display: "flex",
      ":before": {
        content: '" "',
        width: 18,
        height: 13,
        backgroundImage: `url(${src.flag})`,
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        borderRadius: 2,
        backgroundPosition: "center",
        marginRight: 8,
        opacity: faded ? 0.7 : 1,
        backgroundColor: "#b7b7b7",
      },
    };
  };

  // Custom style object for react-select
  const customStyles = {
    option: (provided: any, state: any) => ({
      ...provided,
      fontWeight: 400,
      fontSize: 14,
      lineHeight: "24px",
      cursor: state.isSelected ? "auto" : "pointer",
      backgroundColor: state.isSelected ? "#7448b0" : "#fff",
      color: state.isSelected ? "#fff" : "#160b2a",
      ...flag(state.data),
      "&:hover": {
        backgroundColor: state.isSelected ? "#7448B0" : "#cebee3",
      },
    }),
    indicatorSeparator: () => ({
      display: "none",
    }),
    indicatorsContainer: () => ({
      display: "none",
    }),
    valueContainer: (provided: any, state: any) => ({
      ...provided,
      padding: 0,
    }),
    menu: (provided: any, state: any) => ({
      ...provided,
      zIndex: 1002,
      borderRadius: 0,
      border: "none",
      boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.15)",
    }),
    control: (provided: any, state: any) => ({
      ...provided,
      width: "100%",
      backgroundColor: "#EFF1F7",
      borderRadius: 12,
      padding: "0 14px",
      height: 47,
      fontSize: 14,
      lineHeight: "24px",
      fontWeight: 400,
      transition: "all 0.1s ease",
      caretColor: "#7448B0",
      boxShadow: "none !important",
      cursor: "text",
      border: state.isFocused
        ? "2px solid #7448B0 !important"
        : cardData.errors.country
        ? "2px solid #ff7378 !important"
        : "2px solid #EFF1F7 !important",
      "&:hover": {
        border: state.isFocused
          ? "2px solid #7448B0 !important"
          : cardData.errors.country
          ? "2px solid #ff7378 !important"
          : "2px solid #d6d6d6 !important",
      },
    }),
    singleValue: (provided: any, state: any) => ({
      ...provided,
      ...flag(state.getValue()[0], true),
      color: disabled
        ? "rgb(84, 84, 84)"
        : state.getValue()[0].value
        ? "#000"
        : "#757575",
      marginRight: "auto",
    }),
  };

  return (
    <div className={styles.inputContainer}>
      <Select
        options={countries}
        styles={customStyles}
        value={cardData.country}
        onChange={(option: any) => {
          handleChange(option);
        }}
        isDisabled={disabled}
      />
      <Fade in={cardData.errors.country !== ""}>
        <p className={styles.errorText} id="country-error">
          {cardData.errors.country || <br />}
        </p>
      </Fade>
    </div>
  );
};

export default CountryDropdown;
