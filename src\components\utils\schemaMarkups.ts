import CryptoJS from 'crypto-js';

export const organizationSchema = (data:any) => {
    return (
        {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Orbit Mobile",
            "url": "https://orbitmobile.com/",
            "logo": "https://www.orbitmobile.com/images-int/logos/orbit_logo.svg",
            "description": data?.metaDescription,
            "address": {
              "@type": "PostalAddress",
              "streetAddress": "123 Orbit Street",
              "addressLocality": "Riyadh",
              "addressRegion": "Your State",
              "postalCode": "12345",
              "addressCountry": "Saudi Arabia"
            },
            "contactPoint": {
              "@type": "ContactPoint",
              "telephone": "******-123-4567",
              "contactType": "Customer Service",
              "areaServed": "Worldwide",
              "availableLanguage": "English"
            },
            "offers": [
              {
                "@type": "Service",
                "serviceType": "ESIM services and packages",
                "provider": {
                  "@type": "Organization",
                  "name": "Orbit Mobile"
                }
              }
            ],
            "foundingDate": "2010",
            "sameAs": [
              "https://www.facebook.com/profile.php?id=61571769132219",
              "https://x.com/orbitmobilecare",
              "https://www.tiktok.com/@orbitmobiletravelesim",
              "https://www.instagram.com/orbitmobiletravelesim/",
              "https://snapchat.com/t/si13TdoN",
              "https://www.youtube.com/@orbitmobiletravelesim",
              "https://www.linkedin.com/company/orbit-mobile"
            ],
            "founders": [
              {
                "@type": "Person",
                "name": "John Doe"
              }
            ]
        }
    )
  };

export const faqSchema = (question: string, answer: string, slug?: string) => {
    return (
        {
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "url": "https://orbitmobile.com/help/" + slug,
            "mainEntity": {
              "@type": "Question",
              "name": question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": answer
              }
            }
        }
    )
}

export const policiesSchema = (type:string) => {
    return (
        {
            "@context": "https://schema.org/",
            "@type": "WebAPI",
            "name": "Orbit Mobile - Privacy policy",
            "description": "The Knowledge Graph Search API lets you find entities in the Google Knowledge Graph. The API uses standard schema.org types and is compliant with the JSON-LD specification.",
            "termsOfService": "https://orbitmobile.com/terms-and-conditions",
            "provider": {
              "@type": "Organization",
              "name": "Orbit Mobile"
            }
        }
    )
}

export const productSchema = (plans:any, url:string) => {
    return (
        {
            "@context": "https://schema.org",
            "@type": "ItemList",
            "name": `eSIM Plans for ${plans[0]?.planName}`,
            "itemListElement": plans?.map((plan:any) => ({
              "@type": "Product",
              "name": `${plan?.planName} - ${plan?.dataAllowance}GB - ${plan?.validity} days`,
              "sku": CryptoJS.SHA256(plan.planId).toString(CryptoJS.enc.Hex),
              "brand": {
                "@type": "Brand",
                "name": "Orbit Mobile"
              },
              "offers": {
                "@type": "Offer",
                "url": "https://orbitmobile.com/" + url,
                "priceCurrency": plan.prices[0].currencyCode,
                "price": plan.prices[0].cost,
                "areaServed": plan.countries.map((country:any) => ({
                  "@type": "Place",
                  "name": country.countryName,
                  "additionalType": "https://schema.org/Country"
                })),
                "priceSpecification": {
                  "@type": "PriceSpecification",
                  "price": plan.prices[0].cost,
                  "priceCurrency": plan.prices[0].currencyCide,
                }
              },
              "additionalType": "https://schema.org/Service",
            }))
          }
    )
}

export const countriesServedMarkUp = (countries:any, pageName:string, url:string) => {
  return (
    {
        "@context": "https://schema.org",
        "@type": "ItemList",
        "name": `eSIM services in Orbit Mobile ${pageName}`,
        "offers": {
          "@type": "Offer",
          "url": "https://orbitmobile.com/" + url,
          "areaServed": countries.map((country:any) => ({
            "@type": "Place",
            "name": country.countryName,
            "additionalType": "https://schema.org/Country"
            })),
          },
      }
)
}