@use "./theme.scss" as *;
@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Alexandria:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");

* {
  box-sizing: border-box;
  font-family: Montserrat, system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
}

:dir(rtl) {
  * {
    font-family: Alexandria, system-ui, -apple-system, BlinkMacSystemFont,
      "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans",
      "Helvetica Neue", sans-serif;
  }
}

body,
html {
  padding: 0;
  margin: 0;
  overscroll-behavior-x: none;
}

body {
  overflow-y: scroll;
}

h1, h2, h3, h4, h5, h5, p, div, span {
  white-space: pre-line;
}

.flex {
  display: flex;
}

@keyframes showChatWidget {
  0% { visibility: hidden; }
  100%   { visibility: visible; }
}

.fc-widget-normal {
  visibility: hidden;
  animation: showChatWidget 0s 2s forwards;
}

.title {
  font-size: 30px;
  font-weight: 800;
}
.MuiInputBase-root {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 3px;
  width: 60px;
  > div {
    text-align: center;
  }
}
.MuiTablePagination-actions {
  svg {
    width: 32px;
    height: 32px;
    color: rgba(0, 0, 0, 0.87);
  }
  .Mui-disabled {
    svg {
      color: rgba(0, 0, 0, 0.38);
    }
  }
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-around {
  justify-content: space-around;
}

.justify-content-center {
  justify-content: center;
}

.items-baseline {
  align-items: baseline;
}

.items-flexEnd {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.w-full {
  width: 100%;
}

.flex-wrap {
  flex-wrap: wrap;
}

.szh-menu {
  border-radius: 24px !important;
}

.flex-column {
  flex-direction: column;
}

.heading {
  font-weight: 700;
  font-size: 40px;
  line-height: 60px;
  color: #fff;
  margin: 0;
}

.heading-5 {
  font-weight: 800;
  font-size: 20px;
  line-height: 28px;
  color: #fff;
  margin: 0;
}

.body-text {
  font-weight: 400;
  font-size: 20px;
  line-height: 30px;
  color: #fff;
  margin: 0;
}

.hidden {
  display: none;
}

.max-width-container {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
}

.slick-slider {
  overflow: hidden;
}

.slick-dots {
  text-align: right !important;
  bottom: 0 !important;
  @media (max-width: 768px) {
    right: 40px !important;
  }
  @media (max-width: 500px) {
    right: 0px !important;
  }
  @media (max-width: 375px) {
    bottom: 10px !important;
  }
  @media (max-width: 320px) {
    bottom: 24px !important;
  }
}

.slick-dots li button:before {
  font-size: 11px !important;
}

.slick-dots li.slick-active button:before {
  color: rgba(8, 139, 221, 1) !important;
}

.slick-dots li button:before {
  color: #fff !important;
  opacity: 1 !important;
}

.howItWorks {
  .slick-dots {
    text-align: left !important;
    li {
      width: 8px;
    }
    button:before {
      color: rgba(255, 255, 255, 0.4) !important;
    }
    .slick-active button:before {
      color: rgba(5, 191, 190, 1) !important;
    }
  }
  > div:not(.slick-slider) {
    @media (max-width: 500px) {
      position: absolute;
      background: none;
      height: 100%;
    }
  }
  .slick-slider {
    > button {
      position: absolute;
      bottom: 0;
      right: 0;
      z-index: 99;
      &:first-of-type {
        right: 100px;
      }
      svg {
        stroke: #fff;
        color: #fff;
      }
    }
  }
}

@media (min-width: 991px) {
  #modal-scroll::-webkit-scrollbar {
    width: 25px;
  }

  /* Track */
  #modal-scroll::-webkit-scrollbar-track {
    border-radius: 0px 21px 21px 0;
  }

  /* Handle */
  #modal-scroll::-webkit-scrollbar-thumb {
    border: 8px solid rgba(0, 0, 0, 0);
    background-clip: padding-box;
    border-radius: 9999px;
    background-color: $primary;
    transition: background-color 0.2s ease;
  }

  /* Handle on hover */
  #modal-scroll::-webkit-scrollbar-thumb:hover {
    border: 8px solid rgba(0, 0, 0, 0);
    background-clip: padding-box;
    border-radius: 9999px;
    background-color: $secondary;
  }
}
#search-scroll {
  overflow-y: auto;
}

#search-scroll::-webkit-scrollbar {
  width: 25px;
}

/* Track */
#search-scroll::-webkit-scrollbar-track {
  background: #fff;
  border-radius: 0px 21px 21px 0;
}

/* Handle */
#search-scroll::-webkit-scrollbar-thumb {
  border: 8px solid rgba(0, 0, 0, 0);
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: $primary;
  transition: background-color 0.2s ease;
}

/* Handle on hover */
#search-scroll::-webkit-scrollbar-thumb:hover {
  border: 8px solid rgba(0, 0, 0, 0);
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: $secondary;
}

.blog-pagination {
  .Mui-disabled.MuiPaginationItem-previousNext {
    opacity: 0 !important;
  }
  .MuiPaginationItem-root {
    background: #efeeed;
    font-size: 18px;
    font-weight: 300;
    margin: 0 4px !important;
    @media (max-width: 335px) {
      margin: 0 2px !important;
    }
    &.MuiButtonBase-root:hover {
      background: rgba(55, 203, 202, 0.5);
    }
  }
  .Mui-selected.MuiPaginationItem-root {
    background: #37cbca !important;
    .MuiTouchRipple-root {
      display: none;
    }
  }
  .MuiPaginationItem-previousNext {
    width: 40px !important;
    height: 40px !important;
    background: none;
    @media (max-width: 530px) {
      width: 32px !important;
      height: 32px !important;
    }
    .MuiTouchRipple-root {
      display: none;
    }
    &:hover {
      background: #cebee3;
    }
    svg {
      width: 24px;
      height: 24px;
    }
  }
}

.fade-enter {
  opacity: 0;
}
.fade-exit {
  opacity: 1;
}
.fade-enter-active {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
}
.fade-enter-active,
.fade-exit-active {
  transition: opacity 200ms ease;
}

.quick-fade-enter {
  opacity: 0;
}
.quick-fade-exit {
  opacity: 1;
}
.quick-fade-enter-active {
  opacity: 1;
}
.quick-fade-exit-active {
  opacity: 0;
}
.quick-fade-enter-active,
.quick-fade-exit-active {
  transition: opacity 100ms ease;
}

.slide-top-enter {
  opacity: 0;
  transform: translateY(-100vh);
}
.slide-top-exit {
  opacity: 1;
  transform: translateY(0px);
}
.slide-top-enter-active {
  opacity: 1;
  transform: translateY(0px);
}
.slide-top-exit-active {
  opacity: 0;
  transform: translateY(-100vh);
}
.slide-top-enter-active,
.slide-top-exit-active {
  transition: transform 500ms ease, opacity 300ms ease;
}

/// szhsin menu
.menu .szh-menu-container {
  right: 0px !important;
  left: initial !important;
  .szh-menu {
    right: 0px !important;
    top: 13px !important;
    left: initial !important;
    border-radius: 12px;
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.15);
    padding: 24px 32px 0 24px !important;
  }
}

.country-select {
  .szh-menu-container {
    z-index: 9010;
  }
  .szh-menu {
    border-radius: 32px;
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.15);
    padding: 32px !important;
    margin: 24px !important;
  }
}

.plans-carousel .swiper-wrapper {
  align-items: center;
}

.credit-carousel .swiper-slide {
  justify-content: center;
  display: flex;
}

.credit-carousel .swiper {
  overflow-y: visible;
}

.plans-carousel .swiper {
  overflow-y: visible;
  padding: 50px 0 30px 0;
}

input,
textarea,
button,
select,
a {
  -webkit-tap-highlight-color: transparent;
}

#cookie-anchor {
  position: relative;
  top: -150px;
}

apple-pay-button {
  --apple-pay-button-width: 100%;
  --apple-pay-button-height: 50px;
  --apple-pay-button-border-radius: 8px;
  --apple-pay-button-padding: 0px;
  --apple-pay-button-box-sizing: border-box;
}

#fc_widget {
  .custom-icon {
    width: 50px !important;
  }
}

#fc_frame:dir(rtl) {
  right: auto !important;
  left: 15px !important;
}
