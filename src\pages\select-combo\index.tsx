import { useParams } from "react-router-dom";
import PlanSelection from "../../components/PlanSelection";
import { useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { ApiGet } from "../api/api";
import { isNumeric } from "../../components/utils/CardDetailsCheckers";

const SelectCombo = () => {
  const { countryCode } = useParams();

  const [countryPlans, setCountryPlans] = useState([]);

  const sortPlans = (a: any, b: any) => {
    const aCost = a.prices[0].cost,
      bCost = b.prices[0].cost,
      aData = a.dataAllowance,
      bData = b.dataAllowance,
      aMins = a.voiceAllowance,
      bMins = b.voiceAllowance,
      aSms = a.smsAllowance,
      bSms = b.smsAllowance,
      aValid = a.validity,
      bValid = b.validity;

    if (aCost === bCost) {
      if (aData === bData) {
        if (aMins === bMins) {
          if (aSms === bSms) {
            if (aValid === bValid) {
              return 0;
            } else {
              return aValid - bValid;
            }
          } else {
            return aSms - bSms;
          }
        } else {
          return aMins - bMins;
        }
      } else {
        return aData - bData;
      }
    } else {
      return aCost - bCost;
    }
  };

  useEffect(() => {
    ApiGet(
      `/plans?planType=combo&${
        isNumeric(countryCode) ? "zoneId" : "countryCode"
      }=${countryCode}`
    )
      .then((response) => {
        console.log(response);
        setCountryPlans(response.data.comboPlans.sort(sortPlans));
      })
      .catch((error) => {
        console.log(error);
      });
  }, [countryCode]);

  return (
    <PlanSelection
      planTypes={countryPlans}
      pageTitle="Combos"
      pageText="Select the plan that will give you the most freedom with connectivity."
    />
  );
};

export default SelectCombo;
