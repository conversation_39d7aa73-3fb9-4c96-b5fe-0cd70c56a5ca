@use "../../styles/theme.scss" as *;

.container {
  position: relative;
  display: flex;
  justify-content: center;
  &:hover {
    .tooltip {
      opacity: 1;
    }
  }
}

.tooltip {
  position: absolute;
  padding: 24px;
  z-index: 999;
  background: #fff;
  color: $dark-dark-purple;
  font-size: 16px;
  line-height: 24px;
  border-radius: 12px;
  bottom: calc(100% + 20px);
  width: 255px;
  opacity: 0;
  pointer-events: none;
  transition: all 0.1s ease;
  text-align: start;
  box-shadow: 0px 0px 20px rgba(62, 29, 107, 0.2);
  svg {
    position: absolute;
    bottom: -14px;
    margin-left: auto;
    margin-right: auto;
    left: 0;
    right: 0;
  }
}
