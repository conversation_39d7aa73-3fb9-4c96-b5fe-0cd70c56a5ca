import styles from "../../styles/register.module.scss";
import Button from "../../components/Button";
import { Input } from "../../components/Input";
import { useState, useEffect } from "react";
import jwtDecode from "jwt-decode";
import { Helmet } from "react-helmet-async";
import {
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  clearInput,
} from "../../components/utils/InputHandlers";
import { validateAll } from "indicative/validator";
import { ApiPostAuth } from "../../pages/api/api";
import auth from "../../components/utils/firebaseAuth";
import { useNavigate, useSearchParams, useLocation } from "react-router-dom";
import Modal from "../../components/Modal";
import { CrossWithCircle } from "../../components/svgs";
import { t } from "i18next";
import { signInWithEmailAndPassword, updatePassword } from "firebase/auth";

const passwordFields = ["newPassword", "confirmNewPassword"];
const passwordRules = getRules(passwordFields);
const passwordMessages = getMessages(passwordFields);

const ResetPassword = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState(null as any);
  const [searchParams, setSearchParams] = useSearchParams();
  const [title, setTitle] = useState("reset");
  const [passwordData, setPasswordData] = useState(
    createStateObject(passwordFields)
  );
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState<"success" | "failure">("failure");

  useEffect(() => {
    console.log(location.pathname);
    const token =
      location.pathname === "/reset-password"
        ? searchParams.get("auth")
        : searchParams.get("token");
    if (location.pathname === "/set-password") {
      setTitle("setPassword");
    }
    if (!token) {
      navigate("/");
    }
  }, []);

  const handleResetPasswordFromFirebase = async () => {
    setLoading(true);
    const token =
      location.pathname === "/reset-password"
        ? searchParams.get("auth")
        : searchParams.get("token");
    let decoded: any = null;
    if (token) {
      decoded = jwtDecode(token);
    }
    try {
      if (Object.keys(decoded.info).length === 1) {
        signInWithEmailAndPassword(
          auth,
          decoded.email,
          passwordData.newpassword
        )
          .then(() => {})
          .catch((error) => {
            const errorCode = error.code;
            const errorMessage = error.message;
            console.log(errorCode, errorMessage);
          });
      } else {
        await updatePassword(auth.currentUser, passwordData.newPassword);
      }
    } catch (err) {
      console.log(err);
      setLoading(false);
    }
  };

  const resetPassword = () => {
    const token =
      location.pathname === "/reset-password"
        ? searchParams.get("auth")
        : searchParams.get("token");
    const data = {
      newPassword: passwordData.newPassword,
      confirmNewPassword: passwordData.confirmNewPassword,
    };
    validateAll(data, passwordRules, passwordMessages)
      .then((response) => {
        if (data.newPassword !== data.confirmNewPassword) {
          setPasswordData({
            ...passwordData,
            errors: {
              password: "",
              confirmPassword: "Passwords do not match",
            },
          });
        } else {
          setLoading(true);
          if (location.pathname === "/set-password") {
            ApiPostAuth("/users/set-password", {
              token,
              password: data.newPassword,
              confirmPassword: data.confirmNewPassword,
            })
              .then(async (response) => {
                await handleResetPasswordFromFirebase();
                setLoading(false);
                setShowModal(true);
                setModalType("success");
              })
              .catch((error) => {
                setLoading(false);
                setShowModal(true);
                setModalType("failure");
              });
          } else {
            ApiPostAuth("/users/resetpassword", {
              token,
              password: data.newPassword,
              confirmPassword: data.confirmNewPassword,
            })
              .then(async (response) => {
                await handleResetPasswordFromFirebase();
                setLoading(false);
                setShowModal(true);
                setModalType("success");
              })
              .catch((error) => {
                setLoading(false);
                setShowModal(true);
                setModalType("failure");
              });
          }
        }
      })
      .catch((errors) => {
        console.log(errors);
        displayErrors(errors, setPasswordData);
      });
  };

  const handleModalAction = () => {
    if (modalType === "success") {
      navigate("/login");
    }
    setShowModal(false);
  };

  return (
    <div className={styles.finishReg}>
      <Helmet>
        <title>
          {t("general.orbit")}| {t("pages.resetPassword")}
        </title>
        <meta name="description" content={t("account.msgs.changePassword")} />
      </Helmet>
      <h2>{title === "reset" ? t("account.resetPassword") : "Set Password"}</h2>
      <p className={styles.text}>{t("account.msgs.changePassword")}</p>
      <div className={styles.inputs}>
        {passwordFields.map((prop: any) => {
          return (
            <Input
              label={t("placeholders." + prop)}
              value={passwordData[prop]}
              onChange={(e: any) => {
                handleInputChange(prop, e, passwordData, setPasswordData);
              }}
              error={passwordData.errors[prop]}
              clear={() => {
                clearInput(prop, passwordData);
              }}
              key={`input-${prop}`}
              password
              disabled={loading}
            />
          );
        })}
      </div>
      <Button color="primary" onClick={resetPassword}>
        {t("account.resetPassword")}
      </Button>
      <Modal
        show={showModal}
        style={{ height: 320, maxWidth: 500, padding: 25 }}
      >
        <div className="flex justify-content-center flex-column items-center">
          {modalType === "success" ? (
            <img src="/images-int/help/help_activating.svg" />
          ) : (
            <CrossWithCircle />
          )}
          <h2>
            {modalType === "success"
              ? t("account.msgs.congratulations")
              : t("buttons.oops")}
          </h2>
          <p>
            {modalType === "success"
              ? t("account.msgs.successPassword")
              : t("account.errors.somethingWrong")}
          </p>
          <Button color="primary" onClick={() => handleModalAction()}>
            {modalType === "success"
              ? t("general.continue")
              : t("buttons.tryAgain")}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default ResetPassword;
