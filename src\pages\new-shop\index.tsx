import { useEffect, useState } from "react";
import styles from "../../styles/new-shop.module.scss";
import CountrySelect from "../../components/CountrySelect";
import { ApiGet, ApiPostAuth } from "../api/api";
import PlanTile from "../../components/PlanTile";
import { Alert } from "../../components/svgs";
import { formatPrice } from "../../components/utils/formatPrice";
import Button from "../../components/Button";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Collapse, Fade } from "@mui/material";
import PlanTileSkeleton from "../../components/PlanTileSkeleton";
import NumberTypeSwitch from "../../components/NumberTypeSwitch";
import SelectDropdown from "../../components/SelectDropdown";
import CreditSkeleton from "../../components/CreditSkeleton";
import { v4 as uuidv4 } from "uuid";
import Tooltip from "../../components/Tooltip";
import { t } from "i18next";

const NewShop = () => {
  const dispatch = useDispatch();

  const { loggedIn } = useSelector((state: any) => state);

  const [plans, setPlans] = useState([] as any);

  const [selectedPlan, setSelectedPlan] = useState(null as any);

  const [countryOptions, setCountryOptions] = useState([] as any);
  const [regionOptions, setRegionOptions] = useState([] as any);
  const [cityOptions, setCityOptions] = useState([] as any);

  const [selectedRegion, setSelectedRegion] = useState(null as any);
  const [selectedCity, setSelectedCity] = useState(null as any);

  const [countryInfo, setCountryInfo] = useState(null as any);

  const { planType, countryCode } = useParams();

  const [plansLoading, setPlansLoading] = useState(false);

  const [numberType, setNumberType] = useState("mobile");

  const navigate = useNavigate();

  const planTypeKeys = {
    combo: "comboPlans",
    esim: "esimPlans",
    vn: "didPlans",
    credit: "creditPlans",
  } as any;

  const planTypeLabels = {
    combo: "Combo Plans",
    esim: "Data Plans",
    vn: "Virtual Phone Number Plans",
    credit: "Worldwide Credit",
  } as any;

  useEffect(() => {
    ApiGet("/plans/countries")
      .then((response: any) => {
        const options = response.data.countries.map((country: any) => ({
          key: country,
          displayLabel: (
            <div className={styles.countryDisplay}>
              <div
                className={styles.flag}
                style={{ backgroundImage: `url(${country.iconUrl})` }}
              />
              {country.countryName}
            </div>
          ),
        }));
        setCountryOptions(options);
        setCountryInfo(
          response.data.countries.find(
            (country: any) => country.countryCode === countryCode
          )
        );
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  const sortPlans = (a: any, b: any) => {
    const aCost = a.prices[0].cost,
      bCost = b.prices[0].cost,
      aMins = a.voiceAllowance,
      bMins = b.voiceAllowance,
      aSms = a.smsAllowance,
      bSms = b.smsAllowance,
      aValid = a.validity,
      bValid = b.validity;

    if (aCost === bCost) {
      if (aMins === bMins) {
        if (aSms === bSms) {
          if (aValid === bValid) {
            return 0;
          } else {
            return aValid - bValid;
          }
        } else {
          return aSms - bSms;
        }
      } else {
        return aMins - bMins;
      }
    } else {
      return aCost - bCost;
    }
  };

  useEffect(() => {
    if (countryCode) {
      setPlansLoading(true);
      ApiGet(`/plans?countryCode=${countryCode}&planType=${planType}`)
        .then((response) => {
          setPlans(response.data[planTypeKeys[planType!]].sort(sortPlans));
          setPlansLoading(false);
        })
        .catch((error) => {
          navigate(`/shop/select-plan/${countryCode}`);
        });

      ApiGet(`/plans/countries/${countryCode}/regions`).then((response) => {
        if (countryCode === "CA" || countryCode === "US") {
          setRegionOptions(
            response.data.regions.map((region: any) => ({
              value: region.id,
              label: region.name,
            }))
          );
        } else {
          ApiPostAuth("/plans/countries/cities", {
            id: response.data.country.id,
            citiesRequestType: "country",
          }).then((response) => {
            setCityOptions(
              response.data.cities.map((city: any) => ({
                value: city.id,
                label: city.name,
              }))
            );
          });
        }
      });
    }
  }, [countryCode]);

  useEffect(() => {
    if (selectedRegion) {
      setSelectedCity(null);
      setCityOptions([]);
      ApiPostAuth("/plans/countries/cities", {
        id: selectedRegion.value,
        citiesRequestType: "region",
      }).then((response) => {
        setCityOptions(
          response.data.cities.map((city: any) => ({
            value: city.id,
            label: city.name,
          }))
        );
      });
    }
  }, [selectedRegion]);

  const getPlanType = (type: any) => {
    if (type === "combo") {
      return "COMBO";
    } else if (type === "esim") {
      return "DATA";
    } else if (type === "vn") {
      return "PHONE";
    } else if (type === "credit") {
      return "CREDIT";
    }
  };

  const handleCheckout = () => {
    let planToAdd = { ...selectedPlan };
    planToAdd.iconUrl = countryInfo.iconUrl;
    planToAdd.countryName = countryInfo.countryName;
    planToAdd.numberType = numberType;
    planToAdd.basketId = uuidv4();
    planToAdd.planType = getPlanType(planType);
    planToAdd.autoRenew = false;
    if (selectedCity) {
      planToAdd.cityId = selectedCity.value;
    }
    if (planType === "vn") {
      if (numberType === "landline") {
        planToAdd.didType = "LOCAL";
      } else if (numberType === "mobile") {
        planToAdd.didType = "MOBILE";
      }
    } else {
      planToAdd.didType = "NA";
    }

    let newBasket = planToAdd;

    localStorage.setItem("basket", JSON.stringify(newBasket));

    dispatch({
      type: "set",
      basket: newBasket,
    });

    if (loggedIn) {
      navigate("/cart-summary");
    } else {
      navigate("/register");
    }
  };

  useEffect(() => {
    if (planType === "vn" && selectedPlan) {
      const active = plans.find(
        (item: any) => item.planId === selectedPlan.planId
      );
      if (!active.hasMobileNumber && numberType === "mobile") {
        setNumberType("landline");
      } else if (!active.hasLocalNumber && numberType === "landline") {
        setNumberType("mobile");
      }
    }
  }, [selectedPlan]);

  return (
    <>
      <div className="max-width-container">
        <div className={styles.mainContainer}>
          <div className={styles.planType}>
            {planTypeLabels[planType!]}
            <span className={styles.divider}>/</span>
            <div className={styles.countryName}>
              <div
                className={styles.flag}
                style={{ backgroundImage: `url(${countryInfo?.iconUrl})` }}
              />
              <div>{countryInfo?.countryName}</div>
            </div>
          </div>
          <h1 className={styles.heading}>Select a Plan</h1>
          <h4 className={styles.subHeading}>
            {planType === "credit"
              ? "Select the amount of credit you need"
              : "Select a Country and a plan that fits your needs."}
          </h4>
          {planType !== "credit" && (
            <div
              className={`${styles.selectsContainer} ${
                planType === "vn"
                  ? countryCode === "CA" || countryCode === "US"
                    ? styles.fourCol
                    : styles.twoCol
                  : planType === "combo"
                  ? countryCode === "CA" || countryCode === "US"
                    ? styles.threeCol
                    : styles.oneCol
                  : styles.oneCol
              }`}
            >
              {planType === "vn" && (
                <NumberTypeSwitch
                  type={numberType}
                  setType={setNumberType}
                  disabled={
                    selectedPlan &&
                    ((numberType === "mobile" &&
                      !selectedPlan?.hasLocalNumber) ||
                      (numberType === "landline" &&
                        !selectedPlan?.hasMobileNumber))
                  }
                />
              )}
              <CountrySelect
                label="Country"
                options={countryOptions}
                onSelect={(item: any) => {
                  navigate(`/shop/${planType}/${item.key.countryCode}`);
                }}
                selected={countryCode}
              />
              {(planType === "vn" || planType === "combo") &&
                (countryCode === "CA" || countryCode === "US") && (
                  <>
                    <SelectDropdown
                      placeholder="Select a Region"
                      label="Region"
                      value={selectedRegion}
                      options={regionOptions}
                      onChange={(option: any) => {
                        setSelectedRegion(option);
                      }}
                      dropDownMaxHeight={500}
                      disabled={regionOptions.length === 0}
                    />
                    <SelectDropdown
                      placeholder="Select a City"
                      label="City"
                      value={selectedCity}
                      options={cityOptions}
                      onChange={(option: any) => {
                        setSelectedCity(option);
                      }}
                      dropDownMaxHeight={500}
                      disabled={cityOptions.length === 0}
                    />
                  </>
                )}
            </div>
          )}
          {(plansLoading || plans.length > 0) &&
            (planType === "credit" ? (
              <div className={styles.creditContainer}>
                {plansLoading
                  ? Array.from({ length: 5 }).map((x) => <CreditSkeleton />)
                  : plans.map((plan: any) => (
                      <div
                        className={`${styles.credit} ${
                          selectedPlan?.planId === plan.planId && styles.active
                        }`}
                        onClick={() => {
                          if (selectedPlan?.planId === plan.planId) {
                            setSelectedPlan(null);
                          } else {
                            setSelectedPlan(plan);
                          }
                        }}
                      >
                        {plan.prices[0].currencySymbol}
                        {plan.prices[0].cost.toString().includes(".")
                          ? formatPrice(plan.prices[0].cost)
                          : plan.prices[0].cost}
                      </div>
                    ))}
              </div>
            ) : (
              <div className={styles.plansContainer}>
                {plansLoading
                  ? Array.from({ length: 8 }).map((x) => <PlanTileSkeleton />)
                  : plans.map((plan: any) => (
                      <PlanTile
                        plan={plan}
                        active={selectedPlan?.planId === plan.planId}
                        onClick={() => {
                          if (selectedPlan?.planId === plan.planId) {
                            setSelectedPlan(null);
                          } else {
                            setSelectedPlan(plan);
                          }
                        }}
                      />
                    ))}
              </div>
            ))}
        </div>
      </div>
      <Fade in={selectedPlan}>
        <div className={styles.bottomSection}>
          <div className={styles.left}>
            <Alert />
            <p className={styles.message}>
              To avoid an error message, make sure that your device is not tied
              to a specific network provider and that your phone is{" "}
              <a
                target="_blank"
                href="https://gistmobile.com/esim-compatibility/"
                style={{ color: "#000" }}
              >
                eSIM-compatible
              </a>
            </p>
          </div>
          <div className={styles.right}>
            <div className={styles.price}>
              Total {selectedPlan && selectedPlan?.prices[0].currencySymbol}
              {selectedPlan
                ? formatPrice(selectedPlan?.prices[0].cost)
                : "0.00"}
            </div>
            <Tooltip
              show={
                !selectedCity &&
                (planType === "vn" || planType === "combo") &&
                (countryCode === "CA" || countryCode === "US")
              }
              text="Please select a Region and City"
              style={{ textAlign: "center" }}
            >
              <Button
                disabled={
                  !selectedPlan ||
                  (!selectedCity &&
                    (planType === "vn" || planType === "combo") &&
                    (countryCode === "CA" || countryCode === "US"))
                }
                style={{ height: 56, padding: "0 26px" }}
                onClick={handleCheckout}
              >
                { t('general.continue') }
              </Button>
            </Tooltip>
          </div>
        </div>
      </Fade>
    </>
  );
};

export default NewShop;
