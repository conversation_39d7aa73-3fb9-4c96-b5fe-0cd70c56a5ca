/*
==========================================================================================

                                        Checkbox

Description: Reusable checkbox component

Parameters: checked (bool) - whether checkbox is checked or not
            onClick (func) - handle click on checkbox
            error (str) - inline error message
            size (num) - default = 27 - size of box in px
            disabled (bool) - whether checkbox is disabled or not

==========================================================================================
*/

import styles from "./checkbox.module.scss";
import { Fade } from "@mui/material";

const Checkbox = ({
  checked,
  onClick,
  disabled,
  indeterminate,
  id = "",
}: any) => {
  return (
    <div className={styles.container}>
      <input
        type="checkbox"
        className={styles.checkbox}
        onChange={() => {}}
        checked={checked}
        disabled={disabled}
      />
      <div
        className={`${styles.box} ${indeterminate && styles.indeterminate} ${
          disabled && styles.disabled
        } ${checked && styles.checked}`}
        onClick={(e) => {
          e.stopPropagation();
          !disabled && onClick();
        }}
        id={id}
      >
        <div className={`${styles.tick} ${checked && styles.tickChecked}`}>
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M3 8.26667L6.33333 12L13 4"
              stroke="#7448B0"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default Checkbox;
