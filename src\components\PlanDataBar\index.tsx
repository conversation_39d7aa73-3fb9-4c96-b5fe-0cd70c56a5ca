import { useEffect, useState } from "react";
import styles from "./plan-data-bar.module.scss";

const PlanDataBar = ({ Icon, displayText, percentage, noMargin }: any) => {
  return (
    <div className={`${styles.container} ${noMargin && styles.noMargin}`}>
      <Icon />
      <div className={styles.barContainer}>
        <div className={styles.bar} style={{ width: percentage }} />
        <div className={styles.text}>{displayText}</div>
      </div>
    </div>
  );
};

export default PlanDataBar;
