@use "./theme.scss" as *;

.main {
  padding: 22px 80px;
  width: 100%;
  min-height: calc(100vh - 100px);
  @media (max-width: 850px) {
    padding: 24px 16px;
  }
}

.dashGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  position: relative;
  @media (max-width: 850px) {
    display: flex;
    flex-direction: column;
  }
}

.sidebar {
  background: #eff1f7;
  border-radius: 24px 0px 0px 24px;
  margin: 26px 0;
  padding: 24px;
  padding-bottom: 50px;
  @media (max-width: 850px) {
    border-radius: 24px;
    margin: 0 auto;
    margin-bottom: 42px;
    padding: 24px 24px 0 24px;
    width: 100%;
    max-width: 380px;
    overflow: hidden;
  }

  .profile {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 12px;
    @media (max-width: 850px) {
      flex-direction: row;
    }
    .userInfo {
      text-align: center;
      @media (max-width: 850px) {
        text-align: left;
        margin-left: 16px;
      }
    }
    .name {
      color: $dark-dark-purple;
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      margin-bottom: 4px;
      min-height: 24px;
      word-break: break-all;
    }
    .simNo {
      font-size: 14px;
      line-height: 21px;
      color: $dark-dark-purple;
    }
  }

  .globalCredit {
    background-color: #dde1ee;
    padding: 16px;
    border-radius: 16px;
    margin-bottom: 16px;
    .top {
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      margin-bottom: 4px;
    }
    .creditTitle {
      font-size: 12px;
      line-height: 18px;
    }
    .creditAmount {
      font-size: 18px;
      font-weight: 700;
      line-height: 27px;
    }
  }

  .sectionName {
    display: flex;
    align-items: center;
    color: $dark-dark-purple;
    cursor: pointer;
    padding: 15px 12px;
    border-radius: 50px;
    &.active {
      background-color: #dde1ee;
      color: $secondary;
      .arrow {
        transform: rotate(180deg);
      }
      .nameText {
        font-weight: 600;
      }
    }
    .arrow {
      margin-left: auto;
      transition: all 0.2s ease;
    }
    .nameText {
      margin-left: 12px;
      font-size: 16px;
      line-height: 24px;
    }
    svg {
      width: 24px;
      height: 24px;
      vertical-align: middle;
    }
  }

  .bundles,
  .wallet,
  .settings,
  .rates {
    @media (max-width: 850px) {
      display: none;
    }
  }

  .bundles,
  .wallet,
  .rates {
    margin-bottom: 4px;
    .subsectionContainer {
      padding: 15px 0;
      padding-left: 36px;
      .subsection {
        display: flex;
        align-items: center;
        height: 48px;
        cursor: pointer;
        &.active {
          color: $secondary;
          .subsectionName {
            font-weight: 600;
            color: $secondary;
          }
        }
        svg {
          width: 20px;
          height: 20px;
          margin-right: 12px;
        }
        .subsectionName {
          color: $dark-dark-purple;
          font-size: 12px;
          line-height: 18px;
        }
        .flag {
          width: 24px;
          height: 24px;
          border-radius: 40px;
          margin-right: 12px;
          background-position: center;
          background-size: cover;
          background-color: #aaaaaa;
        }
      }
    }
  }
}

.dashPlans {
  background: #ffffff;
  box-shadow: 0px 0px 40px rgba(22, 11, 42, 0.2);
  border-radius: 24px;
  width: 100%;
  padding: 50px;
  @media (max-width: 850px) {
    padding: 0px;
    box-shadow: none;
    border-radius: 0px;
    background: none;
  }
}

.mobileMenu {
  width: 100%;
  bottom: 0px;
  height: 44px;
  display: none;
  align-items: center;
  justify-content: space-evenly;
  margin-bottom: 27px;
  margin-top: 3px;
  .link {
    display: flex;
    align-items: center;
    color: $dark-dark-purple;
    font-size: 14px;
    position: relative;
    padding: 10px 12px;
    border-radius: 100px;
    &.active {
      color: $secondary;
      font-weight: 600;
      background: $light-primary;
    }
    svg {
      vertical-align: middle;
      margin-right: 8px;
    }
  }
  @media (max-width: 850px) {
    display: flex;
  }
  @media (max-width: 540px) {
    justify-content: space-between;
  }
}

.walletSwitch {
  display: none;
  margin-bottom: 16px;
  height: 45px;
  .walletLink {
    color: $dark-dark-purple;
    position: relative;
    font-size: 14px;
    line-height: 21px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 16px;
    text-align: center;
    border-radius: 100px;
    transition: background-color 0.3s ease;
    &.active {
      font-weight: 600;
      color: $secondary;
      background-color: $light-primary;
    }
    .label {
      position: relative;
      z-index: 20;
    }
  }
  @media (max-width: 850px) {
    display: flex;
  }
}
