import Modal from "../Modal";
import { Close, Trash } from "../svgs";
import {
  clearInput,
  createStateObject,
  getMessages,
  getRules,
  handleInputChange,
} from "../utils/InputHandlers";
import styles from "./edit-card-modal.module.scss";
import { placeholders } from "../utils/InputHandlers";
import { Input } from "../Input";
import { useEffect, useState } from "react";
import Button from "../Button";
import BillingDetails from "../AddCardSections/BillingDetails";
import { useSelector } from "react-redux";
import { useMediaQuery } from "@mui/material";
import { t } from "i18next";

// Set up input data for card
const cardFields = ["cardholderName", "expiry"];
const cardRules = getRules(cardFields);
const cardMessages = getMessages(cardFields);

// Set up input data for billing details
const billingFields = [
  "name",
  "address",
  "city",
  "postcode",
  "country",
  "countryCode",
  "phoneNumber",
];
const billingRules = getRules(billingFields);
const billingMessages = getMessages(billingFields);

const EditCardModal = ({ show, setShow, card, handleDeleteFromEdit }: any) => {
  const [cardData, setCardData] = useState(createStateObject(cardFields));
  const [billingData, setBillingData] = useState(
    createStateObject(billingFields)
  );

  const [loading, setLoading] = useState(false);

  const { countries } = useSelector((state: any) => state);

  const [editBilling, setEditBilling] = useState(false);

  const saveChanges = () => {};

  useEffect(() => {
    if (card) {
      resetCard();
      resetBilling();
    }
  }, [card]);

  const resetCard = () => {
    setCardData({
      ...cardData,
      cardholderName: card.cardholderName,
      expiry: card.expiry,
    });
  };

  const resetBilling = () => {
    setBillingData({
      ...billingData,
      name: card.cardholderName,
      address: card.addressOne,
      city: card.city,
      postcode: card.postcode,
      country: {
        value: card.country,
        label: countries.find((item: any) => item.countryCode === card.country)
          .countryName,
        flag: `https://public-trill-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${card.country}.png`,
      },
      countryCode: card.phoneCountryCode,
      phoneNumber: card.phoneNumber,
    });
  };

  const cancel = () => {
    resetBilling();
    resetCard();
    setShow(false);
  };

  const cancelBilling = () => {
    setEditBilling(false);
    resetBilling();
  };

  const saveBilling = () => {
    setEditBilling(false);
  };

  const under768 = useMediaQuery("(max-width: 768px)");

  return (
    <Modal
      style={{ width: under768 ? "100%" : "auto", maxWidth: "95vw" }}
      show={show}
    >
      <div className={styles.main}>
        <button onClick={cancel} className={styles.closeButton}>
          <Close />
        </button>
        {!editBilling ? (
          <>
            <h2 className={styles.editHeading}>Edit card</h2>
            <div className={styles.contentGrid}>
              <div className={styles.cardInputs}>
                <div className={styles.nameContainer}>
                  <div className={styles.inputLabel}>Cardholder name</div>
                  <Input
                    placeholder={placeholders.cardholderName}
                    value={cardData.cardholderName}
                    disabled={loading}
                    onChange={(e: any) => {
                      handleInputChange(
                        "cardholderName",
                        e,
                        cardData,
                        setCardData
                      );
                    }}
                    error={cardData.errors.cardholderName}
                    clear={() => {
                      clearInput("cardholderName", setCardData);
                    }}
                    onKeyDown={saveChanges}
                  />
                </div>
                <div className={styles.expiryContainer}>
                  <div className={styles.inputLabel}>Expiry date</div>
                  <Input
                    placeholder={placeholders.expiry}
                    value={cardData.expiry}
                    disabled={loading}
                    onChange={(e: any) => {
                      handleInputChange("expiry", e, cardData, setCardData);
                    }}
                    error={cardData.errors.expiry}
                    clear={() => {
                      clearInput("expiry", setCardData);
                    }}
                    onKeyDown={saveChanges}
                  />
                </div>
                <Button
                  onClick={handleDeleteFromEdit}
                  color="tertiary"
                  style={{ padding: 0 }}
                >
                  <Trash />
                  Delete card
                </Button>
              </div>
              <div className={styles.billing}>
                <h4>Billing details</h4>
                <div className={styles.billingContent}>
                  {billingData.name}
                  <br />
                  {billingData.address}
                  <br />
                  {billingData.city}
                  <br />
                  {billingData.postcode}
                  <br />
                  {billingData.country.label}
                  <br />
                  {billingData.countryCode} {billingData.phoneNumber}
                </div>
                <Button
                  onClick={() => {
                    setEditBilling(true);
                  }}
                  color="tertiary"
                  style={{ padding: 0 }}
                >
                  Edit
                </Button>
              </div>
            </div>
            <div className={styles.buttons}>
              <Button
                onClick={cancel}
                color="secondary"
                style={{ marginRight: 16 }}
              >
                { t('buttons.cancel') }
              </Button>
              <Button>{ t('buttons.save') }</Button>
            </div>
          </>
        ) : (
          <BillingDetails
            billingDetails={billingData}
            setBillingDetails={setBillingData}
            cancel={cancelBilling}
            loading={loading}
            edit
            proceed={saveBilling}
          />
        )}
      </div>
    </Modal>
  );
};

export default EditCardModal;
