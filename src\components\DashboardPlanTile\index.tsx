import { useDispatch } from "react-redux";
import { ApiGet } from "../../pages/api/api";
import {
  <PERSON>,
  Copy,
  ExpiryAlert,
  MoonStars,
  PlanDataBars,
  PlanDataCalendar,
} from "../svgs";
import styles from "./dashboard-plan-tile.module.scss";
import { useState } from "react";
import formatDateUtil, { months } from "../utils/formatDate";
import { v4 as uuidv4 } from "uuid";
import { useNavigate } from "react-router-dom";
import DataBar from "../DataBar";
import moment from "moment";
import CountriesPreviewButton from "../CountriesPreviewButton";
import IncludedCountriesModal from "../IncludedCountriesModal";
import { t } from "i18next";
import i18n from "../../i18n";

export const formatTimeLeft = (end: any) => {
  const now = new Date();
  const endTime = new Date(end);

  const diff = endTime.getTime() - now.getTime();

  const days = diff / 86400000;

  if (days < 1) {
    const hours = days * 24;

    if (hours < 1) {
      return `${Math.round(hours * 60)} minutes`;
    } else {
      const minutes = (hours % 1) * 60;

      return `${Math.floor(hours)} hours ${Math.round(minutes)} minutes`;
    }
  } else {
    const hours = (days % 1) * 24;

    return `${Math.floor(days)} days ${Math.round(hours)} hours`;
  }
};

export const getTimePercentage = (start: any, end: any) => {
  const now = new Date();
  const startTime = new Date(start);
  const endTime = new Date(end);

  const percent =
    (endTime.getTime() - now.getTime()) /
    (endTime.getTime() - startTime.getTime());

  return `${percent * 100}%`;
};

const DashboardPlanTile = ({ data, subscription, repopulate }: any) => {
  const getDaysLeft = (time: string) => {
    const endDate = new Date(time);
    const now = new Date();
    const diff = endDate.getTime() - now.getTime();
    if (diff < 0) {
      return "0 days left";
    } else {
      return `${Math.ceil(diff / 86400000)} days left`;
    }
  };

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [showManage, setShowManage] = useState(false);

  const getRenewTime = (time: any) => {
    let ms = new Date(time).getTime();
    ms += 2592000000;
    let newDate = new Date(ms);
    return `${formatDateUtil(newDate)} ${newDate
      .getHours()
      .toString()
      .padStart(2, "0")}:${newDate.getMinutes().toString().padStart(2, "0")}`;
  };

  const handleRenewPlan = (planToRenew: any) => {
    ApiGet(`/plans/${planToRenew.planId}`)
      .then((response) => {
        let planToAdd = response.data;

        planToAdd.iconUrl = planToRenew.flagImage;
        planToAdd.countryName = planToRenew.planName;
        planToAdd.basketId = uuidv4();
        planToAdd.planType = planToRenew.planType;
        planToAdd.autoRenew = false;
        planToAdd.isRenew = true;
        planToAdd.subscriptionId = planToRenew.subscriptionId;
        if (planToAdd.hasMobileNumber) {
          planToAdd.didType = "MOBILE";
        } else if (planToAdd.hasLocalNumber) {
          planToAdd.didType = "LOCAL";
        } else {
          planToAdd.didType = "NA";
        }

        dispatch({
          type: "set",
          basket: planToAdd,
        });

        navigate("/cart-summary");
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: t('buttons.oops'),
            message: error.response.data.message,
          },
        });
      });
  };

  const [activeModal, setActiveModal] = useState("");

  const formatDate = (dateStr: string) => {
    const date = moment(dateStr);
    const day = date.date().toString();
    const month = months[date.month()];
    const year = date.year().toString();
    const hours = date.hours().toString().padStart(2, "0");
    const minutes = date.minutes().toString().padStart(2, "0");

    return `${day} ${month} ${year} ${hours}:${minutes}`;
  };

  const handleCopyIccid = () => {
    if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
      dispatch({
        type: "notify",
        payload: {
          error: false,
          heading: "Copied to clipboard",
          message: "",
        },
      });
      return navigator.clipboard.writeText(data.iccid);
    }
  };

  const getTimeRemaining = (planData: any) => {
    if (!planData.activationLimit) return "? days";
    const now = new Date();
    const end = Date.parse(planData.activationLimit.replace(/\s/, "T"));
    const remaining = end - now.getTime();
    const timeDays = remaining / (60 * 60 * 24 * 1000);
    switch (true) {
      case timeDays >= 365:
        return "1 year";
      case timeDays >= 30:
        let months = Math.round(timeDays / 30).toString();
        if (months === "1") {
          return months + " month";
        } else {
          return months + " months";
        }
      case timeDays >= 1:
        let time = Math.round(timeDays).toString();
        if (time === "1") {
          return time + " day";
        } else {
          return time + " days";
        }
      default:
        let hours = Math.round(timeDays * 24).toString();
        if (hours === "1") {
          return hours + " hour";
        } else {
          return hours + " hours";
        }
    }
  };

  const [showCountries, setShowCountries] = useState(false);

  return (
    <div className={styles.main}>
      <IncludedCountriesModal show={showCountries} setShow={setShowCountries} />
      <div className={styles.top}>
        {subscription.activated ? (
          <div className={`${styles.planStatus} ${styles.active}`}>
            <div className={styles.activeCircle} />
            { t('plan.active') }
          </div>
        ) : (
          <div className={`${styles.planStatus} ${styles.inactive}`}>
            <MoonStars />
            { t('plan.inactive') }
          </div>
        )}
        {subscription.activated &&
          subscription.classification === "promotional" && (
            <CountriesPreviewButton
              onClick={() => {
                setShowCountries(true);
              }}
            />
          )}
      </div>
      {!subscription.activated && (
        <div className={styles.expiryNotice}>
 {/*         <div className={styles.top}>
            <ExpiryAlert />
            <div>
              { t('plan.youHave') } {" "}
              <span style={{ fontWeight: 600 }}>
                {getTimeRemaining(subscription)} { i18n.language === 'ar' && 'left' } 
              </span>{" "}
              { t('plan.toActiviteBeforeExpiry') }
            </div>
          </div> */}
          <div className={styles.text}>
            { t('plan.toactivitePlan') }
          </div>
        </div>
      )}
      <div className={styles.nameContainer}>
        {subscription.classification !== "promotional" ? (
          <div
            className={styles.flag}
            style={{ backgroundImage: `url(${subscription.flagImage})` }}
          />
        ) : (
          <div className={styles.promotionalIcon}>%</div>
        )}
        <div>
          <div className={styles.titleText}>{subscription.planName}</div>
          <div className={styles.planInfo}>
            {parseInt(subscription.dataAllowance)}GB | { t(`plan.${subscription.validity === 1 ? 'validFor_one' : 'validFor'}`, { days: subscription.validity.toString() })}
          </div>
        </div>
        {!subscription.activated &&
          subscription.classification === "promotional" && (
            <div style={{ marginLeft: "auto", marginTop: "auto" }}>
              <CountriesPreviewButton
                onClick={() => {
                  setShowCountries(true);
                }}
              />
            </div>
          )}
      </div>
      {subscription.activated && (
        <div className={styles.planData}>
          <DataBar
            amount={subscription.remainingData}
            percentage={subscription.remainingBytes / subscription.initialBytes}
          />
          <div>
            <div className={styles.dataItem} style={{ marginBottom: 15 }}>
              <div className={styles.dataLabel}>
                <PlanDataBars />
                { t('plan.balance') }
              </div>
              <div className={styles.dataValue}>
                {subscription.remainingData} left
              </div>
            </div>
            <div className={styles.dataItem}>
              <div className={styles.dataLabel}>
                <PlanDataCalendar />
                { t('plan.expiryDate') }
              </div>
              <div className={styles.dataValue}>
                {subscription.endDate ? formatDate(subscription.endDate) : "-"}
              </div>
            </div>
          </div>
        </div>
      )}
      <div className={styles.iccidContainer}>
        <div className={styles.iccidLabel}>
          <Chip />
          { t('plan.iccid') }
        </div>
        <div className={styles.iccid}>
          {data.iccid}
          <button className={styles.copyButton} onClick={handleCopyIccid}>
            <Copy />
          </button>
        </div>
      </div>
    </div>
  );
};

export default DashboardPlanTile;
