import Modal from "../Modal"
import styles from "../../styles/offers.module.scss";
import { Cross } from "../svgs";
import { t } from "i18next";

const CountriesCovered = ({ show, setShow, countries}: { show: boolean, setShow: Function, countries: any }) => {
    return (
        <Modal show={show} setShow={setShow} style={{ maxWidth: 1000, height: 600 }}>
            <div className={styles.countiresCoveredModal}>
                <h2>{ t('plan.countriesCovered') }</h2>
                <div className={styles.countriesListCovered}>
                    {
                        countries.map((country:any, index:number) => (
                            <div key={index} className="flex items-center">
                            <img src={country.iconUrl} />
                            <p>{ country.countryName }</p>
                            </div>
                        ))
                    }
                </div>
                <span onClick={() => setShow(false)} className={styles.close}>
                    <Cross />
                </span>
            </div>
        </Modal>
    )
}

export default CountriesCovered