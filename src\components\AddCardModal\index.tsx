import { useEffect, useState } from "react";
import Modal from "../Modal";
import { Close } from "../svgs";
import { createStateObject } from "../utils/InputHandlers";
import styles from "./add-card-modal.module.scss";
import CardDetails from "../AddCardSections/CardDetails";
import BillingDetails from "../AddCardSections/BillingDetails";
import ConfirmBillingDetails from "../AddCardSections/ConfirmBillingDetails";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { t } from "i18next";

// Set up required fields and FLV data for card details
const cardFields = ["cardholderName", "number", "expiry", "cvv"];
const billingFields = [
  "name",
  "address",
  "city",
  "postcode",
  "country",
  "countryCode",
  "phoneNumber",
];

const AddCardModal = ({ show, setShow, cards, getCards }: any) => {
  const dispatch = useDispatch();

  // Active section
  const [activeSection, setActiveSection] = useState("cardDetails");
  const [loading, setLoading] = useState(false);

  // Card Details
  const [cardDetails, setCardDetails] = useState(createStateObject(cardFields));

  const handleSaveCardDetails = () => {
    if (cards.length) {
      setActiveSection("confirmBillingDetails");
    } else {
      setActiveSection("newBillingDetails");
    }
  };

  // Billing details
  const [billingDetails, setBillingDetails] = useState(
    createStateObject(billingFields)
  );

  const [savedBillingDetails, setSavedBillingDetails] = useState(null as any);

  useEffect(() => {
    if (cards.length) {
      setSavedBillingDetails(cards[0]);
    }
  }, [cards]);

  const handleSaveBilling = (billingObj: any) => {
    setSavedBillingDetails(billingObj);
    setActiveSection("confirmBillingDetails");
  };

  const handleSaveCard = () => {
    const selected = savedBillingDetails;

    // If all required fields validated, then set loading state -> true and save the card
    const addCardData = {
      cardholderName: cardDetails.cardholderName,
      cardNumber: cardDetails.number.replaceAll(" ", ""),
      expMonth: cardDetails.expiry.slice(0, 2),
      expYear: cardDetails.expiry.slice(3),
      defaultPayment: true,
      addressOne: selected?.addressOne,
      addressTwo: "sd",
      city: selected?.city,
      country: selected?.country,
      postcode: selected?.postcode,
      phoneCountryCode: selected?.phoneCountryCode,
      phoneNumber: selected?.phoneNumber,
      cvc: cardDetails.cvv,
    };

    setLoading(true);
    ApiPostAuth("/payments/cards", addCardData)
      .then((response: any) => {
        /* If card save success -
         *   stop loading,
         *   close and reset modal,
         *   display snackbar success message,
         *   and call after function
         */
        setLoading(false);
        cancel();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: t('account.msgs.success'),
            message: response.data.message,
          },
        });
        getCards();
      })
      .catch((error: any) => {
        // If save card fails, stop loading and display snackbar error
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: t('buttons.oops'),
            message: error.response.data.message,
          },
        });
      });
  };

  const cancel = () => {
    setShow(false);
    setTimeout(() => {
      setCardDetails(createStateObject(cardFields));
      setBillingDetails(createStateObject(billingFields));
      setActiveSection("cardDetails");
    }, 400);
  };

  return (
    <Modal style={{ width: "auto", maxWidth: "95vw" }} show={show}>
      <div className={styles.main}>
        <button onClick={cancel} className={styles.closeButton}>
          <Close />
        </button>
        {activeSection === "cardDetails" ? (
          <CardDetails
            cardDetails={cardDetails}
            setCardDetails={setCardDetails}
            proceed={handleSaveCardDetails}
            cancel={cancel}
          />
        ) : activeSection === "newBillingDetails" ? (
          <BillingDetails
            billingDetails={billingDetails}
            setBillingDetails={setBillingDetails}
            cancel={() => {
              setActiveSection("confirmBillingDetails");
            }}
            proceed={handleSaveBilling}
            loading={loading}
          />
        ) : activeSection === "confirmBillingDetails" ? (
          <ConfirmBillingDetails
            cardDetails={cardDetails}
            cancel={cancel}
            saveCard={handleSaveCard}
            addAddress={() => {
              setActiveSection("newBillingDetails");
            }}
            loading={loading}
            savedBillingDetails={savedBillingDetails}
            setSavedBillingDetails={setSavedBillingDetails}
            cards={cards}
          />
        ) : (
          ""
        )}
      </div>
    </Modal>
  );
};

export default AddCardModal;
