@use "../../styles/theme.scss" as *;

.label {
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  margin-bottom: 4px;
}

.switchContainer {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: space-between;
  height: 52px;
  border-radius: 1000px;
  background-color: #eff1f7;
  font-size: 12px;
  line-height: 18px;
  cursor: pointer;
  &.disabled {
    .innerLabel {
      opacity: 0.5;
    }
  }
  svg {
    height: 20px;
    width: 20px;
    margin-right: 8px;
    vertical-align: middle;
  }
}

.innerLabel {
  display: flex;
  width: 50%;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.thumb {
  position: absolute;
  left: 0px;
  width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $secondary;
  color: #fff;
  border-radius: 1000px;
  transition: left 0.3s ease;
  font-weight: 600;
  &.business {
    left: 50%;
  }
}
