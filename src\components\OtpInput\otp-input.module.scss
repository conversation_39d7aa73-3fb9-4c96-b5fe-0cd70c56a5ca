@use "../../styles/theme.scss" as *;

.container {
  display: grid;
  grid-template-rows: 1fr;
  align-items: center;
  color: $primary;
  font-weight: 700;
  font-size: 32px;
  position: relative;
  width: 100%;
  max-width: 400px;
  grid-column-gap: 13px;
  .numberDisplay {
    border-bottom: 1px solid $primary;
    width: 100%;
    height: 48px;
    cursor: text;
    text-align: center;
    &:last-child {
      margin-right: 0;
    }
  }
}

.input {
  border: none;
  font-size: 28px;
  width: 100%;
  height: 48px;
  line-height: 48px;
  text-align: center;
  background-color: transparent;
  outline: none;
  top: 1px;
  caret-color: $primary;
  &:focus {
    border-bottom: 3px solid $primary;
  }
}
