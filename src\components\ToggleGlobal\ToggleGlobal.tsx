import Shimmer from "../Shimmer";
import styles from "./toggleGlobal.module.scss";

export const ToggleGlobal = ({ style, on, onChange = () => {}, disabled, id = "" }: any) => {
  return (
    <div
      className={`${styles.main} ${id}`}
      style={{
        ...style,
        background: on ? "#CEEDF5" : "#DBD9D6",
        cursor: disabled ? "auto" : "pointer",
        opacity: disabled ? 0.5 : 1,
      }}
      onClick={(e) => {
        e.stopPropagation();
        if (!disabled) {
          onChange();
        }
      }}
    >
      {disabled && <Shimmer />}
      <div className={styles.thumb} style={{ insetInlineEnd: on ? -1 : 15, backgroundColor: on ? '#088BDD' : '#0F133A' }} />
    </div>
  );
};