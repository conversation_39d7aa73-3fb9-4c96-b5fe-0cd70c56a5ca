@use "../../styles/theme.scss" as *;

.pageNumbers {
  display: flex;
  align-items: center;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  * {
    margin-right: 18px;
    &:last-child {
      margin-right: 0px;
    }
  }
}

.pageNumber {
  cursor: pointer;
  width: 36px;
  height: 36px;
  border-radius: 1000px;
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    background: $light-primary;
  }
  &.activePageNumber {
    background: $primary;
    color: #fff;
    cursor: auto;
    &:hover {
      background: $primary;
    }
  }
}

.pageArrowButton {
  cursor: pointer;
  width: 36px;
  height: 36px;
  border-radius: 1000px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  &:hover {
    background: $light-primary;
  }
}

.dots {
  font-size: 10px;
  width: 36px;
  font-weight: 300;
  letter-spacing: 0.5px;
  display: flex;
  justify-content: center;
  align-items: center;
}
