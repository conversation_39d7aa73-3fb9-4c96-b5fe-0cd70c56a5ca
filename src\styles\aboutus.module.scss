@use "../styles/theme.scss" as *;

.aboutus {
  .ourStory {
    padding: 30px 60px;
    text-align: center;
    background-image: url("../../public/images-int/about-us/storyHeroImage.jpg");
    background-size: cover;
    @media (max-width: 768px) {
      padding: 30px 0;
    }
    h1 {
      font-size: 64px;
      font-weight: 800;
      color: $secondary;
      margin-bottom: 20px;
      @media (max-width: 768px) {
        font-size: 55px;
        margin-top: 0;
      }
    }
    p {
      font-size: 18px;
      margin: 0 auto;
      line-height: 28px;
      color: $secondary;
      width: 100%;
      margin-bottom: 25px;
      max-width: 756px;
      @media (max-width: 1024px) {
        width: 85%;
        font-size: 16px;
      }
      @media (max-width: 768px) {
        font-size: 15px;
      }
    }
    img {
      width: 54%;
      max-width: 995px;
      @media (max-width: 768px) {
        width: 85%;
      }
    }
  }
  .keyFacts {
    padding: 30px 100px;
    max-width: 1600px;
    margin: 0 auto;
    @media (max-width: 1024px) {
      padding: 30px;
    }
    .container {
      background-color: #fff;
      border-radius: 40px;
      padding: 10px 60px 40px 60px;
      h2 {
        font-size: 48px;
        font-weight: 800;
        margin-bottom: 30px;
        color: $secondary;
        @media (max-width: 1024px) {
          font-size: 35px;
        }
        @media (max-width: 768px) {
          text-align: center;
        }
      }
      > div {
        @media (max-width: 768px) {
          display: grid;
          grid-template-columns: auto auto;
          text-align: center;
        }
        h2 {
          margin: 0;
        }
      }
    }
  }
  .message {
    padding: 20px 50px;
    @media (max-width: 768px) {
      flex-direction: column;
    }
    img {
      @media (max-width: 768px) {
        width: 250px;
      }
    }
    div {
      width: 50%;
      @media (max-width: 768px) {
        width: 100%;
      }
      h2 {
        color: $secondary;
        font-size: 48px;
        font-weight: 800;
        margin-bottom: 30px;
        @media (max-width: 1024px) {
          font-size: 35px;
        }
      }
      p {
        color: $secondary;
        font-size: 17px;
        font-weight: 400;
        line-height: 26px;
        @media (max-width: 1024px) {
          font-size: 15px;
        }
      }
    }
  }
  .valuesContainer {
    background-color: #e8f8f8;
    margin-bottom: 100px;
    @media (max-width: 768px) {
      margin-bottom: 0;
    }
  }
  .values {
    padding: 50px;
    @media (max-width: 768px) {
      flex-direction: column;
      margin-bottom: 0;
    }
    h4 {
      margin: 0;
    }
    > div {
      width: 44%;
      position: relative;
      @media (max-width: 1024px) {
        width: 47%;
      }
      @media (max-width: 768px) {
        width: 100%;
      }
      h2 {
        color: $secondary;
        font-size: 48px;
        font-weight: 800;
        margin-bottom: 30px;
        @media (max-width: 1024px) {
          font-size: 35px;
        }
      }
      > div {
        display: grid;
        grid-template-columns: auto auto;
        margin-top: 20px;
        gap: 20px;
        svg {
          width: 15px;
          height: 15px;
          margin-inline-end: 15px;
        }
        @media (max-width: 600px) {
          grid-template-columns: auto;
        }
      }
      p {
        color: $secondary;
        font-size: 17px;
        font-weight: 400;
        line-height: 26px;
        margin: 0;
        @media (max-width: 1024px) {
          font-size: 15px;
        }
      }
      img {
        position: absolute;
        width: 100%;
        max-width: 523px;
        @media (max-width: 768px) {
          position: relative;
          width: 50%;
          max-width: 482px;
          top: 65px;
        }
        @media (max-width: 500px) {
          width: 320px;
        }
        @media (max-width: 500px) {
          width: 280px
        }
      }
    }
  }
}
