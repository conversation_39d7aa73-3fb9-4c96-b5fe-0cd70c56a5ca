import { t } from "i18next";
import styles from "../../styles/help.module.scss";
import { Helmet } from 'react-helmet-async';
import { organizationSchema } from "../../components/utils/schemaMarkups";

const ContactUs = () => {
    const data = {
        metaDescription: "Need Assistance? Orbit Mobile are here to help you with your eSIM anywhere and to answer your questions"
    }
    return(
        <div>
            <Helmet>
                <title>{t("general.orbit")}| {t("pages.contactUs")}</title>
                <script type="application/ld+json">
                    {JSON.stringify(organizationSchema(data))}
                </script>
                <meta name="description" content={data.metaDescription} />
            </Helmet>
            <div className={styles.searchSection}>
                <h1>{ t('contact.contactOurTeam') }</h1>
                <p>{ t('contact.haveAquestion') }</p>
                <p>{ t('contact.sendToUs') }</p>
                <img src='/images-int/help/help.png' alt="Need Assistance? Orbit Mobile are here to help you with your eSIM anywhere and to answer your questions" />
            </div>
            <section className={styles.formSection}>
                <script type="text/javascript" src="https://s3.amazonaws.com/assets.freshdesk.com/widget/freshwidget.js"></script>
                <iframe title="Feedback Form" className="freshwidget-embedded-form" id="freshwidget-embedded-form" src="https://form.orbitmobile.com/widgets/feedback_widget/new?&widgetType=embedded&submitTitle=Send+Request&searchArea=no" scrolling="no" height="850px" width="100%" ></iframe>
            </section>
      </div>
    )
}

export default ContactUs