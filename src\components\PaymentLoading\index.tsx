import { CircularProgress, Fade } from "@mui/material";
import styles from "./payment-loading.module.scss";

const PaymentLoading = ({ loading }: any) => {
  return (
    <Fade in={loading} unmountOnExit>
      <div className={styles.main}>
        <CircularProgress
          style={{
            width: 80,
            height: 80,
            color: "#fff",
          }}
        />
      </div>
    </Fade>
  );
};

export default PaymentLoading;
