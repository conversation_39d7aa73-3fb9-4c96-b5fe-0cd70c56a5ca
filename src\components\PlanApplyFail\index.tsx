import Button from "../Button";
import styles from "./payment-fail.module.scss";
import Modal from "../Modal";
import { useNavigate } from "react-router-dom";
import { HeadPhones } from "../svgs";
import { t } from "i18next";

const PlanApplyFail = ({ show, setShow }: any) => {
  const navigate = useNavigate();

  return (
    <Modal show={show} style={{ maxWidth: 490 }}>
      <div className={styles.container}>
        <img src="/images-int/payment/payment_fail.svg" />
        <h4>{ t('buttons.oops') }! { t('account.errors.somethingWrong') }</h4>
        <p>{ t('account.checkout.planIssue') }</p>
        <div className={styles.buttons}>
          <Button
            style={{ height: 48 }}
            onClick={() => {
              setShow(false);
            }}
          >
            { t('buttons.repurchase') }
          </Button>
          <Button
            style={{ height: 48 }}
            onClick={() => {
              navigate("/dashboard/home");
            }}
            color="secondary"
          >
            { t('general.dashboard')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default PlanApplyFail;
