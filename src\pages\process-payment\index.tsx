import { useEffect } from "react";
import { ApiPostAuth } from "../api/api";
import { useNavigate, useSearchParams } from "react-router-dom";
import { CircularProgress } from "@mui/material";
import PaymentLoading from "../../components/PaymentLoading";
import { useSelector } from "react-redux";

const StripeResponse = () => {
  const navigate = useNavigate();

  const [searchParams, setSearchParams] = useSearchParams();

  const handleError = () => {
    navigate("/checkout?pay_failure=true");
    localStorage.removeItem("sessionId");
    localStorage.removeItem("stripeLocation");
  };
  const { basket } = useSelector((state: any) => state);

  useEffect(() => {
    const paymentIntent = searchParams.get("payment_intent");
    const paymentType = searchParams.get("payment_type");
    const sessionId = localStorage.getItem("sessionId");
    console.log(paymentIntent, paymentType, sessionId);
    if ((paymentIntent || sessionId) && paymentType) {
      let planData = {
        id: basket.planId,
        currencyCode: basket.prices[0].currencyCode,
        planType: "DATA",
        automaticPayment: false,
        didType: "NA",
      } as any;

      const paymentData = {
        paymentType:
          paymentType === "google_pay"
            ? "googlePay"
            : paymentType === "apple_pay"
            ? "applePay"
            : "card",
      };

      const overallData = {
        plans: [planData],
        payment: paymentData,
        paymentId: paymentType === "card" ? sessionId : paymentIntent,
      };

      ApiPostAuth("/plans/create", overallData)
        .then((response) => {
          if (response.data.failedPlans.length > 0) {
            handleError();
          } else {
            localStorage.removeItem("sessionId");
            localStorage.removeItem("stripeLocation");
            navigate("/checkout?pay_success=true");
          }
        })
        .catch((error) => {
          handleError();
        });
    } else {
      handleError();
    }
  }, []);

  return <PaymentLoading loading />;
};

export default StripeResponse;
