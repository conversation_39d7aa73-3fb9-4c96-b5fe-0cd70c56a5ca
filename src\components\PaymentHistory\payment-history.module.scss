@use "../../styles/theme.scss" as *;

.main {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.topBar {
  color: $secondary;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 22px;
  @media (max-width: 850px) {
    padding: 15px 0;
  }
  h3 {
    font-weight: 800;
    font-size: 26px;
    line-height: 32px;
    margin: 0 0 24px 0;
  }
}

.tableContainer {
  width: 100%;
  @media (max-width: 850px) {
    overflow-x: auto;
    white-space: nowrap;
  }
}

.table {
  margin-top: 45px;
  border-collapse: collapse;
  outline: 1px solid rgba(0, 0, 0, 0.12);
  border-top-left-radius: 9px;
  border-top-right-radius: 9px;
  overflow: hidden;
  width: 100%;
  thead {
    background-color: #cee8f8;
  }
  th {
    text-align: start;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: $dark-dark-purple;
    height: 47px;
    @media (max-width: 850px) {
      padding-inline-end: 24px;
    }
    &:first-child {
      padding-inline-start: 24px;
    }
  }
  tbody {
    td {
      @media (max-width: 850px) {
        padding-inline-end: 24px;
      }
    }
    tr {
      height: 60px;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: $dark-dark-purple;
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
      img {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin-inline-end: 10px;
      }
      td.card {        
        span {
          display: flex;

          &:dir(rtl) {
            flex-direction: row-reverse;
            justify-content: flex-end;
          }
        }
        svg,
        img {
          margin-inline-end: 12px;
          width: 38px;
          height: 22px;
          vertical-align: middle;
        }
      }
      td:first-child {
        border-radius: 8px 0 0 8px;
        padding-inline-start: 24px;
      }
      td:last-child {
        border-radius: 0 8px 8px 0;
        padding-inline-end: 24px;
      }
    }
    tr:last-child {
      border-bottom: 0;
    }
  }
}

.mobileHistory {
  display: none;
  .monthTitle {
    color: $dark-dark-purple;
    margin: 16px 0;
    font-size: 14px;
    line-height: 21px;
  }
  @media (max-width: 850px) {
    display: block;
  }
}

.mobilePayment {
  width: 100%;
  background: #eff1f7;
  margin-bottom: 12px;
  padding: 24px;
  border-radius: 8px;
  color: $dark-dark-purple;
  font-size: 14px;
  line-height: 21px;
  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  svg,
  img {
    margin-inline-end: 12px;
    width: 38px;
    height: 22px;
    vertical-align: middle;
  }
}

.paginationContainer {
  width: 100%;
  display: flex;
  justify-content: end;
  margin-top: auto;
  border-left: 1px solid rgba(0, 0, 0, 0.12);
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  border-bottom-left-radius: 9px;
  border-bottom-right-radius: 9px;
  font-size: 14px;
  font-weight: 400;

  button {
   &:dir(rtl) {
    transform: scaleX(-1);
   }
  }

  .MuiTablePagination-toolbar {
    padding-left: 0 !important;
  }
}

.noBundles,
.noBundlesMobile {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 50px;
  padding-bottom: 70px;
  color: $dark-dark-purple;
  text-align: center;
  svg {
    color: #fff;
  }
  h5 {
    font-weight: 800;
    font-size: 20px;
    line-height: 28px;
    margin: 20px 0 6px 0;
    @media (max-width: 850px) {
      margin-top: 25px;
    }
  }
  p {
    font-size: 14px;
    line-height: 20px;
    margin: 0 0 32px 0;
    max-width: 511px;
  }
}

.noBundles {
  @media (max-width: 850px) {
    padding-top: 0;
  }
}

.noBundlesMobile {
  display: none;
  padding-top: 50px;
  @media (max-width: 850px) {
    display: flex;
  }
}
