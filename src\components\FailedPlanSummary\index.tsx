import styles from "./failed-plan-summary.module.scss";
import formatPlanData from "../utils/formatPlanData";

const FailedPlanSummary = ({ planObj }: any) => {
  return (
    <div className={styles.main}>
      <div className={styles.row}>
        <div className={styles.countryName}>
          <div
            className={styles.flag}
            style={{
              backgroundImage: `url(https://public-gist-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/${planObj.regionCode}.png)`,
            }}
          />
          <div className={styles.text}>{planObj.planName}</div>
        </div>
        <div className={styles.price}>
          {planObj.currencySymbol}
          {parseFloat(planObj.price).toFixed(2)}
        </div>
      </div>
      <div className={styles.row}>
        <div>{formatPlanData(planObj)}</div>
      </div>
    </div>
  );
};

export default FailedPlanSummary;
