@use "../../styles/theme.scss" as *;

.header {
  width: 100%;
  background-color: #fafaf9;
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 9000;
  transition: top 0.3s ease;
  &.dashboard {
    box-shadow: none;
    position: absolute;
    background: none;
  }
}
.smartbanner {
  > div {
    justify-content: space-between;
    padding: 0.5rem 1rem;
    background-color: rgb(232, 248, 248);
    border-color: rgb(212, 212, 212);
    border-bottom-width: 1px;
    z-index: 9999;
    a {
      font-size: 13px;
      text-decoration: none;
      background: #9be5e4;
      padding: 10px;
      border-radius: 15px;
      color: $secondary;
      font-weight: bold;
    }
    > div {
      justify-content: flex-start;
      gap: 0.5rem;
      svg {
        color: $secondary;
        width: 1rem;
        height: 1rem;
        cursor: pointer;
      }
      img {
        display: block;
        border-radius: 0.5rem;
        width: 3rem;
        height: 3rem;
        cursor: pointer;
        background: #fff;
        padding: 5px;
      }
      div {
        text-align: left;
        &:dir(rtl) {
          text-align: right;
        }
        p {
          color: $secondary;
          font-weight: 600;
          font-size: 0.875rem;
          margin: 2px;
          &:last-of-type {
            font-size: 12px;
            font-weight: 100;
          }
        }
      }
    }
  }
}
.main {
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 50px;
  @media (max-width: 768px) {
    padding: 0 16px;
    height: 78px;
  }
}

.linksContainer {
  display: flex;
  align-items: center;
  @media (max-width: 965px) {
    display: none;
  }
  .link {
    height: 33px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    border-radius: 100px;
    font-weight: 400;
    font-size: 14px;
    &.active {
      background: $primary;
      color: #fff;
      font-weight: 700;
    }
    &:hover {
      color: $secondary;
    }
  }
  a {
    text-decoration: none;
    color: $secondary;
  }
}

.openMenu {
  display: none;
  background: none;
  border: none;
  padding: 0;
  width: 44px;
  height: 44px;
  align-items: center;
  justify-content: center;
  svg {
    vertical-align: middle;
  }
  @media (max-width: 965px) {
    display: flex;
  }
}

.searchButton {
  border: none;
  background: none;
  padding: 0;
  margin-right: 33px;
  color: $dark-dark-purple;
  cursor: pointer;
  width: 24px;
  &:hover {
    color: $secondary;
  }
  svg {
    vertical-align: middle;
  }
  @media (max-width: 965px) {
    display: none;
  }
}

.mainButton {
  @media (max-width: 965px) {
    margin-left: auto;
    margin-right: 8px;
    button {
      height: 33px;
      font-size: 14px;
      margin: auto;
      svg {
        width: 16px !important;
        height: 16px !important;
      }
    }
  }
  a {
    @media (max-width: 965px) {
      display: none;
    }
  }
  .localeSelect {
    position: relative;
    margin-inline-end: 8px;
    border-radius: 100px;
    padding: 10px 12px;
    cursor: pointer;
    &:hover {
      background: #efeeed;
    }
    @media (max-width: 965px) {
      height: 37px;
    }
    img {
      width: 20px;
      height: 20px;
      margin-inline-end: 6px;
    }
    span {
      font-weight: 700;
      font-size: 14px;
    }
    > div {
      position: absolute;
      box-shadow: 0px 6px 12px 0px #0000001a;
      background: #fff;
      top: 48px;
      width: 160px;
      padding: 6px 8px;
      border-radius: 16px;
      .locale {
        margin-bottom: 4px;
        padding: 10px 0px 10px 12px;
        border-radius: 100px;
        cursor: pointer;
        &.active {
          cursor: auto;
          &:hover {
            background: #fff;
          }
        }
        &:hover {
          background: #efeeed;
        }
        &:last-of-type {
          margin-bottom: 0px;
        }
      }
      span {
        font-size: 14px;
        line-height: 20px;
        font-weight: 400;
      }
    }
  }
}

.basket {
  height: 50px;
  width: 103px;
  border-radius: 100px 0 0 100px;
  background: #eff1f7;
  padding: 0 0 0 24px;
  position: relative;
  right: -25px;
  margin-left: -25px;
  z-index: 5;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  color: $dark-dark-purple;
  cursor: pointer;
  svg {
    margin-right: 10px;
  }
  span {
    text-decoration: none;
  }
  &:hover {
    background: $light-primary;
  }
  @media (max-width: 965px) {
    position: static;
    border-radius: 100px;
    padding: 0;
    justify-content: center;
    width: 90px;
    margin-right: 20px;
  }
  @media (max-width: 768px) {
    height: 40px;
    width: 80px;
    svg {
      height: 16px;
      width: 16px;
    }
  }
}

.logo {
  width: 102px;
  vertical-align: middle;
  @media (max-width: 768px) {
    width: auto;
    height: 40px;
  }
}
