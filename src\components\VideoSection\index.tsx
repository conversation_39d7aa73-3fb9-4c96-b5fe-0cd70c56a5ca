import { useEffect, useRef, useState } from "react";
import { Pause, Play } from "../svgs";
import styles from "./video-section.module.scss";
import $ from "jquery";

const VideoSection = ({ playOnScroll }: any) => {
  const video: any = useRef(null);

  const [videoPlaying, setVideoPlaying] = useState(false);
  const [showPause, setShowPause] = useState(false);
  const [paused, setPaused] = useState(false);

  const handlePlayVideo = () => {
    video.current!.play();
    setVideoPlaying(true);
    setShowPause(true);
  };

  const handlePauseVideo = () => {
    video.current!.pause();
    setShowPause(false);
  };

  const checkScrollForVideo = () => {
    const elementScrolled = (elem: any) => {
      const docViewTop = $(window).scrollTop()!;
      const docViewBottom = docViewTop + $(window).height()!;
      const elemTop = $(elem).offset()!.top;
      return elemTop <= docViewBottom && elemTop >= docViewTop;
    };

    if (elementScrolled("#home-page-video")) {
      if (!$("#home-page-video").hasClass("paused")) {
        $("#home-page-video").trigger("play");
        setVideoPlaying(true);
        setShowPause(true);
      }
    } else {
      $("#home-page-video").trigger("pause");
      setShowPause(false);
    }
  };

  useEffect(() => {
    if (playOnScroll) {
      //$(window).on("scroll", checkScrollForVideo);
    } else {
      //checkScrollForVideo();
    }
    return () => {
      $(window).off("scroll");
    };
  }, []);

  return (
    <section className={styles.videoSection}>
      <video
        ref={video}
        height="auto"
        width="100%"
        className={styles.videoPlayer}
        preload="metadata"
        id="home-page-video"
        controls
        onPause={(e) => {
          $(e.target).addClass("paused");
          setShowPause(false);
        }}
        onPlay={(e) => {
          setVideoPlaying(true);
          setShowPause(true);
        }}
      >
        <source src="https://public-gist-bucket.s3.eu-north-1.amazonaws.com/videos/Gist+Mobile+(Explainer+Video).mp4#t=0.5"></source>
        Your browser does not support the video tag.
      </video>
      {!showPause && (
        <button onClick={handlePlayVideo} className={styles.playButton}>
          <Play />
        </button>
      )}
      {showPause && (
        <button onClick={handlePauseVideo} className={styles.pauseButton}>
          <Pause />
        </button>
      )}
    </section>
  );
};

export default VideoSection;
