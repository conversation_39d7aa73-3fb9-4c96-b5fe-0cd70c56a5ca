@use "../../styles/theme.scss" as *;

.blogSection {
  padding: 0px 50px 100px 50px;
  @media (max-width: 768px) {
    padding: 0px 24px 50px 24px;
  }
  .blogHeading {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin: 0 auto;
    margin-bottom: 33px;
    max-width: 1600px;
    h3 {
      font-weight: 700;
      font-size: 34px;
      line-height: 51px;
      color: $dark-dark-purple;
      margin: 0;
      @media (max-width: 768px) {
        font-size: 26px;
        line-height: 39px;
      }
    }
  }
  .swiperButtons {
    display: flex;
    align-items: center;
    button {
      padding: 0;
      background: none;
      border: none;
      svg {
        vertical-align: middle;
      }
    }
  }
}
