import { useEffect } from "react";
import { ApiGet, ApiPostAuth } from "../api/api";
import { useNavigate, useSearchParams } from "react-router-dom";
import { CircularProgress } from "@mui/material";
import PaymentLoading from "../../components/PaymentLoading";
import { useDispatch } from "react-redux";

const CrmLogin = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    const token = searchParams.get("auth");
    if (token) {
      localStorage.setItem("token", token);
      dispatch({
        type: "set",
        plans: [],
      });
      ApiGet("/users").then((response: any) => {
        console.log(response);
        dispatch({
          type: "set",
          loggedIn: true,
        });
        dispatch({
          type: "set",
          userInfo: {
            firstName: response.data.firstName,
            lastName: response.data.lastName,
            currency: response.data.creditCurrency,
          },
        });
        navigate("/dashboard/home");
      });
    }
  }, []);

  return <PaymentLoading loading />;
};

export default CrmLogin;
