@use "../../styles/theme.scss" as *;

.main {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .error {
    color: $error-text;
    font-size: 14px;
    font-weight: 400;
  }
  @media (max-width: 768px) {
    padding: 50px 24px 20px 24px;
  }
  img {
    width: 120px;
  }
  svg {
    width: 120px;
    height: 120px;
  }
  h4 {
    color: $secondary;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    margin: 0 0 3px 0;
    @media (max-width: 768px) {
      margin: 0 0 16px 0;
    }
  }
  h5 {
    text-align: center;
    font-size: 16px;
  }
  p {
    text-align: center;
    color: $dark-dark-purple;
    font-size: 14px;
    line-height: 24px;
    margin: 0 0 25px 0;
  }
  .buttons {
    display: flex;
    align-items: center;
    @media (max-width: 768px) {
      flex-direction: column-reverse;
      width: 100%;
      button {
        width: 100%;
        margin: 0 0 12px 0 !important;
      }
    }
  }
}
