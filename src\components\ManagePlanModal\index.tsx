import Modal from "../Modal";
import styles from "./manage-plan-modal.module.scss";
import PlanDataBar from "../PlanDataBar";
import {
  Bars,
  Close,
  Hourglass,
  Phone,
  RenewAlert,
  SMS,
  Share,
  Trash,
  Voicemail,
} from "../svgs";
import { formatTimeLeft, getTimePercentage } from "../DashboardPlanTile";
import { useEffect, useState } from "react";
import { ApiGet, ApiPostAuth } from "../../pages/api/api";
import { formatPrice } from "../utils/formatPrice";
import Shimmer from "../Shimmer";
import Toggle from "../Toggle";
import { useDispatch } from "react-redux";
import formatDate, { formatDateWords } from "../utils/formatDate";
import Button from "../Button";
import { v4 as uuid } from "uuid";
import { useNavigate } from "react-router-dom";
import { formatBytes } from "../utils/formatPlanData";
import { t } from "i18next";

const ManagePlanModal = ({
  show,
  setShow,
  data,
  repopulate,
  setActiveModal,
}: any) => {
  console.log(data);

  const [priceData, setPriceData] = useState(null as any);

  useEffect(() => {
    if (show) {
      ApiGet(`/plans/${data.planId}`).then((response: any) => {
        setPriceData(response.data.prices[0]);
      });
    }
  }, [show]);

  const [toggleLoading, setToggleLoading] = useState(false);

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleAutoRenewChange = () => {
    setToggleLoading(true);
    ApiPostAuth("/subscriptions/autopay", {
      subscriptionId: data.subscriptionId,
      autoPay: !data.autorenew,
    })
      .then((response) => {
        repopulate(() => {
          setToggleLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: false,
              heading: t('account.msgs.success'),
              message: response.data.message,
            },
          });
        }, false);
      })
      .catch((error) => {
        setToggleLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: t('buttons.oops'),
            message: error.response.data.message,
          },
        });
      });
  };

  const handleRenewPlan = (planToRenew: any) => {
    ApiGet(`/plans/${planToRenew.planId}`)
      .then((response) => {
        let planToAdd = response.data;

        planToAdd.iconUrl = planToRenew.flagImage;
        planToAdd.countryName = planToRenew.planName;
        planToAdd.basketId = uuid();
        planToAdd.planType = planToRenew.planType;
        planToAdd.autoRenew = false;
        planToAdd.isRenew = true;
        planToAdd.subscriptionId = planToRenew.subscriptionId;
        if (planToAdd.hasMobileNumber) {
          planToAdd.didType = "MOBILE";
        } else if (planToAdd.hasLocalNumber) {
          planToAdd.didType = "LOCAL";
        } else {
          planToAdd.didType = "NA";
        }

        dispatch({
          type: "set",
          basket: planToAdd,
        });
        localStorage.setItem("basket", JSON.stringify(planToAdd));

        navigate("/cart-summary");
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: t('buttons.oops'),
            message: error.response.data.message,
          },
        });
      });
  };

  const getRenewTime = (time: any) => {
    let ms = new Date(time).getTime();
    ms += 2592000000;
    let newDate = new Date(ms);
    return `${formatDate(newDate)} ${newDate
      .getHours()
      .toString()
      .padStart(2, "0")}:${newDate.getMinutes().toString().padStart(2, "0")}`;
  };

  return (
    <Modal show={show} style={{ height: "auto", maxWidth: 700 }}>
      <div className={styles.main}>
        <h4>Manage</h4>
        <button
          onClick={() => {
            setShow(false);
          }}
          className={styles.closeButton}
        >
          <Close />
        </button>
        <div className={`${styles.grid} ${data.held && styles.held}`}>
          <div className={styles.colContainer}>
            {!data.held && <div className={styles.colTitle}>Initial Plan</div>}
            <div className={styles.colInfoBox}>
              <div className={styles.flexApart}>
                <div>{data.displayName}</div>
                {priceData === null ? (
                  <div className={styles.priceLoad}>
                    <Shimmer />
                  </div>
                ) : (
                  <div>
                    {priceData.currencySymbol}
                    {formatPrice(priceData.cost)}
                  </div>
                )}
              </div>
              <div className={styles.country}>{data.planName}</div>
              <div className={styles.initialGrid}>
                {data.initialBytes !== 0 && (
                  <div className={styles.initialData}>
                    <Bars />
                    <div>
                      <div className={styles.value}>
                        {formatBytes(data.initialBytes)}
                      </div>
                      <div className={styles.label}>Data</div>
                    </div>
                  </div>
                )}
                {data.initialMinutes !== 0 && (
                  <div className={styles.initialData}>
                    <Phone />
                    <div>
                      <div className={styles.value}>{data.initialMinutes}</div>
                      <div className={styles.label}>Minutes</div>
                    </div>
                  </div>
                )}
                {data.initialMessages !== 0 && (
                  <div className={styles.initialData}>
                    <SMS />
                    <div>
                      <div className={styles.value}>{data.initialMessages}</div>
                      <div className={styles.label}>SMS</div>
                    </div>
                  </div>
                )}
                <div className={styles.initialData}>
                  <Hourglass />
                  <div>
                    <div className={styles.value}>{data.validity} Days</div>
                    <div className={styles.label}>Validity</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {!data.held && (
            <div className={styles.colContainer}>
              <div className={styles.colTitle}>Remaining Balance</div>
              <div className={styles.colInfoBox}>
                {data.initialBytes !== 0 && (
                  <PlanDataBar
                    Icon={Bars}
                    displayText={`${data.remainingData} left`}
                    percentage={
                      (data.remainingBytes / data.initialBytes) * 100 + "%"
                    }
                  />
                )}
                {data.initialMinutes !== 0 && (
                  <PlanDataBar
                    Icon={Phone}
                    displayText={`${data.remainingMinutes} mins left`}
                    percentage={
                      (data.remainingMinutes / data.initialMinutes) * 100 + "%"
                    }
                  />
                )}
                {data.initialMessages !== 0 && (
                  <PlanDataBar
                    Icon={SMS}
                    displayText={`${data.remainingMessages} SMS left`}
                    percentage={
                      (data.remainingMessages / data.initialMessages) * 100 +
                      "%"
                    }
                  />
                )}
                <PlanDataBar
                  Icon={Hourglass}
                  displayText={formatTimeLeft(data.endDate)}
                  percentage={getTimePercentage(data.startDate, data.endDate)}
                  noMargin
                />
              </div>
            </div>
          )}
        </div>
        {data.held && (
          <div className={styles.alert}>
            <RenewAlert />
            <div className={styles.heading}>Requires Renewal</div>
            <p>
              Renew by {getRenewTime(data.endDate)}, otherwise this plan and
              it’s phone number will be removed from your account
            </p>
          </div>
        )}
        {data.autorenewal && (
          <div className={`${styles.autoRenewBox} ${data.held && styles.held}`}>
            <div className={styles.row}>
              <div className={styles.title}>Auto-Renew</div>
              <div className={styles.autorenew}>
                <div className={styles.autorenewText}>
                  {data.held
                    ? "You can turn on this smart feature when you renew your plan."
                    : data.autorenew
                    ? t('buttons.on')
                    : t('buttons.off')}
                </div>
                <div style={{ width: 37 }}>
                  <Toggle
                    on={data.autorenew}
                    onChange={handleAutoRenewChange}
                    disabled={toggleLoading}
                    style={{ pointerEvents: data.held ? "none" : "all" }}
                  />
                </div>
              </div>
            </div>
            <div className={styles.renewText}>
              {data.autorenew
                ? `You’re all set! This plan will auto-renew on ${formatDateWords(
                    data.endDate
                  )}`
                : "When turned on, your plan will renew automatically before it expires or if your data runs out during. Cancel anytime."}
            </div>
          </div>
        )}
        <div className={styles.toolbar}>
          {data.didNumber && (
            <div className={styles.actionButtons}>
              <button
                className={styles.actionButton}
                onClick={() => {
                  setActiveModal("share");
                }}
              >
                <Share />
              </button>
              <button
                className={styles.actionButton}
                onClick={() => {
                  setActiveModal("voicemail");
                }}
              >
                <Voicemail />
              </button>
              <button
                className={styles.actionButton}
                onClick={() => {
                  setActiveModal("delete");
                }}
              >
                <Trash />
              </button>
            </div>
          )}
          <Button
            onClick={() => {
              handleRenewPlan(data);
            }}
            style={{ marginLeft: "auto" }}
          >
            Renew Plan
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ManagePlanModal;
