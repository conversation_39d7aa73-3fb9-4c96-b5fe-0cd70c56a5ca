@use "./theme.scss" as *;

.container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  .links {
    a {
      font-size: 14px;
      color: $secondary;
      width: 100%;
      text-align: center;
      &.active {
        font-weight: 700;
        padding-bottom: 10px;
        height: 10px;
        border-radius: 20px;
        box-shadow: 0 30px 0 -1px #05bfbe;
      }
    }
  }
}

.main {
  width: 100%;
  display: flex;
  max-width: 327px;
  flex-direction: column;
  align-items: center;
  padding: 40px 0 25px 0;
  @media (max-width: 768px) {
    padding: 40px 24px;
    max-width: max-content;
  }
  h2 {
    margin: 60px 0;
  }
  a {
    color: $secondary;
    text-decoration: none;
    &:hover {
      text-decoration: underline;
    }
  }
  h4 {
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    color: $primary;
    margin: 0 0 12px 0;
    text-align: center;
  }
  .text {
    font-size: 16px;
    line-height: 24px;
    color: $dark-dark-purple;
    margin: 0 0 25px 0;
    text-align: center;
  }
}

.buttons {
  display: flex;
}

.noEsim {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 32px;
  .esimDisclaimer {
    background: #eff1f7;
    border-radius: 12px;
    color: #160b2a;
    font-size: 16px;
    line-height: 24px;
    padding: 23px 37px;
    text-align: center;
    max-width: 698px;
    margin-bottom: 39px;
  }
  .checkboxContainer {
    display: flex;
    align-items: center;
    color: #000;
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 32px;
    p {
      margin: 0;
      margin-left: 12px;
    }
  }
}

.greatNews {
  font-size: 16px;
  line-height: 24px;
  color: $dark-dark-purple;
  margin: 7px 0 0 0;
  padding-bottom: 7px;
  max-width: 624px;
  text-align: center;
}

.polices {
  a {
    color: #088bdd;
  }
  font-size: 12px;
  line-height: 18px;
  color: $dark-dark-purple;
  margin: 24px 0 0 0;
  text-align: center;
}

.personal {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 50px 50px 100px 50px;
  text-align: center;
  @media (max-width: 768px) {
    padding: 50px 24px;
  }
  h4 {
    color: $primary;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    margin: 0 0 22px 0;
  }
  .personalInputs {
    width: 100%;
    max-width: 327px;
    margin-bottom: 40px;
  }
}

.verifyMain {
  width: 100%;
  display: flex;
  max-width: 418px;
  flex-direction: column;
  align-items: center;
  padding: 80px 0 50px 0;
  color: $secondary;
  @media (max-width: 768px) {
    padding: 24px;
    max-width: max-content;
  }
  .verifyTitle {
    font-size: 30px;
    font-weight: 800;
    line-height: 38px;
    letter-spacing: -0.025em;
    text-align: center;
    margin: 0px;
    margin-bottom: 16px;
  }
  p {
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    margin: 0px;
  }
  .resend {
    margin-top: 34px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    h5 {
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      margin: 0px;
      margin-bottom: 4px;
    }
    p {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      margin: 0px;
    }
  }
}

.finishReg {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  padding-bottom: 80px;
  .image {
    width: 80px;
    height: 80px;
  }
  h2 {
    margin: 16px 0px;
    font-size: 30px;
    font-weight: 800;
    line-height: 38px;
    letter-spacing: -0.025em;
    text-align: center;
  }
  .text {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    margin: 0px;
  }
  .inputs {
    width: 100%;
    max-width: 388px;
    margin-top: 30px;
  }
}
