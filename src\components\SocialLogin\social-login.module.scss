@use "../../styles/theme.scss" as *;

.divider {
  color: $dark-dark-purple;
  font-size: 12px;
  line-height: 18px;
  margin: 22px 0 16px 0;
  width: 100%;
  text-align: center;
  p {
    position: relative;
    &::after,
    &::before {
      display: inline-block;
      position: absolute;
      content: "";
      border-top: 1px solid #c3cbff;
      width: 55px;
      top: 50%;
      margin: 0 1rem;
    }
    &::after {
      right: 0;
    }
    &::before {
      left: 0;
    }
  }
}

.buttonsContainer {
  max-width: 327px;
  width: 100%;
}

.socialButton {
  max-width: 327px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  border: none;
  border-radius: 1000px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  img {
    width: 24px;
    margin-inline-end: 10px;
  }
}

.fbButton {
  margin-top: 12px;
  background: #fff;
  color: #1877f2;
  border: 1px solid #1877f2;
}

.appleButton {
  margin-top: 12px;
  background: #fff;
  color: #160b2a;
  border: 1px solid #160b2a;
}

.googleButton {
  margin-bottom: 12px;
  background: #fff;
  color: #160b2a;
  box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.15);
}
