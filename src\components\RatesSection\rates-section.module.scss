@use "../../styles/theme.scss" as *;

.topBar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 26px;
  margin-bottom: 20px;
  @media (max-width: 840px) {
    display: grid;
    grid-row-gap: 20px;
  }
  h3 {
    color: $primary;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    margin: 0 29px 0 0;
    white-space: nowrap;
    @media (max-width: 840px) {
      grid-area: 1 / 1 / 2 / 2;
      z-index: initial;
    }
  }
  .closeButton {
    margin-left: 53px;
    @media (max-width: 840px) {
      grid-area: 1 / 1 / 2 / 2;
      margin-left: auto;
    }
    svg {
      vertical-align: middle;
    }
    padding: 0;
    background: none;
    border: none;
    cursor: pointer;
  }
}

.searchContainer {
  width: 100%;
  max-width: 709px;
  @media (max-width: 840px) {
    grid-area: 2 / 1 / 3 / 2;
  }
}

.ratesContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  @media (max-width: 1120px) {
    grid-template-columns: 1fr;
    margin: 0 auto;
  }
}

.noResults {
  grid-column: 1 / 3;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding-top: 50px;
  h4 {
    font-weight: 700;
    font-size: 40px;
    line-height: 60px;
    color: $primary;
    margin: 0 0 12px 0;
  }
  p {
    color: $dark-dark-purple;
    margin: 0 0 20px 0;
    font-size: 16px;
    line-height: 24px;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  padding-top: 50px;
}
