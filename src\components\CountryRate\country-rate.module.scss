.main {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  background: #eff1f7;
  border-radius: 8px;
  padding: 24px;
  @media (max-width: 560px) {
    flex-direction: column;
    align-items: flex-start;
  }
  @media (max-width: 420px) {
    align-items: center;
  }
}

.nameContainer {
  display: grid;
  grid-template-columns: 20px auto;
  grid-column-gap: 8px;
  align-items: center;
  font-weight: 600;
  font-size: 14px;
  line-height: 21px;
  margin-right: 15px;
  @media (max-width: 560px) {
    margin: 0 0 20px 0;
  }
  .flag {
    width: 20px;
    height: 20px;
    border-radius: 1000px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    background-color: #cdced1;
  }
}

.ratesContainer {
  display: flex;
  align-items: flex-end;
  .singleRate {
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 18px;
    @media (max-width: 420px) {
      flex-direction: column;
    }
    svg {
      margin-right: 2px;
      @media (max-width: 420px) {
        margin: 0 0 2px 0;
      }
    }
  }
}
