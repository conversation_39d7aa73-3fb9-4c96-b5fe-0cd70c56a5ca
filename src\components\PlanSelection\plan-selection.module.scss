@use "../../styles/theme.scss" as *;

.mainContainer {
  display: flex;
  flex-direction: column-reverse;
  padding: 50px 65px 0 65px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  flex-grow: 1;
  min-height: 290px;
  @media (max-width: 1330px) {
    display: flex;
    flex-direction: column-reverse;
    height: auto;
    align-items: flex-start;
  }
  @media (max-width: 870px) {
    padding: 24px;
  }
  @media (max-width: 768px) {
    padding: 24px 0;
  }
}

.mainContainerCarousel {
  display: flex;
  flex-direction: column-reverse;
  padding: 50px 65px 0 65px;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  flex-grow: 1;
  min-height: 290px;
  @media (max-width: 1254px) {
    display: flex;
    flex-direction: column-reverse;
    height: auto;
  }
  @media (max-width: 870px) {
    padding: 24px;
  }
  @media (max-width: 768px) {
    padding: 24px 0;
  }
}

.esimWarning {
  display: grid;
  grid-template-columns: 28px 1fr;
  grid-column-gap: 12px;
  align-items: center;
  color: #fff;
  margin-bottom: 46px;
  align-self: flex-start;
  padding-left: 52px;
  @media (max-width: 768px) {
    padding: 0 24px;
    margin-bottom: 10px;
    margin-top: 10px;
    font-size: 12px;
    line-height: 18px;
  }
  a {
    color: #fff;
  }
  svg {
    width: 28px;
    height: 24px;
  }
}

.mainContainerNoNumberType {
  display: flex;
  padding: 16px 65px 0 65px;
  justify-content: center;
  align-items: center;
  width: 100%;
  flex-grow: 1;
  min-height: 290px;
  @media (max-width: 1254px) {
    height: auto;
  }
  @media (max-width: 870px) {
    padding: 24px;
  }
  @media (max-width: 768px) {
    padding: 24px 0;
  }
}

.numberTypeSelector {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 160px;
  margin-right: auto;
  @media (max-width: 1254px) {
    align-self: flex-start;
    margin-top: 20px;
    align-items: flex-start;
  }
  @media (max-width: 768px) {
    padding: 0 24px;
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 50px;
  }
  .heading {
    font-size: 16px;
    line-height: 24px;
    color: #fff;
    margin-bottom: 20px;
    @media (max-width: 768px) {
      width: 100%;
    }
  }
  .numberType {
    width: 160px;
    height: 44px;
    border-radius: 1000px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: $dark-dark-purple;
    position: relative;
    overflow: hidden;
    background-color: #eff1f7;
    cursor: pointer;
    transition: color 0.3s ease, background-color 1s ease, opacity 0.3s ease;
    @media (max-width: 768px) {
      margin-right: 16px;
      width: calc(50% - 8px);
      &:last-of-type {
        margin-right: 0px;
      }
    }
    &.disabled {
      pointer-events: none;
      opacity: 0.25;
    }
    .button {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
      position: relative;
      z-index: 20;
    }
    &.active {
      color: #fff;
      cursor: auto;
      background-color: transparent;
      @media (max-width: 768px) {
        background-color: $secondary;
        transition: color 0.3s ease, background-color 0.3s ease;
      }
    }
    .purpleBackground {
      position: absolute;
      z-index: 10;
      @media (max-width: 768px) {
        display: none;
      }
    }
  }
}

.plansContainer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  min-height: 280px;
  margin: auto 0;
  @media (max-width: 768px) {
    display: none;
  }
}

.mobilePlansContainer {
  display: none;
  width: 100%;
  @media (max-width: 768px) {
    display: block;
  }
}

.desktopCarousel {
  width: 100%;
  position: relative;
  margin: auto 0;
  @media (max-width: 768px) {
    display: none;
  }
  @media (max-width: 870px) {
    padding: 0 40px;
  }
  .arrowPrev,
  .arrowNext {
    position: absolute;
    color: #fff;
    top: 65px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    svg {
      vertical-align: middle;
    }
  }
  .arrowNext {
    right: -50px;
    @media (max-width: 870px) {
      right: -10px;
    }
  }
  .arrowPrev {
    left: -50px;
    @media (max-width: 870px) {
      left: -10px;
    }
  }
}

.mobilePlanTypeInfo {
  display: none;
  color: #fff;
  padding: 0 24px;
  margin-bottom: 5px;
  margin-top: 20px;
  width: 100%;
  h5 {
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    margin: 0 0 4px 0;
  }
  p {
    font-size: 16px;
    line-height: 24px;
    margin: 0;
  }
  @media (max-width: 768px) {
    display: block;
  }
}

.bottomContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  color: #fff;
  padding: 50px;
  @media (max-width: 768px) {
    padding: 24px 24px 50px 24px;
    button {
      width: 100%;
    }
  }
  .planTypeInfo {
    margin-right: 30px;
    @media (max-width: 768px) {
      display: none;
    }
  }
  h5 {
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    margin: 0 0 4px 0;
  }
  p {
    font-size: 16px;
    line-height: 24px;
    margin: 0;
  }
}
