import styles from "./legal-pop-up.module.scss";
import { motion } from "framer-motion";
import Button from "../Button";
import { Link } from "react-router-dom";
import { t } from "i18next";

const LegalPopUp = ({ privacy, accept }: any) => {
  return (
    <motion.div
      layout
      initial={{ opacity: 1, y: 200 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 1, y: 200 }}
      transition={{
        type: "tween",
        ease: "easeInOut",
        duration: 0.3,
      }}
      className={styles.main}
      key={privacy ? "privacy" : "terms"}>
      <div className={styles.textContainer}>
        <h5 className={styles.heading}>
          {privacy
            ? t('cookies.privacyUpdated')
            : t('cookies.termsUpdated')
          }
        </h5>
      </div>
      <div className={styles.buttons}>
        <Link
          style={{ textDecoration: "none" }}
          to={privacy ? "/privacy-policy" : "/terms-and-conditions"}
        >
          <Button color="tertiary">
            {privacy
              ? t('cookies.privacyRead')
              : t('cookies.termsRead')
            }
          </Button>
        </Link>
        <Button onClick={() => accept()}>{ t('general.continue') }</Button>
      </div>
    </motion.div>
  );
};

export default LegalPopUp;
