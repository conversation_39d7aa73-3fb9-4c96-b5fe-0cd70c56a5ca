@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  background: #fff;
  border-radius: 24px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
}

.topBar {
  display: flex;
  justify-content: space-between;
}

.box {
  position: relative;
  overflow: hidden;
  height: 24px;
  background: $skeleton;
  border-radius: 5px;
}

.bars {
  margin-top: 18px;
  margin-bottom: 16px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}
