import { useEffect, useState } from "react";
import Modal from "../Modal";
import styles from "./included-countries.module.scss";
import { ApiGet } from "../../pages/api/api";

const IncludedCountriesModal = ({ show, setShow }: any) => {
  const [countries, setCountries] = useState([] as any);

  useEffect(() => {
    ApiGet("/plans/countries")
      .then((response: any) => {
        const options = response.data.countries;
        setCountries(options);
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  return (
    <Modal
      show={show}
      style={{ maxWidth: 1021 }}
      clickOff={() => {
        setShow(false);
      }}
      noTransition
    >
      <div className={styles.modalMain}>
        <h2 className={styles.heading}>Countries Covered</h2>
        <div className={styles.itemsGrid}>
          {countries.map((country: any) => (
            <div className={styles.countryDisplay} key={country.countryCode}>
              <div
                className={styles.flag}
                style={{ backgroundImage: `url(${country.iconUrl})` }}
              />
              {country.countryName}
            </div>
          ))}
        </div>
      </div>
    </Modal>
  );
};

export default IncludedCountriesModal;
