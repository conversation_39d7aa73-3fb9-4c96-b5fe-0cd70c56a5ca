@use "../../styles/theme.scss" as *;

.container {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  background: rgba(22, 11, 42, 0.6);
  @media (max-width: 768px) {
    align-items: flex-start;
    padding: 40px 0;
  }
}

.modal {
  width: 95%;
  max-width: 1124px;
  max-height: 80vh;
  border-radius: 24px;
  background: #fff;
  display: flex;
  position: relative;
}

.main {
  width: 100%;
  padding: 63px 74px 60px 74px;
  overflow: auto;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  @media (max-width: 768px) {
    padding: 50px 24px;
  }
  .heading {
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    color: $primary;
    margin: 75px 0 12px 0;
    text-align: center;
    max-width: 733px;
    @media (max-width: 768px) {
      margin: 30px 0 12px 0;
      font-size: 20px;
      line-height: 30px;
    }
  }
  p {
    font-size: 16px;
    line-height: 24px;
    color: $dark-dark-purple;
    margin: 0 0 87px 0;
    text-align: center;
    width: 100%;
    @media (max-width: 768px) {
      margin: 0 0 30px 0;
    }
    a {
      text-decoration: none;
      color: $secondary;
      font-weight: 600;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.button {
  @media (max-width: 768px) {
    button {
      font-size: 12px !important;
    }
  }
}
