@use "../../styles/theme.scss" as *;

.main {
  min-height: 100vh;
  background: #fafaf9;
  padding-top: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  &.bannerOpen {
    padding-top: 160px;
  }
  @media (max-width: 768px) {
    padding-top: 78px;
    &.bannerOpen {
      padding-top: 148px;
    }
  }
}

.content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  @media (max-width: 768px) {
    min-height: calc(100vh - 68px);
  }
}

.notificationWrapper {
  position: fixed;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10000;
  padding: 34px 25px;
  pointer-events: none;
  top: 0px;
}

.legalPopUpContainer {
  bottom: 38px;
  left: 0;
  position: fixed;
  z-index: 9999;
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column-reverse;
  pointer-events: none;
}
