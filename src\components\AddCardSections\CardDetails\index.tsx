import Checkbox from "../../Checkbox";
import { Input } from "../../Input";
import styles from "./card-details.module.scss";
import {
  clearInput,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  placeholders,
} from "../../utils/InputHandlers";
import { validateAll } from "indicative/validator";
import Button from "../../Button";
import { motion } from "framer-motion";
import { checkExpiry } from "../../utils/checkExpiry";
import { t } from "i18next";

// Set up required fields and FLV data for card details
const cardFields = ["cardholderName", "number"];
const inlineCardFields = ["expiry", "cvv"];
const combinedCardFields = [...cardFields, ...inlineCardFields];
const cardRules = getRules(combinedCardFields);
const cardMessages = getMessages(combinedCardFields);

const CardDetails = ({ cardDetails, setCardDetails, cancel, proceed }: any) => {
  const submitCardDetails = () => {
    const data = {
      cardholderName: cardDetails.cardholderName,
      number: cardDetails.number,
      expiry: cardDetails.expiry,
      cvv: cardDetails.cvv,
    };

    validateAll(data, cardRules, cardMessages)
      .then(() => {
        let expiryCheck = checkExpiry(data.expiry);
        if (expiryCheck.error) {
          setCardDetails({
            ...cardDetails,
            errors: {
              ...cardDetails.errors,
              expiry: expiryCheck.message,
            },
          });
        } else {
          proceed();
        }
      })
      .catch((errors) => {
        displayErrors(errors, setCardDetails);
      });
  };

  return (
    <div className={styles.cardDetails}>
      <h3>Add a new card</h3>
      <div className={styles.inputContainer}>
        {cardFields.map((prop) => (
          <Input
            placeholder={placeholders[prop]}
            value={cardDetails[prop]}
            onChange={(e: any) => {
              handleInputChange(prop, e, cardDetails, setCardDetails);
            }}
            error={cardDetails.errors[prop]}
            clear={() => {
              clearInput(prop, setCardDetails);
            }}
            onKeyDown={submitCardDetails}
            key={`input-${prop}`}
          />
        ))}
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gridColumnGap: 13,
          }}
        >
          {inlineCardFields.map((prop) => (
            <Input
              placeholder={placeholders[prop]}
              value={cardDetails[prop]}
              onChange={(e: any) => {
                handleInputChange(prop, e, cardDetails, setCardDetails);
              }}
              error={cardDetails.errors[prop]}
              clear={() => {
                clearInput(prop, setCardDetails);
              }}
              onKeyDown={submitCardDetails}
              key={`input-${prop}`}
            />
          ))}
        </div>
        <div className={styles.disclaimer}>
          By saving this card you are giving your consent for us to store your
          card details for the sole purpose of your future payments in line with
          our information security policies.
        </div>
        <motion.div
          transition={{ durarion: 300, easing: "ease-out" }}
          layoutId="add-card-buttons"
          className={styles.buttons}
        >
          <Button
            style={{ marginRight: 16 }}
            color="secondary"
            onClick={cancel}
          >
            { t('buttons.cancel') }
          </Button>
          <Button onClick={submitCardDetails}>{ t('buttons.save') }</Button>
        </motion.div>
      </div>
    </div>
  );
};

export default CardDetails;
