@use "./theme.scss" as *;

.container {
  position: relative;
  flex-grow: 1;
}

.backButton {
  padding: 0;
  background: none;
  border: none;
  cursor: pointer;
  position: absolute;
  top: 27px;
  left: 55px;
  @media (min-width: 1700px) {
    left: calc((100vw - 1600px) / 2);
  }
  @media (max-width: 768px) {
    left: 24px;
    top: 24px;
  }
  color: $dark-dark-purple;
  svg {
    vertical-align: middle;
  }
}
.verifyMain {
  width: 100%;
  display: flex;
  max-width: 418px;
  flex-direction: column;
  align-items: center;
  padding: 50px 0;
  color: $secondary;
  @media (max-width: 768px) {
    padding: 24px;
    max-width: max-content;
  }
  .verifyTitle {
    font-size: 30px;
    font-weight: 800;
    line-height: 38px;
    letter-spacing: -0.025em;
    text-align: center;
    margin: 0px;
    margin-bottom: 16px;
  }
  p {
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    margin: 0px;
  }
  .resend {
    margin-top: 34px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    h5 {
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      margin: 0px;
      margin-bottom: 4px;
    }
    p {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      margin: 0px;
    }
  }
}

.innerContainer {
  width: 100%;
  max-width: 440px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: auto;
  padding: 50px 0 100px 0;
  text-align: center;
  @media (max-width: 768px) {
    padding: 70px 24px;
  }
  button {
    width: 100%;
    margin-top: 12px;
  }
  h2 {
    font-weight: 700;
    font-size: 40px;
    line-height: 60px;
    color: $secondary;
    margin: 0 0 16px;
    @media (max-width: 768px) {
      font-size: 30px;
      line-height: 45px;
    }
  }
  h4 {
    font-weight: 700;
    font-size: 30px;
    line-height: 36px;
    color: $secondary;
    margin: 0 0 0 12px;
    padding-top: 29px;
    @media (max-width: 768px) {
      font-size: 20px;
      line-height: 30px;
    }
  }
  .text {
    color: $dark-dark-purple;
    font-size: 14px;
    line-height: 24px;
    margin: 20px 0 25px 0;
  }
}
