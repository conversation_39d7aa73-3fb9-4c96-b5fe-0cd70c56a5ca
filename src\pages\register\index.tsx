import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { ApiPostAuth } from "../../pages/api/api";
import Button from "../../components/Button";
import styles from "../../styles/register.module.scss";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import { useSelector } from "react-redux";
import SocialLogin from "../../components/SocialLogin";
import {
  clearInput,
  createStateObject,
  getMessages,
  displayErrors,
  getRules,
  handleInputChange,
} from "../../components/utils/InputHandlers";
import { Input } from "../../components/Input";
import { validateAll } from "indicative/validator";
import {
  createUserWithEmailAndPassword,
  sendEmailVerification,
  signInWithEmailAndPassword,
  deleteUser,
} from "firebase/auth";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";
import auth from "../../components/utils/firebaseAuth";

// Set up required fields and FLV data for personal info
const personalFields = ["email"];
const persoanlRules = getRules(personalFields);
const personalMessages = getMessages(personalFields);

const Register = () => {
  const navigate = useNavigate();
  const [personal, setPersonal] = useState(createStateObject(personalFields));
  const [user, setUser] = useState<any>("");
  const [emailSent, setEmailSent] = useState(false);
  const [loading, setLoading] = useState(false);

  const { loggedIn, isSmartBannerOpen } = useSelector((state: any) => state);
  const { t, i18n } = useTranslation();

  useEffect(() => {
    if (loggedIn) {
      navigate("/dashboard/home");
    }
  }, [loggedIn]);

  const actionCodeSettings = {
    url: `https://${
      import.meta.env.VITE_APP_WEB_URL
    }/finish-registration?email=${personal.email}`,
    handleCodeInApp: true,
  };

  function getActionCodeSettingsWithAccessCode() {
    return {
      ...actionCodeSettings,
      url:
        actionCodeSettings.url +
        `&accessToken=${auth.currentUser?.accessToken}`,
    };
  }

  const handleFacebookNext = (data: any) => {
    const searchParams = new URLSearchParams({
      type: "facebook",
      ...data,
    }).toString();
    navigate(`/finish-registration-social?${searchParams}`);
  };

  const handleCheckUser = () => {
    validateAll(personal, persoanlRules, personalMessages)
      .then(async (response) => {
        try {
          setLoading(true);
          ApiPostAuth("/users/register/email/check", {
            email: personal.email,
          })
            .then((response: any) => {
              signInWithEmailAndPassword(
                auth,
                personal.email,
                "temporaryPassword"
              )
                .then(async (response) => {
                  console.log(response);
                  await deleteUser(auth.currentUser).then(() => {
                    submitEmail();
                  });
                })
                .catch((error) => {
                  console.log(error);
                  submitEmail();
                });
            })
            .catch((errors) => {
              console.log(errors);
              displayErrors(
                [
                  {
                    message: errors.response.data.message,
                    field: "email",
                    validation: "email",
                  },
                ],
                setPersonal
              );
              setLoading(false);
            });
          // Show success message
        } catch (error) {
          submitEmail();
        }
      })
      .catch((errors) => {
        setLoading(false);
        displayErrors(errors, setPersonal);
      });
  };

  const handleGoogleNext = (data: any) => {
    const searchParams = new URLSearchParams({
      type: "google",
      ...data,
    }).toString();
    navigate(`/finish-registration-social?${searchParams}`);
  };

  const handleAppleNext = (data: any) => {
    const searchParams = new URLSearchParams({
      type: "apple",
      ...data,
    }).toString();
    navigate(`/finish-registration-social?${searchParams}`);
  };

  const submitEmail = async () => {
    let excuteFinally = true;
    validateAll(personal, persoanlRules, personalMessages)
      .then(async (response) => {
        try {
          // Step 1: Create the user
          const userCredential = await createUserWithEmailAndPassword(
            auth,
            personal.email,
            "temporaryPassword"
          );
          setUser(userCredential.user);
          await sendEmailVerification(
            userCredential.user,
            getActionCodeSettingsWithAccessCode()
          );

          // Show success message
        } catch (error: any) {
          setLoading(false);
          if (error) excuteFinally = false;
          switch (error.code) {
            case "auth/email-already-in-use":
              displayErrors(
                [
                  {
                    message: `Email address ${personal.email} already in use.`,
                    field: "email",
                    validation: "email",
                  },
                ],
                setPersonal
              );
              break;
            case "auth/invalid-email":
              displayErrors(
                [
                  {
                    message: `Email address ${personal.email} is invalid.`,
                    field: "email",
                    validation: "email",
                  },
                ],
                setPersonal
              );
              break;
            case "auth/operation-not-allowed":
              displayErrors(
                [
                  {
                    message: `Error during sign up.`,
                    field: "email",
                    validation: "email",
                  },
                ],
                setPersonal
              );
              break;
            case "auth/weak-password":
              displayErrors(
                [
                  {
                    message:
                      "Password is not strong enough. Add additional characters including special characters and numbers.",
                    field: "email",
                    validation: "email",
                  },
                ],
                setPersonal
              );
              break;
            default:
              displayErrors(
                [
                  {
                    message: error.message,
                    field: "email",
                    validation: "email",
                  },
                ],
                setPersonal
              );
              break;
          }
        } finally {
          if (excuteFinally) {
            setLoading(false);
            setEmailSent(true);
            setTimeToReset(30);
            setTimeToResetCounter();
            localStorage.setItem(
              "accessToken",
              JSON.stringify(auth.currentUser.accessToken)
            );
          }
        }
      })
      .catch((errors) => {
        displayErrors(errors, setPersonal);
      });
  };

  const resendEmail = async () => {
    if (user) {
      try {
        // Step 2: Send email verification
        await sendEmailVerification(
          user,
          getActionCodeSettingsWithAccessCode()
        );

        // Show success message
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    }
    setTimeToReset(30);
    setTimeToResetCounter();
  };

  const [timeToReset, setTimeToReset] = useState(30);

  const [timer, setTimer] = useState(null as any);

  const setTimeToResetCounter = () => {
    const clearTimer = () => {
      clearInterval(timer);
    };
    clearTimer();
    setTimer(
      setInterval(() => {
        setTimeToReset((prev) => {
          if (prev > 0) {
            return prev - 1;
          } else {
            clearTimer();
            return 0;
          }
        });
      }, 1000)
    );
  };

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          {t("general.orbit")} | {t("account.register")}
        </title>
        <meta
          name="description"
          content="Need Assistance? Orbit Mobile are here to help you with your eSIM anywhere and to answer your questions"
        />
      </Helmet>
      <SwitchTransition>
        <CSSTransition
          key={emailSent ? "email-sent" : "submit-email"}
          addEndListener={(node, done) =>
            node.addEventListener("transitionend", done, false)
          }
          classNames="fade"
        >
          {!emailSent ? (
            <div className={styles.main}>
              <div
                className={
                  styles.links +
                  " flex justify-content-around items-baseline w-full"
                }
                style={{ marginTop: isSmartBannerOpen ? "30px" : "0px" }}
              >
                <Link to="/login" style={{ textDecoration: "none" }}>
                  {t("account.login")}
                </Link>
                <Link
                  to="/register"
                  className={styles.active}
                  style={{ textDecoration: "none" }}
                >
                  {t("account.register")}
                </Link>
              </div>
              <h2 className="title">{t("account.register")}</h2>
              <Input
                value={personal["email"]}
                label={t("account.email")}
                disabled={loading}
                onChange={(e: any) => {
                  handleInputChange("email", e, personal, setPersonal);
                }}
                clear={() => {
                  clearInput("email", setPersonal);
                }}
                error={personal.errors["email"]}
                key="input-email-register"
                onKeyDown={handleCheckUser}
              />
              <Button
                style={{ marginTop: 9, width: "100%" }}
                loading={loading}
                onClick={handleCheckUser}
              >
                {t("general.continue")}
              </Button>
              <SocialLogin
                register
                handleGoogleNext={handleGoogleNext}
                handleFacebookNext={handleFacebookNext}
                handleAppleNext={handleAppleNext}
              />
              {
                i18n.language === 'en' ? (
                  <div className={styles.polices}>
                    By creating an account, you accept{" "}
                    <a href="/terms-and-conditions" target="_blank">
                      Orbit Mobile’s Terms & conditions
                    </a>{" "}
                    and{" "}
                    <a href="/privacy-policy" target="_blank">
                      Privacy Policy
                    </a>
                  </div>
                ) : (
                  <div className={styles.polices}>
                    من خلال إنشاء حساب، فإنك توافق على {" "}
                    <a href="/terms-and-conditions" target="_blank">
                      الشروط والأحكام
                    </a>{" "}
                    <a href="/privacy-policy" target="_blank">
                    وسياسة الخصوصية {" "}
                    </a>
                    الخاصة بتطبيق Orbit Mobile
                </div>
                )
              }
            </div>
          ) : (
            <div className={styles.verifyMain}>
              <h3 className={styles.verifyTitle}>{t("account.verifyMail")}</h3>
              <p>{t("account.verifyMailCheckInbox")}</p>
              <div className={styles.resend}>
                <h5>{t("account.noMail")}</h5>
                {timeToReset !== 0 ? (
                  <p>
                    {t("account.requestNewMailCounter")} 00:
                    {timeToReset.toString().padStart(2, "0")}
                  </p>
                ) : (
                  <Button
                    onClick={resendEmail}
                    style={{ height: 48, marginTop: 16, padding: "0px 50px" }}
                  >
                    {t("account.requestNewMail")}
                  </Button>
                )}
              </div>
            </div>
          )}
        </CSSTransition>
      </SwitchTransition>
    </div>
  );
};

export default Register;
