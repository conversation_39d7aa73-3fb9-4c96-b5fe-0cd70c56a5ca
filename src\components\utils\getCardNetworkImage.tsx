import { Visa, Mastercard, Amex } from "../svgs";

export const getCardNetworkImage = (card: any) => {
  let network = "network" in card ? card.network.toLowerCase() : "";
  return network === "visa" ? (
    <Visa />
  ) : network === "mastercard" ? (
    <Mastercard />
  ) : network === "amex" ? (
    <Amex />
  ) : network === "jcb" ? (
    <img src="/images-int/payment/JCB.png" />
  ) : network === "discover" ? (
    <img src="/images-int/payment/discover.jpg" />
  ) : network === "diners" ? (
    <img src="/images-int/payment/diners.png" />
  ) : network === "unionpay" ? (
    <img src="/images-int/payment/unionpay.svg" />
  ) : (
    ""
  );
};
