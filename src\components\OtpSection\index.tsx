import { useEffect, useState } from "react";
import Button from "../Button";
import OtpInput from "../OtpInput";
import styles from "./otp-section.module.scss";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import { CircularProgress, Fade } from "@mui/material";
import { useDispatch } from "react-redux";

const OtpSection = ({
  countryCode,
  phoneNumber,
  handleOtpSuccess,
  sendToEmail,
  otpValue,
  setOtpValue,
  loading,
  resendOtp,
  style = {},
}: any) => {
  const dispatch = useDispatch();
  const [timeToReset, setTimeToReset] = useState(30);

  const [timer, setTimer] = useState(null as any);

  const setTimeToResetCounter = () => {
    const clearTimer = () => {
      clearInterval(timer);
    };
    clearTimer();
    setTimer(
      setInterval(() => {
        setTimeToReset((prev) => {
          if (prev > 0) {
            return prev - 1;
          } else {
            clearTimer();
            return 0;
          }
        });
      }, 1000)
    );
  };

  useEffect(setTimeToResetCounter, []);

  const [sentToEmail, setSentToEmail] = useState(false);

  return (
    <div className={styles.container} style={style}>
      <h4>
        Enter the code sent to the {sentToEmail ? "email" : "number"} below
      </h4>
      <p className={styles.number}>
        {sentToEmail ? "<EMAIL>" : `${countryCode} ${phoneNumber}`}
      </p>
      {loading ? (
        <CircularProgress
          style={{
            width: 60,
            height: 60,
            marginTop: 30,
            color: "#3E1D6B",
          }}
        />
      ) : (
        <>
          <SwitchTransition>
            <CSSTransition
              key={timeToReset === 0 ? "resend-button" : "timer"}
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="fade"
            >
              {timeToReset === 0 ? (
                <Button
                  style={{ height: 24, marginBottom: 29 }}
                  color="tertiary"
                  onClick={() => {
                    resendOtp(() => {
                      dispatch({
                        type: "notify",
                        payload: {
                          error: false,
                          heading: "Code Re-sent!",
                          message: "Please enter the new code",
                        },
                      });
                      setTimeToReset(30);
                      setTimeToResetCounter();
                    });
                  }}
                >
                  Re-send code
                </Button>
              ) : (
                <p className={styles.resetTime}>
                  Re-send code in {timeToReset} seconds
                </p>
              )}
            </CSSTransition>
          </SwitchTransition>
          {sendToEmail && (
            <Fade in={!sentToEmail}>
              <div>
                <Button
                  style={{ height: 24, marginBottom: 45 }}
                  color="tertiary"
                  onClick={() => {
                    setSentToEmail(true);
                    setTimeToReset(30);
                    setTimeToResetCounter();
                  }}
                >
                  Send the code to my email
                </Button>
              </div>
            </Fade>
          )}
          <OtpInput
            value={otpValue}
            setValue={setOtpValue}
            handleSuccess={handleOtpSuccess}
            loading={loading}
          />
        </>
      )}
    </div>
  );
};

export default OtpSection;
