@use "../../styles/theme.scss" as *;

.main {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
  padding-inline: 0;
  padding-block: 8px 15px;
  > svg {
    width: 150px;
    flex-grow: 1
  }
  @media (max-width: 768px) {
    padding: 50px 24px;
  }
  h5 {
    color: $secondary;
    font-weight: 700;
    font-size: 24px;
    margin: 0;
    @media (max-width: 768px) {
      margin: 0 0 40px 0;
    }
  }
  .buttons {
    display: flex;
    align-items: center;
    @media (max-width: 385px) {
      flex-direction: column-reverse;
      width: 100%;
      button {
        width: 100%;
        margin: 0 0 12px 0 !important;
      }
    }
  }
}
