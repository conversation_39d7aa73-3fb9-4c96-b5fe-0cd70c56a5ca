import { useEffect, useState } from "react";
import styles from "../../styles/new-shop.module.scss";
import { ApiGet, ApiPostAuth } from "../api/api";
import { Alert, FrequencyTick } from "../../components/svgs";
import { formatPrice } from "../../components/utils/formatPrice";
import Button from "../../components/Button";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Fade } from "@mui/material";
import CreditSkeleton from "../../components/CreditSkeleton";
import { v4 as uuidv4 } from "uuid";
import Tooltip from "../../components/Tooltip";
import Toggle from "../../components/Toggle";
import { t } from "i18next";

const AddCredit = () => {
  const dispatch = useDispatch();

  const { loggedIn } = useSelector((state: any) => state);

  const [plans, setPlans] = useState([] as any);

  const [selectedPlan, setSelectedPlan] = useState(null as any);

  const [plansLoading, setPlansLoading] = useState(true);

  const navigate = useNavigate();

  useEffect(() => {
    ApiGet("/plans")
      .then((response: any) => {
        setPlans(response.data.creditPlans);
        setPlansLoading(false);
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  const handleCheckout = () => {
    let planToAdd = { ...selectedPlan };
    planToAdd.basketId = uuidv4();
    planToAdd.planType = "CREDIT";
    planToAdd.autoRenew = autoRenew;
    planToAdd.didType = "NA";
    planToAdd.renewFrequency = selectedFrequency;

    let newBasket = planToAdd;

    localStorage.setItem("creditSelection", JSON.stringify(newBasket));

    dispatch({
      type: "set",
      creditSelection: newBasket,
    });

    if (loggedIn) {
      navigate("/checkout");
    } else {
      navigate("/register");
    }
  };

  const [autoRenew, setAutoRenew] = useState(false);

  const frequencies = [7, 14, 21, 30, 60];

  const [selectedFrequency, setSelectedFrequency] = useState(0);

  useEffect(() => {
    setSelectedFrequency(0);
  }, [autoRenew]);

  return (
    <>
      <div className="max-width-container">
        <div className={styles.mainContainer} style={{ paddingBottom: 46 }}>
          <div className={styles.planType}>Worldwide Credit</div>
          <h1 className={styles.heading}>Select a Credit Amount</h1>
          <h4 className={styles.subHeading}>
            Select the amount of credit you need
          </h4>
          <div className={styles.creditContainer}>
            {plansLoading
              ? Array.from({ length: 8 }).map((x) => <CreditSkeleton />)
              : plans.map((plan: any) => (
                  <div
                    className={`${styles.credit} ${
                      selectedPlan?.planId === plan.planId && styles.active
                    }`}
                    onClick={() => {
                      if (selectedPlan?.planId === plan.planId) {
                        setSelectedPlan(null);
                      } else {
                        setSelectedPlan(plan);
                      }
                    }}
                  >
                    {plan.prices[0].currencySymbol}
                    {plan.prices[0].cost.toString().includes(".")
                      ? formatPrice(plan.prices[0].cost)
                      : plan.prices[0].cost}
                  </div>
                ))}
          </div>
          <div className={styles.autoRenewContainer}>
            <div className={styles.top}>
              <div className={styles.text}>Auto-Renew</div>
              <Toggle
                on={autoRenew}
                onChange={() => {
                  setAutoRenew((prev: any) => !prev);
                }}
              />
            </div>
            <p className={styles.description}>
              Turn on 'auto-renew' to ensure your account always has funds.
              We’ll automatically add funds to your account based on the
              frequency you select. Cancel anytime!
            </p>
            {autoRenew && (
              <div className={styles.frequencyMain}>
                <div className={styles.frequencyTitle}>Select Frequency</div>
                <div className={styles.frequenciesContainer}>
                  {frequencies.map((i: number) => (
                    <div
                      className={`${styles.frequency} ${
                        selectedFrequency === i && styles.active
                      }`}
                      onClick={() => {
                        setSelectedFrequency(i);
                      }}
                    >
                      {selectedFrequency === i && <FrequencyTick />}
                      Every {i} days
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <Fade in={selectedPlan}>
        <div className={styles.bottomSection}>
          <div className={styles.left}>
            <Alert />
            <p className={styles.message}>
              To avoid an error message, make sure that your device is not tied
              to a specific network provider and that your phone is{" "}
              <a
                target="_blank"
                href="https://gistmobile.com/esim-compatibility/"
                style={{ color: "#000" }}
              >
                eSIM-compatible
              </a>
            </p>
          </div>
          <div className={styles.right}>
            <div className={styles.price}>
              Total {selectedPlan && selectedPlan?.prices[0].currencySymbol}
              {selectedPlan
                ? formatPrice(selectedPlan?.prices[0].cost)
                : "0.00"}
            </div>
            <Tooltip
              show={autoRenew && selectedFrequency === 0}
              text="Please select a frequency for the Auto-Renew"
            >
              <Button
                style={{ height: 56, padding: "0 26px" }}
                onClick={handleCheckout}
                disabled={autoRenew && selectedFrequency === 0}
              >
                { t('general.continue') }
              </Button>
            </Tooltip>
          </div>
        </div>
      </Fade>
    </>
  );
};

export default AddCredit;
