@use "./theme.scss" as *;

.container {
  background-color: $secondary;
  flex-grow: 1;
}

.howItWorks {
  background-color: $secondary;
  padding: 70px 80px;
  @media (max-width: 768px) {
    padding: 70px 16px;
  }
  h2 {
    color: #fff;
    font-size: 48px;
    font-weight: 700;
    margin: 0;
    @media (max-width: 768px) {
      font-size: 30px;
    }
  }
  h5 {
    color: #fff;
  }
  p {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 28px;
    font-weight: 300;
    @media (max-width: 768px) {
      font-size: 16px;
    }
  }
  .slide {
    > div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      @media (max-width: 768px) {
        flex-direction: column;
      }
      .rightElement {
        a {
          text-decoration: none;
          color: #0f133a;
        }
        .small {
          margin-top: 15px;
          p {
            font-size: 12px;
          }
        }
        .bgHowItWorks {
          color: #0f133a;
          background-image: url("/images-int/how-it-works/how_it_works_bg.png");
          background-size: 99% 100%;
          background-repeat: no-repeat;
          padding: 60px 20px;
          margin-bottom: 60px;
          @media (max-width: 768px) {
            margin-bottom: 0;
            padding: 20px 20px;
          }
        }
        img {
          margin-inline-end: 10px;
        }
      }
      .leftElement {
        display: flex;
        justify-content: center;
        img {
          width: 350px;
        }
      }
    }
    .leftElement,
    .rightElement {
      width: 42%;
      @media (max-width: 768px) {
        width: 100%;
      }
    }
  }
}
.close {
  position: absolute;
  right: 24px;
  top: 24px;
  cursor: pointer;
  &:dir(rtl) {
    left: 24px;
    right: auto;
  }
  svg {
    stroke: #fff;
  }
}
.howItWorksModal {
  position: relative;
  gap: 10px;
  padding: 20px 35px;
  border: 1px solid rgba(232, 232, 232, 0.8);
  border-radius: 25px;
  &:dir(rtl) {
    align-items: baseline;
  }
  @media (max-width: 768px) {
    flex-direction: column;
    padding: 64px 16px 32px 16px;
  }
  .andriodBorder {
    padding-inline-start: 20px;
    border-inline-start: 1.5px solid #fff;
    @media (max-width: 768px) {
      padding-inline-start: 0;
      border-inline-start: none;
      border-top: 1.5px solid #fff;
      padding-top: 15px;
    }
  }
  > div {
    width: 50%;
    @media (max-width: 768px) {
      width: 100%;
    }
    h5 {
      font-size: 20px;
      margin: 0;
    }
    svg {
      color: #fff;
      width: 17px;
      vertical-align: middle;
    }
    img {
      width: 100%;
    }
    .imgWidth {
      text-align: center;
      img {
        width: 315px;
        &:dir(rtl) {
          width: 280px
        }
        @media (max-width: 768px) {
          width: 220px;
        }
      }
    }
    p {
      color: #fff;
      font-size: 16px;
    }
    a {
      color: #04dcad;
    }
  }
}
