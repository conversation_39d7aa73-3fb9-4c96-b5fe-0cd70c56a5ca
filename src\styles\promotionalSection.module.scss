@use "./theme.scss" as *;
.mainSec {
  width: 65%;
  text-align: center;
  margin: auto;
  @media (max-width: 1024px) {
    width: 90%;
  }
  h2 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    @media (max-width: 1024px) {
      font-size: 40px;
    }
    @media (max-width: 425px) {
      font-size: 30px;
    }
  }
  p {
    width: 70%;
    margin: auto;
    @media (max-width: 1024px) {
      width: 85%;
    }
  }
}
.promotionalSection {
  background-color: $secondary;
  color: #fff;
  padding: 50px 70px;
  @media (max-width: 600px) {
    padding: 50px 10px;
  }
  p {
    font-size: 16px;
    font-weight: 400;
    line-height: 28px;
    @media (max-width: 1024px) {
      font-size: 14px;
      line-height: 22px;
    }
  }
  .features {
    text-align: center;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 70px;
    justify-content: center;
    @media (max-width: 600px) {
      flex-direction: column;
      text-align: initial;
      padding: 15px;
      margin-top: 20px;
    }
    > div {
      width: calc(100% / 3.5);
      @media (max-width: 600px) {
        width: 100%;
        display: flex;
      }
      @media (max-width: 360px) {
        width: 80%;
      }
      img {
        margin-bottom: 15px;
        width: 70px;
        height: 70px;
        @media (max-width: 1024px) {
          width: 45px;
          height: 45px;
        }
      }
      > div {
        @media (max-width: 600px) {
          margin-left: 15px;
        }
        h3 {
          margin: 0;
          font-size: 26px;
          @media (max-width: 1024px) {
            font-size: 19px;
          }
        }
      }
    }
  }
}
