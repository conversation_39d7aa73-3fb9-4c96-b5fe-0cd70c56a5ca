import { useEffect, useState } from "react";
import styles from "./toggle.module.scss";

const Toggle = ({ choices, handleSelected, defaultChoice }: any) => {
  const [selected, setSelected] = useState('')

  useEffect(() => {
    setSelected(defaultChoice)
  }, [defaultChoice])

  const handleToggle = (choice:any) => {
    setSelected(choice)
    handleSelected(choice)
  }

  return (
    <div
      className={`${styles.main}` + ' flex justify-content-between'}
      style={{
        background: "#E8F8F8",
        cursor: "pointer"
      }}>
      {
        choices.map((choice:String, index: number) => {
          return (
            <div key={index} onClick={() => handleToggle(choice)} className={`${!selected && index < 1 ? styles.thumb : ''} ${selected === choice && styles.thumb}`}>
              <span>{ choice }</span>
            </div>
          )
        })
      }
    </div>
  );
};

export default Toggle;
