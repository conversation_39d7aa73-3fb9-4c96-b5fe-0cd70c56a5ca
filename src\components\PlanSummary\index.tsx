import { useState } from "react";
import Toggle from "../Toggle";
import styles from "./plan-summary.module.scss";
import { useDispatch } from "react-redux";
import ConfirmationModal from "../ConfirmationModal";
import formatPlanData from "../utils/formatPlanData";
import { formatPrice } from "../utils/formatPrice";

const PlanSummary = ({ planObj, handleAutoRenewChange }: any) => {
  const dispatch = useDispatch();

  const removeFromBasket = () => {
    let currentBasketStorage = localStorage.getItem("basket");

    let currentBasket = currentBasketStorage
      ? JSON.parse(currentBasketStorage)
      : [];

    let newBasket = currentBasket.filter(
      (basketItem: any) => basketItem.basketId !== planObj.basketId
    );

    localStorage.setItem("basket", JSON.stringify(newBasket));

    dispatch({
      type: "set",
      basket: newBasket,
    });
  };

  const [showModal, setShowModal] = useState(false);

  return (
    <div className={styles.main}>
      <ConfirmationModal
        show={showModal}
        setShow={setShowModal}
        proceed={removeFromBasket}
        heading="Delete bundle?"
        text="Are you sure you’d like to delete this bundle? You will have to shop again if you wish to add it back."
        continueButton="Delete Bundle"
      />
      <div className={styles.row}>
        <div className={styles.planType}>{planObj.classification}</div>
        <div className={styles.price}>
          {planObj.prices[0].currencySymbol}
          {formatPrice(planObj.prices[0].cost)}
        </div>
      </div>
      <div className={styles.row}>
        <div className={styles.countryName}>
          <div
            className={styles.flag}
            style={{ backgroundImage: `url(${planObj.iconUrl})` }}
          />
          <div className={styles.text}>{planObj.countryName}</div>
        </div>
      </div>
      <div className={styles.row}>
        <div>{formatPlanData(planObj)}</div>
      </div>
      <div className={styles.divider} />
      {planObj.autorenewal && (
        <div className={styles.row}>
          <div>Auto-Renew</div>
          <Toggle
            on={planObj.autoRenew}
            onChange={() => {
              handleAutoRenewChange(planObj, !planObj.autoRenew);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default PlanSummary;
