@use "../../styles/theme.scss" as *;

.container {
  position: relative;
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 13px;
  color: $dark-dark-purple;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  background: $light-primary;
  border-radius: 12px;
  width: 153px;
  cursor: pointer;
  svg {
    margin-right: 10px;
  }
}

.clickOff {
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 9999;
}

.menu {
  position: absolute;
  width: 338px;
  background: $secondary;
  box-shadow: 0px 10px 20px rgba(62, 29, 107, 0.2);
  border-radius: 8px;
  right: 0px;
  top: 50px;
  z-index: 10000;
  padding: 24px 26px;
  display: flex;
  flex-direction: column;
  @media (max-width: 850px) {
    right: initial;
    left: 0px;
  }
  &.yearMenu {
    background: $dark-dark-purple;
  }
}

.months {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-column-gap: 27px;
  grid-row-gap: 27px;
  margin-top: 35px;
  padding: 0 7px;
  .month {
    width: 44px;
    height: 44px;
    border-radius: 1000px;
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 12px;
    line-height: 24px;
    justify-content: center;
    cursor: pointer;
    &.active {
      background: #fff;
      color: $dark-dark-purple;
      cursor: auto;
      &:hover {
        background: #fff;
      }
    }
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.selectYear {
  color: #fff;
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  svg {
    width: 24px;
    height: 24px;
    margin-left: 4px;
  }
}
