@use "./theme.scss" as *;
@import url(https://s3.amazonaws.com/assets.freshdesk.com/widget/freshwidget.css);

.mainContainer {
  padding: 0 70px 50px 70px;
  overflow: hidden;
  @media (max-width: 768px) {
    padding: 0 24px 50px 24px;
  }
  .helpCategories {
    text-align: center;
    text-transform: capitalize;
    margin-top: 70px;
    margin-bottom: 40px;
    padding-bottom: 25px;
    border-bottom: 0.5px solid #0f133a;
    @media (max-width: 768px) {
      overflow-y: scroll;
    }
    .helpCategory {
      position: relative;
      //transition: all 0.3s ease;
      cursor: pointer;
      margin-right: 12px;
      &:last-of-type {
        margin-right: 0px;
      }
      @media (max-width: 768px) {
        margin-right: 13px;
        min-width: 143px;
        padding: 26px 8px;
        border-radius: 16px;
        background-color: #efeeed;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        &.active {
          background-color: #fff;
          border: 1px solid #000;
        }
      }
    }
    .active {
      font-weight: bold;
    }
  }
}

.searchContainer {
  position: relative;
  width: 100%;
  @media (max-width: 425px) {
    width: 90%;
  }
}

.searchSection {
  padding: 20px 0 55px 0;
  background-color: #e8f8f8;
  text-align: center;
  position: relative;
  @media (max-width: 1330px) {
    margin-bottom: 35px;
  }
  @media (max-width: 768px) {
    padding: 50px 16px;
  }
  p {
    width: 100%;
    max-width: 686px;
    line-height: 30px;
    margin-top: 0;
    margin-bottom: 10px;
    color: $secondary;
  }
  img {
    position: absolute;
    bottom: -40px;
    right: 80px;
    @media (max-width: 1330px) {
      bottom: -80px;
    }
    @media (max-width: 996px) {
      position: initial;
      margin-top: 50px;
    }

    &:dir(rtl) {
      right: auto;
      left: 80px;
    }
  }
  .searchResults {
    background-color: #fff;
    position: absolute;
    width: 37%;
    margin: auto;
    border-radius: 11px;
    margin-top: 5px;
    padding: 3px 20px;
    font-size: 15px;
    text-align: left;
    box-shadow: 0px 4px 20px 0px #00000040;
    z-index: 99;
    @media (max-width: 1024px) {
      width: 40%;
    }
    @media (max-width: 768px) {
      width: 52%;
      margin-top: -10px;
    }
    @media (max-width: 425px) {
      width: 100%;
    }
    @media (max-width: 320px) {
      padding: 20px;
      font-size: 13px;
    }
    a {
      color: #000c24;
      text-decoration: none;
      text-align: start;
    }
    .notFound {
      margin: 0;
      padding: 15px;
    }
    div {
      margin-bottom: 15px;
      h5 {
        font-size: 16px;
        font-weight: 700;
        margin: 15px 0;
      }
      div {
        p,
        ul {
          display: none;
        }
        p:first-of-type {
          -webkit-line-clamp: 4;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
      }
      p {
        font-size: 14px;
        font-weight: 400;
        width: 100%;
        line-height: 23px;
      }
    }
  }
}
.topQuestions {
  border: 1px solid #000c24;
  border-radius: 20px;
  padding: 30px;
  margin: 30px 250px;
  @media (max-width: 1145px) {
    padding: 20px;
    margin: 30px 20px;
  }
  > div {
    margin-inline-start: 0;
  }
}
.container {
  margin: auto;
  width: 50%;
  @media (max-width: 1145px) {
    width: auto;
  }
}
.faqsSection,
.searchSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  h1 {
    font-weight: 700;
    font-size: 48px;
    line-height: 60px;
    color: $secondary;
    margin-bottom: 25px;
    @media (max-width: 768px) {
      font-size: 30px;
      line-height: 45px;
    }
  }
}

.faqsSection {
  margin-bottom: 79px;
  h2 {
    margin: 0 0 61px 0;
    @media (max-width: 768px) {
      margin: 0 0 35px 0;
    }
  }
}

.helpInteralPage {
  max-width: 686px;
  margin: 60px auto 40px auto;
  color: $secondary;
  font-size: 18px;
  line-height: 28px;
  @media (max-width: 768px) {
    padding: 24px;
    font-size: 16px;
    line-height: 24px;
  }
  h1 {
    font-size: 30px;
    font-weight: 800;
    line-height: 38px;
    letter-spacing: -0.025em;
    margin: 40px 0px 16px 0px;
  }
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 21px;
  a {
    text-decoration: none;
    color: #000;
    &:hover {
      text-decoration: underline;
    }
  }
  svg {
    margin: 0px 8px;
  }
}

.divider {
  width: 100%;
  height: 2px;
  background-color: #efeeed;
  margin: 40px 0px;
}

.contact {
  background: linear-gradient(
    90deg,
    rgba(205, 247, 238, 1) 0%,
    rgba(157, 208, 241, 1) 100%
  );
  margin: 50px auto;
  text-align: center;
  border-radius: 20px;
  padding: 60px;
  max-width: 966px;
  @media (max-width: 425px) {
    margin: 40px 20px;
    padding: 40px 25px;
  }
  h2 {
    font-size: 30px;
    margin-top: 10px;
    @media (max-width: 425px) {
      font-size: 25px;
    }
  }
  p {
    font-size: 18px;
    line-height: 25px;
    @media (max-width: 425px) {
      font-size: 14px;
    }
  }
  svg {
    width: auto;
  }
}

.noResults {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding-bottom: 180px;
  .heading {
    color: $primary;
    font-weight: 700;
    font-size: 40px;
    line-height: 60px;
    text-align: center;
    margin: 0 0 8px;
    z-index: 2;
  }
  p {
    text-align: center;
    font-size: 16px;
    line-height: 24px;
    margin: 0 0 62px;
    z-index: 2;
  }
  .buttons {
    display: flex;
    align-items: center;
    z-index: 2;
  }
  .tree,
  .bush {
    position: absolute;
    z-index: 1;
  }
  .tree {
    right: -200px;
    bottom: 0px;
  }
  .bush {
    left: -150px;
    bottom: 0px;
  }
}

.formSection {
  padding: 100px 200px;
  @media (max-width: 1024px) {
    padding: 20px 40px;
  }
  iframe {
    border: 0;
  }
  .copyright {
    display: none;
  }
}
