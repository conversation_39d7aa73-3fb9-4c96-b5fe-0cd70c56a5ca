@use "../../styles/theme.scss" as *;

.main {
  cursor: pointer;
  @media (min-width: 768px) {
    &:hover {
      .imageContainer img {
        transform: scale(1);
      }
    }
  }
}

.imageContainer {
  padding-bottom: 71.77%;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  @media (max-width: 768px) {
    border-radius: 8px;
  }
  .stickyImageContainer {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  img {
    height: auto;
    width: auto;
    min-height: 100%;
    min-width: 100%;
    transform: scale(1.33);
    transition: transform 0.75s ease;
    @media (max-width: 768px) {
      transform: initial;
    }
  }
}

.title {
  font-weight: 500;
  font-size: 20px;
  line-height: 30px;
  color: $dark-dark-purple;
  margin: 0;
  margin-top: 24px;
  @media (max-width: 768px) {
    font-size: 16px;
    line-height: 24px;
    margin-top: 12px;
  }
}
