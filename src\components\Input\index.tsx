/*
==========================================================================================

                                        Input

Description: Input component for use accross the whole app
             with label, input and inline errors

Parameters: label (str) - label for the input
            placeholder (str) - placeholder for the input
            value (str) - input value state
            onChange (func) - function to handle input change
            error (str) - inline error for input
            onKeyDown (func) - function to call when user 
                               presses enter while focus on input
            password (bool) - whether input is a password field
            forgotPassword (bool) - whether to show forgot password link, 
                                    only used for login page
            clear (func) - function to call when clear button is pressed
            disabled (bool) - whether input is disabled
            tooltip (string) - string to display on tooltip

==========================================================================================
*/

import { useEffect, useRef, useState } from "react";
import styles from "./input.module.scss";
import { Collapse } from "@mui/material";
import { t } from "i18next";
import { Clear, Eye, EyeSlash } from "../svgs";

export const Input = ({
  label,
  placeholder,
  value,
  onChange,
  error,
  onKeyDown,
  password,
  clear,
  disabled,
  id = null,
  sublink,
  noClear,
}: any) => {
  const [type, setType] = useState(password ? "password" : "text");
  const [show, setShow] = useState(false);

  // Handles change from password to text on 'eye' icon click
  const handleShowChange = () => {
    if (show) {
      setType("password");
      setShow(false);
    } else {
      setType("text");
      setShow(true);
    }
  };

  const ref = useRef(null) as any;

  useEffect(() => {
    setTimeout(() => {
      if (ref.current && ref.current.value.length > 0) {
        ref.current.selectionStart = ref.current.value.length;
        ref.current.selectionEnd = ref.current.value.length;
      }
    }, 0);
  }, [type]);

  return (
    <form
      style={{ width: "100%" }}
      autoComplete="none"
      onKeyPress={(event: any) => {
        return event.keyCode != 13;
      }}
    >
      <div className={styles.inputContainer}>
        <div className={styles.inputWrapper}>
          <div className="flex justify-content-between items-baseline">
            {label && <label>{label}</label>}
            {sublink}
          </div>
          <input
            id={id}
            className={`${styles.input} ${password && styles.password} ${
              error && styles.error
            } ${noClear && styles.noClear}`}
            value={value}
            placeholder={placeholder}
            onChange={(e: any) => {
              onChange(e);
            }}
            onKeyDown={(event) => {
              if (event.key === "Enter") {
                event.preventDefault();
                onKeyDown();
              }
            }}
            type={type}
            disabled={disabled}
            ref={ref}
          />
          {!noClear && (
            <div
              className={`${styles.clearIcon} ${password ? styles.isPasswordInput : ''}`}
              onMouseDown={(e) => {
                e.preventDefault();
              }}
              onClick={clear}
            >
              <Clear />
            </div>
          )}
          {password && (
            <div
              className={styles.eyeIcon}
              onClick={handleShowChange}
              onMouseDown={(e) => {
                e.preventDefault();
              }}
            >
              {show ? <Eye /> : <EyeSlash />}
            </div>
          )}
        </div>
        <Collapse in={error ? true : false}>
          <div className={styles.errorText} id={`${id}-error`}>
            <img
              src="/images-int/WarningCircle.svg"
              className={styles.errorIcon}
              onMouseDown={(e) => {
                e.preventDefault();
              }}
            />
            { t(`${error}`) || <br />}
          </div>
        </Collapse>
      </div>
    </form>
  );
};
