import { useMediaQuery } from "@mui/material";
import Modal from "../Modal";
import styles from "./reward-modal.module.scss";
import Button from "../Button";
import { useState } from "react";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";

const RewardModal = ({ show, setShow, register }: any) => {
  const under768 = useMediaQuery("(max-width: 768px)");

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);

  const redeemReward = () => {
    setLoading(true);
    ApiPostAuth("/users/reward")
      .then((response: any) => {
        setLoading(false);
        navigate("/dashboard/home");
      })
      .catch((error: any) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: t('buttons.oops'),
            message: error.response.data.message,
          },
        });
        if (register) {
          navigate("/register-success");
        } else {
          navigate("/dashboard/home");
        }
      });
  };

  return (
    <Modal
      show={show}
      style={{ height: under768 ? "auto" : 550, maxWidth: 1124 }}
    >
      <div className={styles.main}>
        <img src="/images-int/login/gift.png" className={styles.giftImage} />
        <h3>Welcome back!</h3>
        <p>
          Congratulations! As a token of appreciation for being a previous
          member of Gist, you have been awarded{" "}
          <span style={{ fontWeight: 700 }}>
            100MB Europe Data bundle valid for 7 days
          </span>
          . Enjoy!
        </p>
        <Button
          style={{
            marginTop: 32,
            fontSize: 14,
            fontWeight: 600,
            lineHeight: "21px",
            height: 52,
            width: "100%",
            maxWidth: 243,
          }}
          onClick={redeemReward}
          loading={loading}
        >
          Awesome
        </Button>
      </div>
    </Modal>
  );
};

export default RewardModal;
