import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import PlansPageLayout from "../../components/PlansPageLayout";
import styles from "../../styles/select-credit.module.scss";
import Button from "../../components/Button";
import RatesModal from "../../components/RatesModal";
import { v4 as uuidv4 } from "uuid";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import { ApiGet, ApiGetNoAuth } from "../api/api";

const SelectCredit = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { countryCode } = useParams();
  const { selectedCountry, toastersOnLoad } = useSelector(
    (state: any) => state
  );

  const [plans, setPlans] = useState([] as any);

  useEffect(() => {
    setPlans([] as any);
    ApiGet(`/plans?planType=credit`)
      .then((response) => {
        setPlans(response.data.creditPlans);
        setActiveAmount(response.data.creditPlans[2].planId);
      })
      .catch((error) => {
        console.log(error);
      });
  }, [countryCode]);

  const handleAddToCart = () => {
    let planToAdd = plans.find(
      (planItem: any) => planItem.planId === activeAmount
    ) as any;
    planToAdd.iconUrl = selectedCountry.iconUrl;
    planToAdd.countryName = selectedCountry.countryName;
    planToAdd.basketId = uuidv4();
    planToAdd.planType = "CREDIT";
    planToAdd.didType = "NA";
    planToAdd.autoRenew = false;

    let currentBasketStorage = localStorage.getItem("basket");

    let currentBasket = currentBasketStorage
      ? JSON.parse(currentBasketStorage)
      : [];

    let newBasket = [...currentBasket, planToAdd];

    localStorage.setItem("basket", JSON.stringify(newBasket));

    dispatch({
      type: "addToBasket",
      payload: planToAdd,
    });

    dispatch({
      type: "set",
      toastersOnLoad: {
        ...toastersOnLoad,
        addedToBasket: true,
      },
    });

    navigate(`/shop/select-plan/${countryCode}`);
  };

  const [activeAmount, setActiveAmount] = useState(null);

  const [showRates, setShowRates] = useState(false);

  // store controlled swiper instance
  const [controlledSwiper, setControlledSwiper] = useState(null as any);
  const [activeIndex, setActiveIndex] = useState(2);

  return (
    <PlansPageLayout
      selectedCountry={selectedCountry}
      backLink={`/shop/select-plan/${countryCode}`}
    >
      <RatesModal show={showRates} setShow={setShowRates} />
      <div className={styles.mobilePlanTypeInfo}>
        <h5>Global Credit</h5>
        <div style={{ display: "flex" }}>
          <p>
            Credit for calls anywhere in the world.
            <span
              onClick={() => {
                setShowRates(true);
              }}
              className={styles.checkRates}
            >
              Check Global Rates
            </span>
          </p>
        </div>
      </div>
      <div className={styles.mainContainer}>
        <div className={styles.amountsContainer}>
          {plans.map((planItem: any) => (
            <div
              className={`${styles.creditAmountContainer} ${
                activeAmount === planItem.planId && styles.active
              }`}
              onClick={() => {
                setActiveAmount(planItem.planId);
              }}
              key={`plan-item-${planItem.planId}`}
            >
              <div className={styles.creditAmount}>
                {planItem.prices[0].currencySymbol}
                {planItem.prices[0].cost}
              </div>
            </div>
          ))}
        </div>
        <div className={`${styles.mobilePlansContainer} credit-carousel`}>
          <Swiper
            spaceBetween={0}
            slidesPerView={2.5}
            speed={700}
            centeredSlides
            onSwiper={(swiper: any) => setControlledSwiper(swiper)}
            onSlideChange={(swiper: any) => {
              setActiveIndex(swiper.activeIndex);
            }}
            initialSlide={2}
          >
            {plans.map((planItem: any, index: any) => (
              <SwiperSlide key={"plan-tile-" + index}>
                <div
                  className={`${styles.creditAmountContainer} ${styles.active}`}
                >
                  <div
                    className={styles.creditAmount}
                    style={{
                      transform: activeIndex === index ? "" : "scale(0.5)",
                      opacity: activeIndex === index ? "1" : "0.75",
                      color: activeIndex === index ? "#fff" : "#160B2A",
                      backgroundColor:
                        activeIndex === index ? "#7448B0" : "#EEE7F8",
                    }}
                  >
                    {planItem.prices[0].currencySymbol}
                    {planItem.prices[0].cost}
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
      <div className={styles.bottomContainer}>
        <div className={styles.planTypeInfo}>
          <h5>Worldwide Credit</h5>
          <p>
            Select the credit amount that gives you the freedom to make and
            receive calls and text anywhere in the world.
          </p>
          <button
            onClick={() => {
              setShowRates(true);
            }}
            className={styles.checkRates}
          >
            Check Global Rates
          </button>
        </div>
        <Button onClick={handleAddToCart}>Add to cart</Button>
      </div>
    </PlansPageLayout>
  );
};

export default SelectCredit;
