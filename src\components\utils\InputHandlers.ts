import { t } from "i18next";
import {
  checkAddress,
  checkCode,
  checkName,
  checkPostcode,
  isNumeric,
} from "./CardDetailsCheckers";

const rules = {
  email: "required|email",
  password: "required",
  newPassword:
    "regex:^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$.%^&*]).*$|required|min:6",
  confirmNewPassword:
    "regex:^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$.%^&*]).*$|required|min:6|same:newPassword",
  regPassword:
    "regex:^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$.%^&*]).*$|required|min:6",
  confirmRegPassword:
    "regex:^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$.%^&*]).*$|required|min:6|same:regPassword",
  name: "required|min:2",
  number: "required|min:12",
  expiry: "required|min:5",
  cvv: "required|min:3",
  cardholderName: "required|min:2",
  address: "required",
  city: "required|min:2",
  postcode: "required|min:4",
  country: "required",
  countryCode: "min:2",
  phoneNumber: "min:7",
  firstName: "required|min:2",
  lastName: "required|min:2",
} as any;

const passwordMessage = 'errors.invalidPass'

const messages = {
  "email.required": 'errors.email',
  "email.email": 'errors.email',
  "password.required": 'errors.password',
  "newPassword.required": 'errors.password',
  "newPassword.regex": passwordMessage,
  "newPassword.min": passwordMessage,
  "confirmNewPassword.required": 'errors.confirmPassword',
  "confirmNewPassword.regex": passwordMessage,
  "confirmNewPassword.min": passwordMessage,
  "confirmNewPassword.same": 'errors.matchPassword',
  "regPassword.required": 'errors.password',
  "regPassword.regex": passwordMessage,
  "regPassword.min": passwordMessage,
  "confirmRegPassword.required": 'errors.confirmPassword',
  "confirmRegPassword.regex": passwordMessage,
  "confirmRegPassword.min": passwordMessage,
  "confirmRegPassword.same": 'errors.matchPassword',
  "name.required": 'errors.name',
  "name.min": 'errors.name',
  "number.required": 'errors.number',
  "number.min": 'errors.card',
  "expiry.required": 'errors.expiry',
  "expiry.min": 'errors.validExpiry',
  "cvv.required": 'errors.cvv',
  "cvv.min": 'errors.validCVV',
  "cardholderName.required": 'errors.cardholderName',
  "cardholderName.min": 'errors.cardholderName',
  "address.required": 'errors.address',
  "city.required": 'errors.city',
  "city.min": 'errors.city',
  "postcode.required": 'errors.postcode',
  "postcode.min": 'errors.validPostcode',
  "country.required": 'errors.country',
  "countryCode.required": 'errors.code',
  "countryCode.min": 'errors.code',
  "phoneNumber.required": 'errors.phone',
  "phoneNumber.min": 'errors.validPhone',
  "firstName.required": 'errors.firstName',
  "firstName.min": 'errors.firstName',
  "lastName.required": 'errors.lastName',
  "lastName.min": 'errors.lastName',
} as any;

export const placeholders = {
  email: t('account.email'),
  password: t('account.password'),
  currentPassword: "Current Password",
  newPassword: "New Password",
  confirmNewPassword: "Confirm New Password",
  regPassword: t('account.password'),
  confirmRegPassword: "Confirm Password",
  cardholderName: "Cardholder full name",
  number: "Card number",
  expiry: "Expiry Date",
  cvv: "CVV",
  name: "Full Name",
  address: "Address",
  city: "City",
  postcode: "Postal Code",
  countryCode: "+44",
  phoneNumber: "Phone Number",
  firstName: "First Name",
  lastName: "Last Name",
} as any;

export const getRules = (fields: any) => {
  let obj = {} as any;
  fields.forEach((field: string) => {
    obj[field] = rules[field];
  });
  return obj;
};

export const getMessages = (fields: any) => {
  let messageKeys = Object.keys(messages);
  messageKeys = messageKeys.filter((key: any) =>
    fields.some((field: any) => key.includes(field))
  );
  let obj = {} as any;
  messageKeys.forEach((key: string) => {
    obj[key] = messages[key];
  });
  return obj;
};

export const createStateObject = (arr: any) => {
  let obj = {} as any;
  for (let i = 0; i < arr.length; i++) {
    obj[arr[i]] = "";
  }
  let objWithErrors = {
    ...obj,
    errors: {
      ...obj,
    },
  };
  if (arr.some((prop: any) => prop === "country")) {
    objWithErrors.country = {
      value: "",
      label: "Country",
      flag: "https://public-trill-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/GB.png",
    };
  }
  return objWithErrors;
};

export const clearInput = (prop: any, setState: any) => {
  setState((prev: any) => {
    return {
      ...prev,
      [prop]: "",
      errors: {
        ...prev.errors,
        [prop]: "",
      },
    };
  });
};

export const displayErrors = (errors: any, setState: any) => {
  let formattedErrors = {} as any;
  errors.forEach((error: any) => {
    formattedErrors[error.field] = error.message;
  });
  setState((prev: any) => {
    return {
      ...prev,
      errors: formattedErrors,
    };
  });
};

// Handles change in card information inputs
export const handleInputChange = (
  prop: string,
  e: any,
  state: any,
  setState: any
) => {
  const set = (value: any) => {
    setState((prev: any) => {
      return {
        ...prev,
        [prop]: value,
        errors: {
          ...prev.errors,
          [prop]: "",
        },
      };
    });
  };

  if (
    prop === "name" ||
    prop === "firstName" ||
    prop === "lastName" ||
    prop === "cardholderName"
  ) {
    if (checkName(e.target.value) && e.target.value.length <= 60) {
      set(e.target.value);
    }
  } else if (prop === "cvv") {
    if (
      (e.target.value.length <= 4 && isNumeric(e.target.value)) ||
      e.target.value === ""
    ) {
      set(e.target.value);
    }
  } else if (prop === "number") {
    if (e.target.value === "" || e.target.value === " ") {
      set("");
    } else {
      let justNumbers = e.target.value.replaceAll(" ", "");
      let split = justNumbers.match(/.{1,4}/g);
      let withSpaces = split.join(" ");
      if (isNumeric(justNumbers) && justNumbers.length <= 19) {
        set(withSpaces);
      }
    }
  } else if (prop === "expiry") {
    let number = e.target.value.replaceAll("/", "");

    if (isNumeric(number)) {
      if (number.length === 2) {
        if (e.target.value.length === 3 || state.expiry.length === 3) {
          set(number);
        } else {
          set(number + "/");
        }
      } else if (number.length === 3 || number.length === 4) {
        let newValue = [number.slice(0, 2), "/", number.slice(2)].join("");
        set(newValue);
      } else if (number.length === 1) {
        set(number);
      }
    } else if (number === "") {
      set(number);
    }
  } else if (prop === "address") {
    if (checkAddress(e.target.value) && e.target.value.length <= 50) {
      set(e.target.value);
    }
  } else if (prop === "city") {
    if (checkName(e.target.value) && e.target.value.length <= 60) {
      set(e.target.value);
    }
  } else if (prop === "postcode") {
    if (checkPostcode(e.target.value) && e.target.value.length <= 15) {
      set(e.target.value);
    }
  } else if (prop === "countryCode") {
    let code = e.target.value;
    if (checkCode(code) && code.length <= 5) {
      if (!code.includes("+") && code !== "") {
        set("+" + code);
      } else if ((code.match(/\+/g) || []).length <= 1) {
        set(code);
      }
    }
  } else if (prop === "phoneNumber") {
    if (
      (isNumeric(e.target.value) || e.target.value === "") &&
      e.target.value.length <= 15
    ) {
      set(e.target.value);
    }
  } else {
    set(e.target.value);
  }
};
