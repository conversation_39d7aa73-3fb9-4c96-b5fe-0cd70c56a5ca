@use "./theme.scss" as *;

.container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  .links {
    a {
      font-size: 14px;
      color: $secondary;
      width: 100%;
      text-align: center;
      &.active {
        font-weight: 700;
        padding-bottom: 10px;
        height: 10px;
        border-radius: 20px;
        box-shadow: 0 30px 0 -1px #05BFBE;
      }
    }
  }
}

.main {
  width: 100%;
  max-width: 327px;
  padding: 40px 0 25px 0;
  margin: 0 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  h2 {
    margin-top: 60px;
  }
  @media (max-width: 768px) {
    margin: 0 24px;
  }
}
