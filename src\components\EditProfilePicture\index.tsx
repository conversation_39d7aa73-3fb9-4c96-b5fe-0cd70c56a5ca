import Button from "../Button";
import Modal from "../Modal";
import { Camera, Close, Trash, Upload, UserThin } from "../svgs";
import styles from "./edit-profile-picture.module.scss";
import $ from "jquery";
import { v4 as uuidv4 } from "uuid";
import { useState } from "react";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import { useMediaQuery } from "@mui/material";
import { ApiDelete, ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { t } from "i18next";

const EditProfilePicture = ({ show, setShow, picture, reload }: any) => {
  const dispatch = useDispatch();
  const [newProfilePicture, setNewProfilePicture] = useState(null as any);

  const [takingPhoto, setTakingPhoto] = useState(false);

  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  const videoWidth = 232;

  const [loading, setLoading] = useState(false);

  const handleUpload = () => {
    $("#profile-picture-upload").trigger("click");
  };

  const FileToBase64 = (file: any) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  };

  const handleFileUpload = async (e: any) => {
    let file = e.target.files[0];
    let base64File = await FileToBase64(file);
    const fileObj = {
      id: uuidv4(),
      name: file.name,
      file: base64File,
      body: file,
      type: "upload",
    };
    setNewProfilePicture(fileObj);
  };

  const takePhoto = () => {
    setTakingPhoto(true);
    const video = document.getElementById("video-input") as any;
    navigator.mediaDevices
      .getUserMedia({ video: { facingMode: "user" }, audio: false })
      .then((stream) => {
        video.srcObject = stream;
        video.play();
      })
      .catch((err) => {
        console.error(`An error occurred: ${err}`);
      });
  };

  const under900 = useMediaQuery("(max-width: 900px");

  const dataUrlToFile = async (
    dataUrl: string,
    fileName: string
  ): Promise<File> => {
    const res: Response = await fetch(dataUrl);
    const blob: Blob = await res.blob();
    return new File([blob], fileName, { type: "image/png" });
  };

  const handleCapturePhoto = async () => {
    const video = document.getElementById("video-input") as any;
    const canvas = document.getElementById("canvas") as any;

    const width = (video.videoWidth / video.videoHeight) * videoWidth;
    const height = (video.videoHeight / video.videoWidth) * videoWidth;

    if (under900) {
      canvas.setAttribute("width", videoWidth);
      canvas.setAttribute("height", height);

      const context = canvas.getContext("2d");
      context.drawImage(video, 0, 0, videoWidth, height);
    } else {
      canvas.setAttribute("width", width);
      canvas.setAttribute("height", videoWidth);

      const context = canvas.getContext("2d");
      context.drawImage(video, 0, 0, width, videoWidth);
    }

    const data = canvas.toDataURL("image/png");
    const newFile = await dataUrlToFile(data, `${uuidv4()}.png`);
    setNewProfilePicture({
      body: newFile,
      file: data,
      type: "camera",
    });
    stopCamera();
  };

  const stopCamera = () => {
    setTakingPhoto(false);
    const video = document.getElementById("video-input") as any;
    const stream = video.srcObject;
    if (stream) {
      const tracks = stream.getTracks();

      tracks.forEach((track: any) => {
        track.stop();
      });
    }
  };

  const under768 = useMediaQuery("(max-width: 768px)");

  const handleError = (message: string = "") => {
    setLoading(false);
    dispatch({
      type: "notify",
      payload: {
        error: true,
        heading: t('buttons.oops'),
        message: message || t('account.errors.somethingWrong'),
      },
    });
  };

  const handleDelete = () => {
    setLoading(true);
    ApiDelete("/users/delete-picture", { pictureUrl: picture })
      .then((response) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: "Profile image deleted!",
            message: "Your profile image has been deleted successfully!",
          },
        });
        setLoading(false);
        setShow(false);
        setNewProfilePicture(null as any);
        $("#profile-picture-upload").val("");
        reload();
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: t('buttons.oops'),
            message: "An unexpected error occurred",
          },
        });
      });
  };

  const handleSavePhoto = () => {
    setLoading(true);
    console.log(newProfilePicture);
    const splitName = newProfilePicture.body.name.split(".");
    const extenstion = "." + splitName[splitName.length - 1];
    ApiPostAuth("/users/upload-picture", {
      fileExtension: extenstion,
      base64String: newProfilePicture.file.split(",")[1],
    })
      .then((response) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: t('account.msgs.success'),
            message: response.data.message,
          },
        });
        setLoading(false);
        setShow(false);
        setNewProfilePicture(null as any);
        $("#profile-picture-upload").val("");
        reload();
      })
      .catch((error) => {
        handleError(error.response.data.message);
      });
  };

  return (
    <Modal
      show={show}
      style={{ height: under768 ? "auto" : 550, maxWidth: 1124 }}
    >
      <Modal show={showCancelConfirm} style={{ height: "auto", maxWidth: 700 }}>
        <div className={styles.cancelMain}>
          <h4>Are you sure?</h4>
          <p>If you cancel now, the uploaded image will not be saved.</p>
          <div className={styles.buttons}>
            <Button
              color="secondary"
              style={{ marginRight: 16 }}
              onClick={() => {
                setShowCancelConfirm(false);
              }}
            >
              No, keep editing
            </Button>
            <Button
              onClick={() => {
                setNewProfilePicture(null as any);
                $("#profile-picture-upload").val("");
                setShowCancelConfirm(false);
              }}
            >
              Yes, discard changes
            </Button>
          </div>
        </div>
      </Modal>
      <div className={styles.main}>
        <div
          className={styles.close}
          onClick={() => {
            stopCamera();
            setShow(false);
            setNewProfilePicture(null as any);
            setTakingPhoto(false);
          }}
        >
          <Close />
        </div>
        <h4>Profile picture</h4>
        <div className={styles.content}>
          <div
            className={styles.picture}
            style={{
              backgroundImage: `url(${
                newProfilePicture ? newProfilePicture.file : picture
              })`,
              transform: newProfilePicture
                ? newProfilePicture.type === "camera" && !takingPhoto
                  ? "scaleX(-1)"
                  : ""
                : "",
            }}
          >
            {(!picture || picture === "null") && !newProfilePicture && (
              <UserThin />
            )}
            <video
              id="video-input"
              autoPlay={true}
              playsInline={true}
              muted={true}
              style={{
                visibility: takingPhoto ? "visible" : "hidden",
                borderRadius: 1000,
              }}
            />
          </div>
          <input
            type="file"
            style={{ display: "none" }}
            id="profile-picture-upload"
            onChange={(e) => {
              handleFileUpload(e);
            }}
          />
          <canvas id="canvas" style={{ display: "none" }} />
          <SwitchTransition>
            <CSSTransition
              key={
                takingPhoto
                  ? "taking-photo"
                  : newProfilePicture
                  ? "show-new"
                  : "show-current"
              }
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="fade"
            >
              <div className={styles.buttons}>
                {takingPhoto ? (
                  <>
                    <Button
                      onClick={handleCapturePhoto}
                      style={{ marginBottom: 22 }}
                    >
                      <Camera />
                      Take photo
                    </Button>
                    <Button color="secondary" onClick={stopCamera}>
                      Cancel
                    </Button>
                  </>
                ) : newProfilePicture ? (
                  <>
                    <div style={{ display: "flex", marginBottom: 21 }}>
                      <Button
                        color="secondary"
                        style={{ marginRight: 16 }}
                        onClick={() => {
                          setShowCancelConfirm(true);
                        }}
                        disabled={loading}
                      >
                        Cancel
                      </Button>
                      <Button loading={loading} onClick={handleSavePhoto}>
                      { t('buttons.save') }
                      </Button>
                    </div>
                    {newProfilePicture.type === "camera" ? (
                      <Button
                        style={{ padding: 0 }}
                        color="tertiary"
                        onClick={takePhoto}
                        disabled={loading}
                      >
                        <Camera />
                        Take new picture
                      </Button>
                    ) : (
                      <Button
                        style={{ padding: 0 }}
                        color="tertiary"
                        onClick={handleUpload}
                        disabled={loading}
                      >
                        <Upload />
                        Upload new picture
                      </Button>
                    )}
                  </>
                ) : (
                  <>
                    <Button onClick={takePhoto} style={{ marginBottom: 22 }}>
                      <Camera />
                      Take new picture
                    </Button>
                    <Button onClick={handleUpload}>
                      <Upload />
                      Upload new picture
                    </Button>
                    {picture && picture !== "null" && (
                      <Button
                        onClick={handleDelete}
                        style={{ marginTop: 22, background: "#160B2A" }}
                        loading={loading}
                      >
                        <Trash />
                        Delete picture
                      </Button>
                    )}
                  </>
                )}
              </div>
            </CSSTransition>
          </SwitchTransition>
        </div>
      </div>
    </Modal>
  );
};

export default EditProfilePicture;
