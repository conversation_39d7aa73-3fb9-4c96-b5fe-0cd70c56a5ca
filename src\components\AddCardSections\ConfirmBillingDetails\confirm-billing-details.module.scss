.main {
  width: 872px;
  min-height: 550px;
  padding: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  @media (max-width: 985px) {
    width: 95vw;
  }
  @media (max-width: 700px) {
    padding: 65px 24px 0px 24px;
  }
  .addHeading {
    margin-bottom: 87px;
    @media (max-width: 985px) {
      margin-bottom: 40px;
    }
  }
  .buttons {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    @media (max-width: 768px) {
      background: #fff;
      position: sticky;
      bottom: 0px;
      padding: 12px 0 24px 0;
    }
    @media (max-width: 545px) {
      flex-direction: column-reverse;
      margin-top: 50px !important;
      button {
        margin: 0 0 12px 0 !important;
        width: 100%;
      }
    }
  }
}

.data {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  justify-items: center;
  margin-bottom: 73px;
  width: 100%;
  @media (max-width: 985px) {
    display: flex;
    justify-content: space-evenly;
  }
  @media (max-width: 700px) {
    justify-content: space-between;
  }
  @media (max-width: 630px) {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 40px;
  }
}

.billing {
  @media (max-width: 630px) {
    margin-top: 30px;
  }
  h5 {
    font-size: 16px;
    line-height: 24px;
    margin: 0 0 12px 0;
  }
  p {
    margin: 0;
    font-size: 16px;
    line-height: 24px;
  }
}

.billingAddress {
  background: #eff1f7;
  border-radius: 8px;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 24px;
  margin-bottom: 12px;
  max-width: 734px;
  @media (max-width: 768px) {
    display: grid;
    grid-template-columns: 24px 1fr;
  }
}
