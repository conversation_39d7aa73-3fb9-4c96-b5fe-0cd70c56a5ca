import styles from "./logout-modal.module.scss";
import Modal from "../Modal";
import Button from "../Button";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { ApiPostAuth } from "../../pages/api/api";
import { useState } from "react";
import { DoorOpen, LogoutCircles } from "../svgs";
import auth from "../utils/firebaseAuth";
import { signOut } from "firebase/auth";
import { t } from "i18next";
import i18n from "../../i18n";

const LogoutModal = ({ show, setShow }: any) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const handleLogOut = async() => {
    setLoading(true);
    await signOut(auth).then(() => {
      localStorage.removeItem("accesstoken");
    }).catch((error) => {
      console.log(error)
    });
    ApiPostAuth("/users/logout", {})
      .then((response) => {
        localStorage.removeItem("token");
        localStorage.removeItem("expiryTime");
        localStorage.removeItem("basket");
        dispatch({
          type: "set",
          loggedIn: false,
          userInfo: {
            firstName: "",
            lastName: "",
            currency: null,
          },
          plans: [],
          basket: null,
          creditSelection: null,
        });
        setShow(false);
        setLoading(false);
        setTimeout(() => {
          navigate("/login");
        }, 100);
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            heading: "Something went wrong",
            message: "We couldn't log you out successfully, please try again",
            error: true,
          },
        });
      });
  };

  return (
    <Modal show={show} style={{ height: 380, maxWidth: 530 }}>
      <div className={styles.main}>
        <LogoutCircles />
        <h5>{ t('account.logout') } {i18n.language === 'ar' ? '؟' : '?'}</h5>
        <p>{ t('account.logoutMsg') }</p>
        <div
          className={styles.buttons}
          onClick={() => {
            setShow(false);
          }}
        >
          <Button
            loading={loading}
            color="primary"
            style={{ marginInlineEnd: 16 }}
            onClick={handleLogOut}
          >
            <DoorOpen /> { t('account.logout') }
          </Button>
          <Button
            onClick={() => {
              setShow(false);
            }}
            color="secondary"
            disabled={loading}
          >
            { t('buttons.cancel') }
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default LogoutModal;
