@use "../styles/theme.scss" as *;

.container {
  display: flex;
  flex-direction: column;
}

.topSection {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  color: #fff;
  position: relative;
  background-image: url("/../public/images-int/esim-devices/esim_devices_hero.png");
  .background {
    background: linear-gradient(
      90deg,
      rgba(22, 11, 42, 0.8) 0%,
      rgba(22, 11, 42, 0.5) 50%,
      rgba(22, 11, 42, 0.8) 100%
    );
    padding: 100px 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    @media (max-width: 768px) {
      padding: 100px 24px;
      text-align: center;
    }
  }
  h2 {
    font-weight: 700;
    font-size: 40px;
    line-height: 60px;
    margin: 0 0 20px 0;
    @media (max-width: 768px) {
      font-size: 30px;
      line-height: 45px;
    }
  }
  p {
    font-size: 20px;
    line-height: 30px;
    margin: 0;
    max-width: 886px;
    @media (max-width: 768px) {
      font-size: 16px;
      line-height: 24px;
    }
  }
}

.phoneListContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 70px 50px;
  max-width: 1180px;
  margin: 0 auto;
  width: 100%;
  grid-column-gap: 218px;
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    padding: 50px 24px;
  }
}

.singleList {
  margin-bottom: 40px;
  h5 {
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    color: $dark-dark-purple;
    margin: 0 0 20px 0;
  }
  ul {
    margin: 0;
    list-style-type: none;
    padding-inline-start: 0px;
    li {
      font-size: 20px;
      line-height: 30px;
      color: $dark-dark-purple;
      margin-bottom: 2px;
      @media (max-width: 768px) {
        font-size: 16px;
        line-height: 24px;
      }
      &:before {
        content: "-";
        margin-right: 5px;
      }
    }
  }
}

.instructions {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  text-align: center;
  margin-bottom: 100px;
  padding: 0 50px;
  @media (max-width: 768px) {
    padding: 0 12px;
  }
  h3 {
    margin: 0 0 12px 0;
    font-size: 40px;
    line-height: 60px;
    font-weight: 700;
    color: $primary;
    @media (max-width: 768px) {
      font-size: 30px;
      line-height: 45px;
    }
  }
  .info {
    margin: 0 0 32px 0;
    max-width: 888px;
    font-size: 16px;
    color: $dark-dark-purple;
    line-height: 24px;
  }
}

.toggle {
  display: flex;
  width: 432px;
  height: 50px;
  background: $light-primary;
  border-radius: 50px;
  position: relative;
  margin-bottom: 60px;
  @media (max-width: 768px) {
    width: 100%;
    max-width: 432px;
  }
  .label {
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
    color: $dark-dark-purple;
    z-index: 200;
    cursor: pointer;
    transition: color 0.3s ease;
    &.active {
      color: #fff;
    }
  }
  .toggleThumb {
    position: absolute;
    width: 50%;
    height: 100%;
    background: $secondary;
    z-index: 100;
    border-radius: 50px;
    box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.15);
    left: 0px;
    transition: all 0.4s ease;
    &.ios {
      left: 50%;
    }
  }
}

.stepsContainer {
  display: grid;
  grid-template-columns: 436px 1fr;
  align-items: flex-start;
  width: 100%;
  justify-content: flex-start;
  text-align: start;
  height: 700px;
  @media (max-width: 1000px) {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: auto;
  }
  .imageContainer {
    width: 100%;
    position: relative;
    top: -12px;
    &.iosImage {
      top: -18px;
    }
    @media (max-width: 1000px) {
      position: static;
    }
  }
  .stepImage {
    width: 100%;
    max-width: 886px;
  }
}

.stepTile {
  background: #fff;
  box-shadow: -6px 6px 20px 0px rgba(62, 29, 107, 0.2);
  padding: 32px;
  border-radius: 24px;
  width: 100%;
  max-width: 436px;
  @media (max-width: 1000px) {
    margin-bottom: 32px;
  }
  @media (max-width: 768px) {
    padding: 24px;
  }
  .stepItem {
    padding: 10px;
    background-color: #eee7f8;
    color: $dark-dark-purple;
    border-radius: 12px;
    margin-bottom: 10px;
    cursor: pointer;
    ol {
      margin: 0;
      padding-inline-start: 20px;
    }
    &:last-of-type {
      margin-bottom: 0px;
    }
    .stepTitle {
      font-size: 16px;
      &.active {
        font-weight: 600;
      }
    }
    .content {
      padding-top: 16px;
      ul {
        margin: 0;
        padding-inline-start: 25px;
      }
    }
  }
}
