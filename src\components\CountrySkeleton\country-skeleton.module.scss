@use "../../styles/theme.scss" as *;

.imageBox {
  padding-bottom: 124.9%;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: box-shadow 0.3s ease-out;
  .stickyImageContainer {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: $skeleton;
    overflow: hidden;
  }
}

.nameContainer {
  display: grid;
  align-items: center;
  grid-template-columns: 24px auto;
  grid-column-gap: 12px;
  margin-top: 16px;
  overflow: visible;
  .flag {
    width: 24px;
    height: 24px;
    border-radius: 1000px;
    background: $skeleton;
    border: 1px solid rgba(0, 0, 0, 0.15);
    overflow: hidden;
    position: relative;
  }
  .name {
    width: 200px;
    background: $skeleton;
    height: 20px;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
  }
}
