import { useMediaQuery } from "@mui/material";
import { useEffect } from "react";
import { useLocation } from "react-router";

const ScrollToTop = (props: any) => {
  const location = useLocation();
  const under850 = useMediaQuery("(max-width: 850px)");
  useEffect(() => {
    if (!location.pathname.includes("dashboard")) {
      window.scrollTo(0, 0);
    }
  }, [location]);

  return <>{props.children}</>;
};

export default ScrollToTop;
