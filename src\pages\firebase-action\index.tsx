import { useEffect } from "react";
import { ApiPostAuth } from "../api/api";
import { useNavigate, useSearchParams } from "react-router-dom";
import { CircularProgress } from "@mui/material";
import PaymentLoading from "../../components/PaymentLoading";

const FirebaseAction = () => {
  const navigate = useNavigate();

  const [searchParams, setSearchParams] = useSearchParams();

  const handleError = () => {
    navigate("/login");
  };

  useEffect(() => {
    const continueUrl = searchParams.get("continueUrl");

    if (continueUrl) {
      window.location.href = continueUrl;
    } else {
      handleError();
    }
  }, []);

  return <PaymentLoading loading />;
};

export default FirebaseAction;
