import styles from "./footer.module.scss";
import { <PERSON> } from "react-router-dom";
import { useEffect, useState } from "react";
import { CmsApiGet, cmsURL } from "../../pages/api/cms-api";
import { useTranslation } from "react-i18next";

const Footer = () => {
  const { t, i18n } = useTranslation();
  const [data, setData] = useState<any>(null);

  useEffect(() => {
    CmsApiGet(`/api/footer?locale=${i18n.language}&populate=deep`).then(
      (response: any) => {
        setData(response.data.data.attributes);
      }
    );
  }, [i18n, i18n.language]);

  return (
    <footer className={styles.main}>
      <div className="max-width-container">
        <div className={styles.row} style={{ marginBottom: 45 }}>
          <Link to="/">
            <img
              src={cmsURL + data?.logo?.data?.attributes?.url}
              alt="Logo of Orbit Mobile"
              className={styles.footerLogo}
            />
          </Link>
          <nav className={styles.linksContainer}>
            {data?.navLinks?.map((item: any) => (
              <Link to={item.link}>{item.label}</Link>
            ))}
          </nav>
          <div>
            <h5 className="heading-5">{data?.getAppText}</h5>
            <div className={styles.stores}>
              {data?.showAndroidButton && (
                <a href={data?.androidLink}>
                  <img
                    src="/images-int/app-stores/googleStore.svg"
                    alt={`${t("general.downloadApp")} - Google play store`}
                  />
                </a>
              )}
              {data?.showIOSButton && (
                <a href={data.iOSLink}>
                  <img
                    src="/images-int/app-stores/appleStore.svg"
                    alt={`${t("general.downloadApp")} - Apple store`}
                  />
                </a>
              )}
            </div>
          </div>
        </div>
        <div className={styles.divider} />
        <div className={styles.row}>
          <div className={styles.socials}>
            <div className={styles.socialLinks}>
              {data?.social?.map((item: any) => (
                <a href={item?.link} target="_blank">
                  <img src={cmsURL + item?.icon?.data?.attributes?.url} />
                </a>
              ))}
            </div>
          </div>
          <div className={styles.legal}>
            {data?.legals?.map((item: any) => (
              <Link to={item.link}>{item.label}</Link>
            ))}
          </div>
          <div className={styles.copyrights}>
            <p>{data?.copyrightText}</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
