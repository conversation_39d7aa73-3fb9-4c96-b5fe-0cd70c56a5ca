@use "../../styles/theme.scss" as *;

.main {
  background-size: cover;
  background-position: center;
  background-color: $primary;
  width: 89px;
  height: 89px;
  border-radius: 100px;
  margin-bottom: 12px;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  @media (max-width: 850px) {
    width: 60px;
    height: 60px;
    margin-bottom: 0px;
  }
  .noUser {
    transition: all 0.3s ease-out;
    svg {
      width: 36px;
      height: 36px;
      vertical-align: middle;
      @media (max-width: 850px) {
        width: 24px;
        height: 24px;
      }
    }
  }
  &:hover {
    .overlay {
      opacity: 1;
    }
    .noUser {
      opacity: 0;
    }
  }
}

.overlay {
  width: 100%;
  height: 100%;
  border-radius: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    0deg,
    rgba(22, 11, 42, 0.4),
    rgba(22, 11, 42, 0.4)
  );

  color: #fff;
  transition: all 0.3s ease-out;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  svg {
    width: 43px;
    height: 43px;
    @media (max-width: 850px) {
      width: 29px;
      height: 29px;
    }
  }
}
