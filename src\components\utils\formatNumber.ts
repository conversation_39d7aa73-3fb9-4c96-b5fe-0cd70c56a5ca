import { parsePhoneNumber } from "libphonenumber-js";

export const formatNumber = (number: any, debug = false) => {
  try {
    if (number) {
      const phone = parsePhoneNumber(
        number[0] === "+" ? number : "+" + number,
      ) as any;
      if (phone) {
        return phone.formatInternational();
      } else {
        return number;
      }
    } else {
      return "";
    }
  } catch (e) {
    return "";
  }
};
