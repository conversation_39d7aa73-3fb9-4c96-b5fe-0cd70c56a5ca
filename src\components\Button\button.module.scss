@use "../../styles/theme.scss" as *;

.button {
  height: 40px;
  padding: 0 24px;
  font-size: 14px;
  border: none;
  border-radius: 53px;
  cursor: pointer;
  transition: all 0.1s ease-out;
  display: grid;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  &:disabled {
    pointer-events: none;
    opacity: 0.5;
  }
  &.primary {
    background: $secondary;
    color: #fff;
    box-shadow: 0px 8px 20px 0px #0f133a1a;
    &:hover {
      background: #05bfbe;
    }
  }
  &.secondary {
    background: $primary;
    color: $secondary;
    &:hover {
      background: #6bbdbd;
    }
  }
  &.tertiary {
    background: none;
    color: $secondary;
    min-width: initial;
    &:hover {
      color: $primary;
    }
  }
  &.danger {
    background: #f8d4d3;
    color: #691211;
    min-width: initial;
    font-weight: 400;
    &:hover {
      color: #691211;
    }
  }
  &.addFunds {
    background: #cebee3;
    color: #7448b0;
    width: 100%;
    height: 43px;
    font-size: 12px;
    font-weight: 700;
    &:hover {
      background: #baaacf;
    }
  }
}

.children svg {
  color: inherit;
  margin-inline-end: 8px;
}
