import { Link } from "react-router-dom";
import styles from "./blog-preview.module.scss";

const BlogPreview = ({ image, title, link }: any) => {
  return (
    <Link style={{ textDecoration: "none" }} to={`/blog/${link}`}>
      <div className={styles.main}>
        <div className={styles.imageContainer}>
          <div className={styles.stickyImageContainer}>
            <img src={image} />
          </div>
        </div>
        <h5 className={styles.title}>{title}</h5>
      </div>
    </Link>
  );
};

export default BlogPreview;
