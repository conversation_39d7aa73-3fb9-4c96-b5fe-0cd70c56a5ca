import { useState } from "react";
import EditProfilePicture from "../EditProfilePicture";
import { Pencil, User } from "../svgs";
import styles from "./profile-picture.module.scss";

const ProfilePicture = ({ url, reload }: any) => {
  const [showEdit, setShowEdit] = useState(false);

  return (
    <>
      <EditProfilePicture
        show={showEdit}
        setShow={setShowEdit}
        picture={url}
        reload={reload}
      />
      <div
        className={styles.main}
        style={{ backgroundImage: `url(${url})` }}
        onClick={() => {
          setShowEdit(true);
        }}
      >
        {(url === null || url === "null") && (
          <div className={styles.noUser}>
            <User />
          </div>
        )}
        <div className={styles.overlay}>
          <Pencil />
        </div>
      </div>
    </>
  );
};

export default ProfilePicture;
