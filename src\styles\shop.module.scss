@use "../styles/theme.scss" as *;

.container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.searchSection {
  background-image: url("/../public/images-int/shop/shop_hero_lg.png");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  color: #fff;
  margin-bottom: 64px;
  @media (max-width: 991px) {
    background-image: url("/../public/images-int/shop/shop_hero.png");
  }
  @media (max-width: 768px) {
    background-image: url("/../public/images-int/shop/shop_hero_sm.png");
  }
  .background {
    background: linear-gradient(
      90deg,
      rgba(22, 11, 42, 0.6) 0%,
      rgba(22, 11, 42, 0.3) 50%,
      rgba(22, 11, 42, 0.6) 100%
    );
    padding: 76px 0 78px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    @media (max-width: 768px) {
      padding: 75px 24px;
    }
  }
  h2 {
    font-weight: 700;
    font-size: 40px;
    line-height: 60px;
    margin: 0 0 8px 0;
    @media (max-width: 768px) {
      font-size: 28px;
      line-height: 42px;
    }
  }
  p {
    font-size: 20px;
    line-height: 30px;
    text-align: center;
    width: 100%;
    max-width: 647px;
    margin: 0 0 24px 0;
    @media (max-width: 768px) {
      font-size: 16px;
      line-height: 24px;
    }
  }
}

.countries {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  padding: 50px;
  grid-row-gap: 60px;
  grid-column-gap: 25px;
  @media (max-width: 2500px) {
    grid-template-columns: repeat(6, 1fr);
  }
  @media (max-width: 2150px) {
    grid-template-columns: repeat(5, 1fr);
  }
  @media (max-width: 1100px) {
    grid-template-columns: repeat(4, 1fr);
  }
  @media (max-width: 900px) {
    grid-template-columns: repeat(3, 1fr);
  }
  @media (max-width: 768px) {
    padding: 40px 24px;
    grid-template-columns: repeat(2, 1fr);
    grid-row-gap: 24px;
    grid-column-gap: 24px;
  }
}

.noResults {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: relative;
  padding: 57px 0 133px 0;
  flex-grow: 1;
  max-width: 1440px;
  margin: 0 auto;
  @media (max-width: 1230px) {
    padding: 57px 24px 420px 24px;
  }
  .bush {
    position: absolute;
    width: 150px;
    left: 100px;
    bottom: 8px;
    @media (max-width: 580px) {
      display: none;
    }
  }
  .tree {
    position: absolute;
    width: 290px;
    right: 0;
    bottom: 8px;
  }
  h3 {
    font-weight: 700;
    font-size: 40px;
    line-height: 60px;
    color: $primary;
    margin: 0 0 8px 0;
  }
  p {
    font-size: 16px;
    line-height: 24px;
    color: $dark-dark-purple;
    text-align: center;
    margin: 0 0 30px 0;
  }
}

.zones {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 50px;
  padding-top: 100px;
  padding-bottom: 188px;
  @media (max-width: 768px) {
    padding: 50px 24px;
  }
  h4 {
    font-weight: 700;
    font-size: 40px;
    line-height: 60px;
    color: $dark-dark-purple;
    margin: 0 0 16px 0;
    text-align: center;
    @media (max-width: 768px) {
      font-size: 30px;
      line-height: 45px;
    }
  }
  p {
    font-size: 20px;
    line-height: 30px;
    color: $dark-dark-purple;
    margin: 0 0 50px 0;
    text-align: center;
    @media (max-width: 768px) {
      font-size: 16px;
      line-height: 24px;
    }
  }
  .zoneTileContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    @media (max-width: 768px) {
      flex-direction: column;
    }
  }
  .zoneTileLink {
    width: 322px;
    margin: 0 10px 70px 10px;
    @media (max-width: 768px) {
      width: 100%;
      margin: 0;
      margin-bottom: 20px;
    }
  }
  .zoneTile {
    width: 322px;
    position: relative;
    cursor: pointer;
    @media (max-width: 768px) {
      width: 100%;
      margin: 0;
    }
    &:hover {
      .imageContainer {
        box-shadow: 0px 2.61451px 26.1451px rgba(116, 72, 176, 0.25);
      }
    }
    .name {
      width: 100%;
      text-align: center;
      font-size: 16px;
      line-height: 24px;
      margin-top: 12px;
      color: $dark-dark-purple;
      @media (max-width: 768px) {
        margin-top: 10px;
      }
    }
    .imageContainer {
      padding-bottom: 69.64%;
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: box-shadow 0.3s ease-out;
      .stickyImageContainer {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          height: 100%;
          width: auto;
        }
      }
    }
  }
}
