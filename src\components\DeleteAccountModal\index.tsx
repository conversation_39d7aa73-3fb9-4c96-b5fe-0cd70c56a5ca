import { useState } from "react";
import Button from "../Button";
import Modal from "../Modal";
import styles from "./delete-account-modal.module.scss";
import { ApiGet } from "../../pages/api/api";
import { useSelector } from "react-redux";
import { CrossWithCircle, ExclamationMark } from "../svgs";
import { t } from "i18next";

const DeleteAccountModal = ({ show, setShow }: any) => {
  const { userInfo } = useSelector((state: any) => state);
  const [loading, setLoading] = useState(false);
  const [modelStep, setModalStep] = useState(1);
  const [deleteState, setDeleteState] = useState<"success" | "failure">(
    "success"
  );
  const [error, setError] = useState("");
  const [timeToReset, setTimeToReset] = useState(30);
  const [timer, setTimer] = useState(null as any);

  const sendEmail = async () => {
    setError("");
      let handleFinally = true;
      setLoading(true);
      ApiGet("/users/delete/request").then((response) => {
        setLoading(false);
        if (handleFinally) {
          setDeleteState("success");
          setModalStep(2);
          setTimeToReset(30);
          setTimeToResetCounter();
        }
      }) .catch((error: any) => {
        setError(error.code);
        if (error) handleFinally = false;
        setDeleteState("failure");
      })
  };

  const setTimeToResetCounter = () => {
    const clearTimer = () => {
      clearInterval(timer);
    };
    clearTimer();
    setTimer(
      setInterval(() => {
        setTimeToReset((prev) => {
          if (prev > 0) {
            return prev - 1;
          } else {
            clearTimer();
            return 0;
          }
        });
      }, 1000)
    );
  };

  return (
    <Modal show={show} style={{ maxWidth: 500 }}>
      <div className={styles.main}>
        {modelStep === 3 && deleteState === "success" && (
          <>
            <img src="/images-int/help/help_activating.svg" />
            <h4>{ t('account.msgs.accountDeleted') }</h4>
            <p>
              { t('account.msgs.sorryForBye') }
              <br />
              { t('account.msgs.seeYouSoon') }
            </p>
            <Button loading={loading}>
              { t('buttons.byebye') }
            </Button>
          </>
        )}
        {modelStep === 2 && (
          <>
            <img src="/images-int/help/help_activating.svg" />
            <h4>{ t('account.msgs.sentMail') }</h4>
            <p>{t('account.msgs.confirmationMail', {email: userInfo.email})}</p>
            <div className={styles.resend}>
              <h5>{ t('account.msgs.notRecieved', {email: userInfo.email}) }</h5>
              {timeToReset !== 0 ? (
                <p>
                 { t('account.requestNewMailCounter')} 00:
                  {timeToReset.toString().padStart(2, "0")}
                </p>
              ) : (
                <Button
                  onClick={sendEmail}
                  style={{ height: 48, marginTop: 16, padding: "0px 50px" }}
                >
                  { t('account.requestNewMail') } 
                </Button>
              )}
            </div>
          </>
        )}
        {modelStep === 3 && deleteState === "failure" && (
          <>
            <CrossWithCircle />
            <h4>{ t('buttons.oops') }</h4>
            <p>{ t('account.errors.somethingWrong') }</p>
            <Button loading={loading} onClick={() => setModalStep(1)}>
              { t('buttons.tryAgain') }
            </Button>
          </>
        )}
        {modelStep === 1 && (
          <>
            <ExclamationMark />
            <h4>{ t('account.msgs.confirmDeletion') }</h4>
            <p>{ t('account.msgs.sendingMailStep') }</p>
            {error && <p className={styles.error}>Error: {error}</p>}
            <div className={styles.buttons}>
              <Button
                loading={loading}
                onClick={sendEmail}
                style={{ marginInlineEnd: 16 }}>
                {  t('account.msgs.confirmationLink') }
              </Button>
              <Button
                onClick={() => {
                  setShow(false, 'cancel');
                }}
                color="secondary"
                disabled={loading}>
                { t('buttons.cancel') }
              </Button>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default DeleteAccountModal;