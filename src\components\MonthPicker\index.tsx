import { Fade } from "@mui/material";
import { useState } from "react";
import { ArrowDown, CaretDown, Funnel } from "../svgs";
import styles from "./month-picker.module.scss";

export const months = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

const years = (() => {
  const now = new Date();
  const nowYear = now.getFullYear();
  let result = [];
  for (let i = 11; i >= 0; i--) {
    result.push((nowYear - i).toString());
  }
  return result;
})();

const MonthPicker = ({
  activeMonth,
  setActiveMonth,
  activeYear,
  setActiveYear,
}: any) => {
  const [show, setShow] = useState(false);

  const [selectingYear, setSelectingYear] = useState(false);

  return (
    <div className={styles.container}>
      <div
        className={styles.button}
        onClick={() => {
          setShow(true);
        }}
      >
        <Funnel />
        {activeMonth} {activeYear}
      </div>
      {show && (
        <div
          className={styles.clickOff}
          onClick={() => {
            setShow(false);
          }}
        />
      )}
      <Fade in={show}>
        {selectingYear ? (
          <div className={`${styles.yearMenu} ${styles.menu}`}>
            <div className={styles.topBar}>
              <div className={styles.selectYear}>Year</div>
            </div>
            <div className={styles.months}>
              {years.map((year) => (
                <div
                  className={`${styles.month} ${
                    activeYear === year && styles.active
                  }`}
                  onClick={() => {
                    setActiveYear(year);
                    setSelectingYear(false);
                  }}
                  key={year}
                >
                  {year}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className={styles.menu}>
            <div className={styles.topBar}>
              <div
                className={styles.selectYear}
                style={{ cursor: "pointer" }}
                onClick={() => {
                  setSelectingYear(true);
                }}
              >
                {activeYear} <ArrowDown />
              </div>
            </div>
            <div className={styles.months}>
              {months.map((month) => (
                <div
                  className={`${styles.month} ${
                    activeMonth === month && styles.active
                  }`}
                  onClick={() => {
                    setActiveMonth(month);
                    setShow(false);
                  }}
                  key={month}
                >
                  {month}
                </div>
              ))}
            </div>
          </div>
        )}
      </Fade>
    </div>
  );
};

export default MonthPicker;
