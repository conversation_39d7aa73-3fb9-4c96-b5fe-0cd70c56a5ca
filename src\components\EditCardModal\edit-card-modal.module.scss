@use "../../styles/theme.scss" as *;

.main {
  width: 872px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 54px 34px 44px 86px;
  @media (max-width: 995px) {
    width: 100%;
  }
  @media (max-width: 768px) {
    padding: 65px 24px 40px 24px;
  }
  h3,
  .editHeading {
    color: $primary;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
  }
  h3 {
    margin: 0 0 50px 0;
  }
  .editHeading {
    margin: 0;
  }
}

.contentGrid {
  display: grid;
  width: 100%;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 98px;
  justify-content: space-between;
  margin-top: 77px;
  @media (max-width: 680px) {
    display: flex;
    grid-column-gap: initial;
    margin-top: 30px;
  }
  @media (max-width: 525px) {
    flex-direction: column;
  }
  .expiryContainer {
    width: 100%;
    max-width: 157px;
  }
  .nameContainer {
    width: 100%;
  }
  .inputLabel {
    color: #160b2a;
    font-size: 14px;
    line-height: 24px;
    margin-bottom: 4px;
  }
}

.billing {
  @media (max-width: 525px) {
    margin-top: 40px;
  }
  h4 {
    margin: 0 0 12px 0;
  }
}

.closeButton {
  svg {
    vertical-align: middle;
  }
  padding: 0;
  background: none;
  border: none;
  cursor: pointer;
  position: absolute;
  top: 50px;
  right: 50px;
  @media (max-width: 768px) {
    top: 24px;
    right: 24px;
  }
}

.buttons {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  margin-top: 70px;
}
