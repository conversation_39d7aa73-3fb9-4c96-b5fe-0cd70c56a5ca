import styles from "./country-rate.module.scss";
import { Mobile, SMS, Phone } from "../svgs";
import { useSelector } from "react-redux";
import { getCurrencySymbol } from "../utils/getCurrencySymbol";
import { formatPrice } from "../utils/formatPrice";

const CountryRate = ({ rate }: any) => {
  return (
    <div className={styles.main}>
      <div className={styles.nameContainer}>
        <div
          className={styles.flag}
          style={{ backgroundImage: `url(${rate.country.iconUrl})` }}
        />
        <div className={styles.text}>{rate.country.countryName}</div>
      </div>
      <div className={styles.ratesContainer}>
        <div className={styles.singleRate} style={{ marginRight: 24 }}>
          <Mobile />
          <div>
            {rate.rates[0].currency.currencySymbol}
            {formatPrice(parseFloat(rate.rates[0].mobileRate))}/min
          </div>
        </div>
        <div className={styles.singleRate} style={{ marginRight: 24 }}>
          <Phone />
          <div>
            {rate.rates[0].currency.currencySymbol}
            {formatPrice(parseFloat(rate.rates[0].localRate))}/min
          </div>
        </div>
        <div className={styles.singleRate}>
          <SMS />
          <div>
            {rate.rates[0].currency.currencySymbol}
            {formatPrice(parseFloat(rate.rates[0].smsRate))}/SMS
          </div>
        </div>
      </div>
    </div>
  );
};

export default CountryRate;
