import { useState, useEffect } from "react";
import { CmsApiGet } from "../../pages/api/cms-api";
import styles from "./banner.module.scss";
import { Close } from "../svgs";
import { useDispatch, useSelector } from "react-redux";

type bannerType = {
  attributes: {
    Type: "Info" | "Alert";
    details: String;
    active: boolean;
  };
};

export const Banner = () => {
  const dispatch = useDispatch();
  const [banners, setBanners] = useState<bannerType[]>([]);

  const { isBannerOpen } = useSelector((state: any) => state);

  useEffect(() => {
    CmsApiGet("/api/smart-banners").then((response: any) => {
      let activeBanners = response.data.data.filter(
        (banner: any) => banner.attributes.active
      );
      if (activeBanners.length > 0) {
        dispatch({ type: "set", isBannerOpen: true });
      }
      setBanners(activeBanners);
    });
  }, []);

  if (banners.length < 1) {
    return null;
  }

  return (
    <div
      className={styles.banner + " items-center"}
      style={{
        backgroundColor:
          banners[0].attributes.Type === "Info" ? "#088BDD" : "#FFDD99",
        display: !isBannerOpen ? "none" : "flex",
      }}
    >
      <div>
        {banners[0].attributes.Type === "Info" ? (
          <img src="/images-int/info-icon.svg" />
        ) : (
          <img src="/images-int/warning.svg" />
        )}
      </div>
      <div
        style={{
          color: banners[0].attributes.Type === "Info" ? "#fff" : "#000",
        }}
      >
        {banners[0].attributes.details}
      </div>
      <div className={styles.close}>
        <span
          style={{
            color: banners[0].attributes.Type === "Info" ? "#fff" : "#000",
          }}
          onClick={() => {
            dispatch({ type: "set", isBannerOpen: false });
          }}
        >
          <Close />
        </span>
      </div>
    </div>
  );
};
