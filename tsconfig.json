{"compilerOptions": {"target": "ESNext", "lib": ["dom", "dom.iterable", "esnext"], "types": ["vite/client"], "typeRoots": ["src/@typesypes", "node_modules/@types"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src"]}