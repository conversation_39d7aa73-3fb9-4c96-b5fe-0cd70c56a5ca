import axios, { CancelTokenSource } from "axios";
import i18n from "../../i18n";

const queryCors = import.meta.env.VITE_APP_HOST_NAME;

const headers = {
  Accept: "application/json",
  "Accept-Language": i18n.language,
  apikey: import.meta.env.VITE_APP_API_KEY,
  companycode: import.meta.env.VITE_APP_COM_CODE,
  operatingsystem: import.meta.env.VITE_APP_OS_HEADER,
  "App-Version": import.meta.env.VITE_VERSION,
};

export const ApiPostAuth = (
  url: string,
  parameters?: { [propName: string]: any },
  cancelToken?: CancelTokenSource
) =>
  axios.post(queryCors + url, parameters, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    cancelToken: cancelToken?.token,
  });

export const ApiPost = (
  url: string,
  parameters: { [propName: string]: any },
  cancelToken?: CancelTokenSource
) =>
  axios.post(queryCors + url, parameters, {
    headers: headers,
    cancelToken: cancelToken?.token,
  });

export const ApiPatch = (
  url: string,
  parameters?: { [propName: string]: any },
  cancelToken?: CancelTokenSource
) =>
  axios.patch(queryCors + url, parameters, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    cancelToken: cancelToken?.token,
  });

export const ApiGet = (
  url: string,
  pagenumber: any = null,
  countryname: any = null,
  lng?: string
) => {
  let headerObj: any = localStorage.getItem("token")
    ? {
        ...headers,
        Authorization: `Bearer ${localStorage.getItem("token")}`,
        "Accept-Language": lng ? lng : 'en'
        
      }
    : { ...headers,
        "Accept-Language": lng ? lng : 'en', 
        pagenumber: pagenumber };

  if (pagenumber) {
    headerObj.pagenumber = pagenumber;
  }

  if (countryname) {
    headerObj.countryname = countryname;
  }
  return axios.get(queryCors + url, {
    headers: headerObj,
  });
};

export const ApiGetNoAuth = (url: string, cancelToken?: CancelTokenSource) =>
  axios.get(queryCors + url, {
    headers: {
      ...headers,
      "Accept-Language": i18n.language,
    },
    cancelToken: cancelToken?.token,
  });

export const ApiDelete = (
  url: string,
  parameters: { [propName: string]: any }
) =>
  axios.request({
    url: queryCors + url,
    method: "delete",
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    data: parameters,
  });
