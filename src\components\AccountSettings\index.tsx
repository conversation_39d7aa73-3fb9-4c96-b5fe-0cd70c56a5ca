import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ApiGet, ApiPostAuth, ApiPatch } from "../../pages/api/api";
import Button from "../Button";
import { Helmet } from 'react-helmet-async';
import { useNavigate } from "react-router-dom";
import { handleInputChange, createStateObject } from "../utils/InputHandlers";
import DeleteAccountModal from "../DeleteAccountModal";
import { EmailSign, Password, Pencil, Phone, Plus, Trash, User } from "../svgs";
import styles from "./account-settings.module.scss";
import Shimmer from "../Shimmer";
import { ToggleGlobal } from "../ToggleGlobal/ToggleGlobal";
import Modal from "../Modal";
import { Input } from "../Input";
import PhoneInput from "../PhoneInput";
import ChangePasswordModal from "../ChangePasswordModal";
import { useSearchParams } from "react-router-dom";
import { t } from "i18next";

const AccountSettings = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate()
  const fields = ["firstName", "lastName", "countryCode", "phoneNumber"];
  const { userInfo, loggedIn } = useSelector((state: any) => state);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState(null as any);
  const [searchParams, setSearchParams] = useSearchParams();
  const [showModal, setShowModal] = useState(false);
  const [fieldsValue, setFieldsValue] = useState(createStateObject(fields));
  const [modalType, setModalType] = useState<
    "firstName" | "lastName" | "phoneNumber" | "password"
  >("password");

  // Load settings
  const getSettings = () => {
    ApiGet("/users/notificationsettings")
      .then((response: any) => {
        setSettings(response.data);
        setLoading(false);
      })
      .catch((error: any) => {});
  };

  useEffect(() => {
    const deleteParam = searchParams.get("delete");
    if (deleteParam) {
      setShowDeleteModal(true);
    }
  }, []);

  useEffect(getSettings, []);

  const handleModal = (
    input: "firstName" | "lastName" | "password" | "phoneNumber"
  ) => {
    setShowModal(true);
    setModalType(input);
  };

  const updateUserData = () => {
    if (localStorage.getItem("token")) {
      ApiGet("/users")
        .then((response: any) => {
          dispatch({
            type: "set",
            loggedIn: true,
            userInfo: {
              firstName: response.data.firstName,
              lastName: response.data.lastName,
              phone: response.data.registeredPhoneNumber,
            },
          });
        })
        .catch((error) => {
          localStorage.removeItem("token");
        });
    }
  };

  const handleSaveData = () => {
    setShowModal(false);
    ApiPatch("/users", {
      ...(modalType === "firstName" && { firstName: fieldsValue.firstName }),
      ...(modalType === "lastName" && { lastName: fieldsValue.lastName }),
      ...(modalType === "phoneNumber" && fieldsValue.phoneNumber
        ? { phoneNumber: fieldsValue.countryCode + fieldsValue.phoneNumber }
        : ""),
    })
      .then((response: any) => {
        updateUserData();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: t('account.msgs.success'),
            message: response.data.message,
          },
        });
      })
      .catch((error: any) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: "Something went wrong",
            message: error.response.data.message || "",
          },
        });
      });
  };

  // Handle update notif settings
  const handleChange = (prop: string) => {
    setLoading(true);
    ApiPostAuth("/users/notificationsettings", {
      ...settings,
      [prop]: !settings[prop],
    })
      .then((response: any) => {
        getSettings();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: t('account.msgs.success'),
            message: response.data.message,
          },
        });
      })
      .catch((error: any) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: "Something went wrong",
            message: error.response.data.message || "",
          },
        });
      });
  };

  const SkeletonItem = () => (
    <div className={styles.item}>
      <div className={styles.skeleton}>
        <Shimmer />
      </div>
      <ToggleGlobal on={false} disabled />
    </div>
  );

  const handleDeleteModal = (val:boolean, option:string) => {
    setShowDeleteModal(val)
    if (option === 'delete') {
      navigate("/login")
    }
  }

  return (
    <div className={styles.main}>
      <Helmet>
        <title>{t("general.orbit")} | { t('general.dashboard') } |  { t('account.account') }</title>
      </Helmet>
      <div className={styles.top}>
        <h3>{ t('account.account') }</h3>
      </div>
      <div className={styles.container}>
        <h4>{ t('account.details') }</h4>
        <div className={styles.notificationsGrid}>
          <div className={styles.item}>
            <div className="flex" style={{ gap: '4px' }}>
              <div className={styles.settingIcon}>
                <User />
              </div>
              <div className={styles.itemDetails}>
                <h5>{ t('placeholders.firstName') }</h5>
                <p>{userInfo.firstName}</p>
                <h5>{ t('placeholders.lastName') }</h5>
                <p>{userInfo.lastName}</p>
              </div>
            </div>
            <div className={styles.editAction} style={{ minHeight: "15vh" }}>
              <div onClick={() => handleModal("firstName")}>
                <Pencil />
              </div>
              <div onClick={() => handleModal("lastName")}>
                <Pencil />
              </div>
            </div>
          </div>
          <div className={styles.item}>
            <div className="flex items-center">
              <div className={styles.settingIcon}>
                <EmailSign />
              </div>
              <div className={styles.itemDetails}>
                <h5>{ t('account.email') }</h5>
                <p>{userInfo.email}</p>
              </div>
            </div>
          </div>
          <div className={styles.item}>
            <div className="flex items-center">
              <div className={styles.settingIcon}>
                <Password />
              </div>
              <div className={styles.itemDetails}>
                <h5>{ t('account.password') }</h5>
                <p>*****************</p>
              </div>
            </div>
            <div
              className={styles.editAction}
              onClick={() => handleModal("password")}
            >
              <Pencil />
            </div>
          </div>
          <div className={styles.item}>
            <div className="flex items-center">
              <div className={styles.settingIcon}>
                <Phone />
              </div>
              <div className={styles.itemDetails}>
                <h5>{ t('account.phoneNumber') }</h5>
                <p>+{userInfo.phone}</p>
              </div>
            </div>
            <div
              className={styles.editAction}
              onClick={() => handleModal("phoneNumber")}
            >
              {userInfo.phone === "" ? <Plus /> : <Pencil />}
            </div>
          </div>
        </div>
        <div>
          <Button
            color="danger"
            onClick={() => {
              setShowDeleteModal(true);
            }}
          >
            <Trash />
            { t('account.deleteAccount') }
          </Button>
        </div>
      </div>
      <div className={styles.container}>
        <h4>{ t('account.notificationSetts') }</h4>
        {settings ? (
          <div className={styles.notificationsGrid}>
            <div>
              <h5>{ t('account.sms') }</h5>
              <div className={styles.item}>
                <div className={styles.name}>{ t('account.general') }</div>
                <div className="flex items-center">
                  <span style={{ marginInlineEnd: "6px" }}>
                    {settings.smsService ? t('buttons.on') : t('buttons.off')}
                  </span>
                  <ToggleGlobal
                    id="sms-service"
                    on={settings.smsService}
                    onChange={() => {
                      handleChange("smsService");
                    }}
                    disabled={loading}
                  />
                </div>
              </div>
              <div className={styles.item}>
                <div className={styles.name}>{ t('account.promotional') }</div>
                <div className="flex items-center">
                  <span style={{ marginInlineEnd: "6px" }}>
                    {settings.smsOffers ? t('buttons.on') : t('buttons.off')}
                  </span>
                  <ToggleGlobal
                    id="sms-offers"
                    on={settings.smsOffers}
                    onChange={() => {
                      handleChange("smsOffers");
                    }}
                    disabled={loading}
                  />
                </div>
              </div>
            </div>
            <div>
              <h5>{ t('account.email') }</h5>
              <div className={styles.item}>
                <div className={styles.name}>{ t('account.general') }</div>
                <div className={`flex items-center ${styles.control}`}>
                  <span style={{ marginInlineEnd: "6px" }}>
                    {settings.emailService ? t('buttons.on') : t('buttons.off')}
                  </span>
                  <ToggleGlobal
                    id="email-service"
                    on={settings.emailService}
                    onChange={() => {
                      handleChange("emailService");
                    }}
                    disabled={loading}
                  />
                </div>
              </div>
              <div className={styles.item}>
                <div className={styles.name}>{ t('account.promotional') }</div>
                <div className="flex items-center">
                  <span style={{ marginInlineEnd: "6px" }}>
                    {settings.emailOffers ? t('buttons.on') : t('buttons.off')}
                  </span>
                  <ToggleGlobal
                    id="email-offers"
                    on={settings.emailOffers}
                    onChange={() => {
                      handleChange("emailOffers");
                    }}
                    disabled={loading}
                  />
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className={styles.notificationsGrid}>
            <div className={styles.column}>
              <h5>{ t('account.sms') }</h5>
              <SkeletonItem />
              <SkeletonItem />
              <SkeletonItem />
            </div>
            <div className={styles.column}>
              <h5>{ t('account.email') }</h5>
              <SkeletonItem />
              <SkeletonItem />
              <SkeletonItem />
            </div>
          </div>
        )}
      </div>
      <DeleteAccountModal show={showDeleteModal} setShow={(val: boolean, option: string) => handleDeleteModal(val, option)} />
      {modalType === "password" ? (
        <ChangePasswordModal
          show={showModal}
          setShow={(val: boolean) => setShowModal(val)}
        />
      ) : (
        <Modal
          show={showModal}
          style={{ height: 250, maxWidth: 400, padding: 20 }}
        >
          {userInfo.phone === "" ? (
            <h2>{ t('account.addPhone') }</h2>
          ) : (
            <h2>{ t('account.editDetails') }</h2>
          )}
          {modalType !== "phoneNumber" ? (
            <Input
              label={t('placeholders.' + modalType)}
              value={fieldsValue[modalType]}
              onChange={(e: any) =>
                handleInputChange(modalType, e, fieldsValue, setFieldsValue)
              }
            />
          ) : (
            <PhoneInput
              state={fieldsValue}
              setState={setFieldsValue}
              disabled={loading}
            />
          )}
          <div className="flex justify-content-between">
            <Button color="primary" onClick={() => handleSaveData()}>
              {userInfo.phone === "" ? t('buttons.save') : t('buttons.saveChanges')}
            </Button>
            <Button color="secondary" onClick={() => setShowModal(false)}>
              { t('buttons.cancel') }
            </Button>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default AccountSettings;
