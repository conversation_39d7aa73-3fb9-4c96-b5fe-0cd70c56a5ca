export const checkExpiry = (expiryDate: any) => {
  let dateFormat = expiryDate.length === 5 ? expiryDate : '0' + expiryDate
  const expMonth = parseInt(dateFormat.slice(0, 2));
  const expYear = parseInt(dateFormat.slice(3));

  if (expMonth === 0 || expMonth > 12) {
    return {
      error: true,
      message: "Please enter a valid date",
    };
  }

  const now = new Date();
  const nowMonth = now.getMonth() + 1;
  const nowYear = parseInt(now.getFullYear().toString().slice(2));
  console.log(expYear, nowYear)

 if ((expYear === nowYear && ((expMonth - nowMonth) < 2)) || ((expYear - nowYear) === 1 && ((expMonth - nowMonth) === -11))) { 
  return {
      error: true,
      status: 'soon',
      message: "About to expire",
    };
  } else if (expYear < nowYear) {
    return {
      error: true,
      status: 'expired',
      message: "Expiry date is in the past",
    };
  } else if (nowYear === expYear && nowMonth > expMonth) {
    return {
      error: true,
      status: 'expired',
      message: "Expiry date is in the past",
    };
  } else {
    return {
      error: false,
      message: "",
    };
  }
};
