import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ApiGet } from "../../../pages/api/api";
import i18n from "../../../i18n";

const DataLoader = () => {
  const dispatch = useDispatch();
  const { countries, zones, customZones, plans, userInfo, basket } =
    useSelector((state: any) => state);

  // Check if stored token is still valid
  useEffect(() => {
    if (localStorage.getItem("token")) {
      ApiGet("/users")
        .then((response: any) => {
          dispatch({
            type: "set",
            loggedIn: true,
            userInfo: {
              firstName: response.data.firstName,
              lastName: response.data.lastName,
              currency: response.data.creditCurrency,
              email: response.data.email,
              comboCreditEligible: response.data.comboCreditEligible,
              credit: response.data.credit,
              topUpLimit: response.data.topUpLimit,
              phone: response.data.registeredPhoneNumber,
            },
          });
        })
        .catch((error) => {
          localStorage.removeItem("token");
        });
    }
  }, []);

  // Update basket currencies
  useEffect(() => {
    const planTypeKeys = {
      COMBO: "comboPlans",
      DATA: "esimPlans",
      PHONE: "didPlans",
      CREDIT: "creditPlans",
    } as any;
    if (basket && userInfo.currency && "didPlans" in plans) {
      if (basket.prices[0].currencyCode !== userInfo.currency) {
        let basketCopy = { ...basket };

        if (plans.creditPlans.length === 0) {
          if (basketCopy.planType === "CREDIT") {
            dispatch({
              type: "notify",
              payload: {
                error: true,
                heading: "Top up limit exceeded for today",
                message: "Come back tomorrow to top-up your account",
              },
            });
          }

          return;
        }

        let newBasket = null as any;
        let newItem = { ...basketCopy };
        const newPrices = plans[planTypeKeys[newItem.planType]].find(
          (i: any) => i.planId === newItem.planId
        ).prices;
        newItem.prices = newPrices;
        newBasket = newItem;
        dispatch({
          type: "set",
          basket: newBasket,
        });
        localStorage.setItem("basket", JSON.stringify(newBasket));
      }
    }
  }, [userInfo, plans]);

  // Get countries
  useEffect(() => {
    ApiGet("/plans/countries", null, null, i18n.language)
      .then((response) => {
        dispatch({
          type: "set",
          countries: response.data.countries,
        });
      })
      .catch((error) => {});
  }, [i18n, i18n.language]);

  useEffect(() => {
    if (localStorage.getItem("i18nextLng")) {
      let locale = localStorage.getItem("i18nextLng");
      if (locale) {
        i18n.changeLanguage(locale);
      }
    } else {
      localStorage.setItem("i18nextLng", "en");
    }
  }, []);

  // Get zones
  useEffect(() => {
    ApiGet("/plans/zones", null, null, i18n.language)
      .then((response) => {
        dispatch({
          type: "set",
          zones: response.data.zones,
        });
      })
      .catch((error) => {});
  }, []);

  // Get custom zones
  useEffect(() => {
    ApiGet("/plans/zones/custom", null, null, i18n.language)
      .then((response) => {
        dispatch({
          type: "set",
          customZones: response.data.zones,
        });
      })
      .catch((error) => {});
  }, [i18n, i18n.language]);

  // Get plans
  useEffect(() => {
    if (plans?.esimPlans?.length === 0) {
      ApiGet("/plans?planType=all")
        .then((response) => {
          dispatch({
            type: "set",
            plans: response.data,
          });
        })
        .catch((error) => {
          console.log(error);
        });
    }
  }, [plans]);

  return <div></div>;
};

export default DataLoader;
