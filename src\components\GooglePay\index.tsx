import { useSelector } from "react-redux";
import styles from "./google-pay.module.scss";
import { useEffect } from "react";
import { ApiPostAuth } from "../../pages/api/api";

const GooglePay = ({ completePayment }: any) => {
  const { basket, currency, creditSelection } = useSelector(
    (state: any) => state
  );

  const getTotal = () => {
    let total = 0;
    if (creditSelection) {
      total += creditSelection.prices[0].cost;
    }
    return total;
  };

  const baseRequest = {
    apiVersion: 2,
    apiVersionMinor: 0,
  };

  const tokenizationSpecification = {
    type: "PAYMENT_GATEWAY",
    parameters: {
      gateway: "stripe",
      "stripe:version": "2018-10-31",
      "stripe:publishableKey":
        "pk_test_51PxRR2P6VkUUZQfRVFlEVmzKk1Bk1PPu3VbNinOxPJllx6gRGeQa7MxNmwBOXouhZ8fyiVYytOwWg4l0uklVAL2V00waksNAlW",
    },
  };

  const allowedCardNetworks = ["AMEX", "JCB", "MASTERCARD", "VISA"];
  const allowedCardAuthMethods = ["PAN_ONLY", "CRYPTOGRAM_3DS"];

  const baseCardPaymentMethod = {
    type: "CARD",
    parameters: {
      allowedAuthMethods: allowedCardAuthMethods,
      allowedCardNetworks: allowedCardNetworks,
    },
    tokenizationSpecification: tokenizationSpecification,
  };

  const checkForGooglePay = () => {
    const isReadyToPayRequest: any = Object.assign({}, baseRequest);
    isReadyToPayRequest.allowedPaymentMethods = [baseCardPaymentMethod];

    (window as any).paymentsClient
      .isReadyToPay(isReadyToPayRequest)
      .then(function (response: any) {
        if (response.result) {
          // add a Google Pay payment button
          const button = (window as any).paymentsClient.createButton({
            buttonColor: "white",
            onClick: payWithGoogle,
            allowedPaymentMethods: [baseCardPaymentMethod],
            buttonRadius: 8,
            buttonSizeMode: "fill",
          }); // same payment methods as for the loadPaymentData() API call
          document.getElementById("container")!.appendChild(button);
        }
      })
      .catch(function (err: any) {
        // show error in developer console for debugging
        console.error(err);
      });
  };

  const payWithGoogle = () => {
    const paymentDataRequest: any = Object.assign({}, baseRequest);
    paymentDataRequest.allowedPaymentMethods = [baseCardPaymentMethod];
    paymentDataRequest.transactionInfo = {
      totalPriceStatus: "FINAL",
      totalPrice: getTotal().toString(),
      currencyCode: currency,
      countryCode: "GB",
    };
    paymentDataRequest.merchantInfo = {
      merchantName: "Gist Mobile",
      merchantId: "12345678901234567890",
    };

    (window as any).paymentsClient
      .loadPaymentData(paymentDataRequest)
      .then(function (paymentData: any) {
        // if using gateway tokenization, pass this token without modification
        const paymentToken = JSON.parse(
          paymentData.paymentMethodData.tokenizationData.token
        );
        completePayment(paymentToken.id);
      })
      .catch(function (err: any) {
        // show error in developer console for debugging
        console.error(err);
      });
  };

  useEffect(checkForGooglePay, []);

  return <div />;
};

export default GooglePay;
