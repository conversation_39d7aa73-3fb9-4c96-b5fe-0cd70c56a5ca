@use "../../styles/theme.scss" as *;

.planTile {
  padding: 40px 16px 16px 16px;
  border-radius: 24px;
  text-align: center;
  display: inline-block;
  margin: 0px 18px 39px 0px;
  min-width: 215px;
  @media (max-width: 768px) {
    width: 100%;
    margin: 0 auto 5px auto;
    max-width: 350px;
  }

  &.color-0 {
    background: #cdf7ee;
  }

  &.color-1 {
    background: #9bf0de;
  }

  &.color-2 {
    background: #9be5e4;
  }

  &.color-3 {
    background: #9ddaeb;
  }

  &.color-4 {
    background: #9dd0f1;
  }
  .top {
    margin-bottom: 12px;
    padding-bottom: 12px;
  }
  .price {
    position: relative;
    overflow: hidden;
    height: 54px;
    background-color: $skeleton;
    border-radius: 6px;
    width: 100px;
  }
  .bottom {
    display: flex;
    flex-direction: column;
    font-size: 16px;
    line-height: 24px;
    transition: all 0.3s ease;
    div {
      display: flex;
      align-items: center;
      &:last-child {
        margin-bottom: 0;
      }
      svg {
        margin-right: 12px;
        width: 24px;
        height: 24px;
        transition: all 0.3s ease-out;
      }
    }
  }
  .box {
    position: relative;
    overflow: hidden;
    height: 32px;
    background-color: $skeleton;
    border-radius: 6px;
    width: 120px;
    margin: 0 auto;
  }
  .button {
    position: relative;
    overflow: hidden;
    background-color: $skeleton;
    border-radius: 500px;
    width: 100%;
    height: 40px;
  }
}
