import { useEffect, useState } from "react";
import Button from "../../components/Button";
import styles from "../../styles/shop.module.scss";
import { CircularProgress } from "@mui/material";
import CountryCarousel from "../../components/CountryCarousel";
import { useSelector } from "react-redux";
import lunr from "lunr";
import Country from "../../components/Country";
import SearchBar from "../../components/SearchBar";
import Loader from "../../components/utils/Loader";
import CountrySkeletonRow from "../../components/CountrySkeletonRow";
import { Link } from "react-router-dom";

const Shop = () => {
  const { countries, zones } = useSelector((state: any) => state);

  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const handleSearchChange = (e: any) => {
    setSearchQuery(e.target.value);
  };

  const clearSearch = () => {
    setSearchQuery("");
  };

  useEffect(() => {
    if (searchQuery === "") {
      setSearchResults([]);
      setShowSearchResults(false);
    }
  }, [searchQuery]);

  const handleSearch = () => {
    /*if (searchQuery === "") return;
    // Search countries for search query
    const countrySearch = lunr(function () {
      (this as any).ref("countryCode");
      (this as any).field("countryName");
      countries.forEach((countryItem: any) => {
        (this as any).add(countryItem);
      });
    });

    const results = countrySearch.search(
      `${searchQuery
        .trim()
        .split(" ")
        .map((i, index) => {
          if (i.length) {
            return `${index === 0 ? "*" : ""}${i.trim()}${
              index === 0 ? "*" : ""
            }`;
          }
        })
        .join(" ")} ${searchQuery.trim()}`
    );
    let resultArray = [] as any;
    results.forEach((result: any) => {
      const countryToAdd = countries.find(
        (country: any) => country.countryCode == result.ref
      );
      resultArray.push(countryToAdd);
    });
    setSearchResults(resultArray);
    setShowSearchResults(true);*/
    if (searchQuery === "") return;
    const searchStrings = searchQuery.split(" ");
    console.log(searchStrings);
    const filteredCountries = countries.filter((country: any) =>
      searchStrings.some(
        (str: string) =>
          country.countryName.toLowerCase().includes(str.toLowerCase()) ||
          country.countryCode.toLowerCase().includes(str.toLowerCase())
      )
    );
    setSearchResults(filteredCountries);
    setShowSearchResults(true);
  };

  const getZone = (name: any, all = false) => {
    if (zones) {
      if (all) {
        return zones.find((zone: any) => zone.zoneName === name).countries;
      } else {
        return zones
          .find((zone: any) => zone.zoneName === name)
          .countries.filter(
            (country: any) =>
              country.countryImage !==
              "https://public-trill-bucket.s3.eu-north-1.amazonaws.com/Country-Images/Placeholder_Image.png"
          )
          .slice(0, 6);
      }
    } else {
      return [];
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.searchSection}>
        <div className={styles.background}>
          <h2>
            The future of connectivity is here!
            <br />
            Where are you heading?
          </h2>
          <p>
            Whether you need data, a phone number, or both to stay connected, we
            have what you need to get onboard with you.
          </p>
          <SearchBar
            handleSearch={handleSearch}
            id="plan-search-input"
            searchQuery={searchQuery}
            placeholder="Search country or region"
            handleSearchChange={handleSearchChange}
            clearSearch={clearSearch}
            maxWidth={420}
            disabled={zones.length === 0}
          />
        </div>
      </div>
      {showSearchResults ? (
        searchResults.length ? (
          <div className={styles.countries}>
            {searchResults.map((country: any) => (
              <Country
                key={`country-tile-${country.countryName}`}
                name={country.countryName}
                image={country.countryImage}
                flag={country.iconUrl}
                countryCode={country.countryCode}
              />
            ))}
          </div>
        ) : (
          <div className={styles.noResults}>
            <h3>No results</h3>
            <p>
              We didn’t find any countries named “{searchQuery}”.
              <br />
              Please double-check your text and try again or browse all our
              countries.
            </p>
            <Button onClick={clearSearch}>Browse all countries</Button>
            <img
              src="/images-int/shop/no_results_bush.svg"
              className={styles.bush}
            />
            <img
              src="/images-int/shop/no_results_tree.svg"
              className={styles.tree}
            />
          </div>
        )
      ) : zones.length ? (
        <>
          <div>
            {zones.map((zone: any) => {
              if (zone.countries.length && zone.zoneName !== "Global") {
                return (
                  <CountryCarousel
                    key={`zone-row-${zone.zoneName}`}
                    title={zone.zoneName}
                    countries={zone.countries}
                  />
                );
              }
            })}
          </div>
          <div className={styles.zones}>
            <h4>Travelling to multiple countries?</h4>
            <p>Our regional plans will get you covered</p>
            <div className={styles.zoneTileContainer}>
              {zones.map((zone: any) => {
                return (
                  zone.zoneName !== "Global" && (
                    <Link
                      className={styles.zoneTileLink}
                      style={{ textDecoration: "none" }}
                      to={`/shop/select-plan/${zone.zoneId}`}
                      key={`zone-tile-${zone.zoneName}`}
                    >
                      <div className={styles.zoneTile}>
                        <div className={styles.imageContainer}>
                          <div className={styles.stickyImageContainer}>
                            <img src={zone.zoneImage} />
                          </div>
                        </div>
                        <div className={styles.name}>{zone.zoneName}</div>
                      </div>
                    </Link>
                  )
                );
              })}
            </div>
          </div>
        </>
      ) : (
        <>
          <CountrySkeletonRow />
          <CountrySkeletonRow />
          <CountrySkeletonRow />
          <CountrySkeletonRow />
        </>
      )}
    </div>
  );
};

export default Shop;
