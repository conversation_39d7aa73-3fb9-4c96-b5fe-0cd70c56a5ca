import { Fade } from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { Clear, MagnifyingGlass } from "../svgs";
import styles from "./search-bar.module.scss";
import $ from "jquery";

const SearchBar = ({
  handleSearch,
  id,
  searchQuery,
  placeholder,
  handleSearchChange,
  clearSearch,
  maxWidth = "100%",
  disabled,
  lightMode
}: any) => {
  const [isFocused, setIsFocused] = useState(false);
  const searchInput = useRef(null);

  useEffect(() => {
    $(`#${id}`).on("focus", () => {
      setIsFocused(true);
    });
    $(`#${id}`).on("blur", () => {
      setIsFocused(false);
    });
  }, []);

  return (
    <div className={styles.searchContainer + ' searchBar'}>
      <div
        className={`${styles.inputContainer} ${isFocused && styles.focus} ${
          searchQuery.length && styles.filled
        }`}
        style={{ maxWidth: maxWidth, borderColor: isFocused && lightMode && '#838CA0' }}
      >
        <div className={styles.magGlass}>
          <MagnifyingGlass />
        </div>
        <input
          placeholder={placeholder}
          id={id}
          ref={searchInput}
          value={searchQuery}
          onChange={(e) => handleSearchChange(e)}
          onKeyDown={(event) => {
            if (event.key === "Enter") {
              event.preventDefault();
              $(`#${id}`).trigger("blur");
              handleSearch();
            }
          }}
          disabled={disabled}
        />
        <Fade in={searchQuery.length !== 0}>
          <button onClick={clearSearch} className={styles.clearButton}>
            <Clear />
          </button>
        </Fade>
      </div>
    </div>
  );
};

export default SearchBar;
