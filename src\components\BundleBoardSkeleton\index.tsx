import { Link } from "react-router-dom";
import styles from "../BundleBoard/bundle-board.module.scss";
import styles2 from "./bundle-board-skeleton.module.scss";
import Button from "../Button";
import { Plus, Trolley } from "../svgs";
import DashboardPlanSkeleton from "../DashboardPlanSkeleton";
import Shimmer from "../Shimmer";

const BundleBoardSkeleton = () => {
  return (
    <>
      <div className={styles.title}>
        <div className={styles2.title}>My eSIMs</div>
        <Link style={{ textDecoration: "none" }} to="/#plans-section">
          <Button style={{ whiteSpace: "nowrap" }}>
            <Trolley />
            Get a new plan
          </Button>
        </Link>
      </div>
      <div className={styles.planTilesContainer}>
        <DashboardPlanSkeleton />
        <DashboardPlanSkeleton />
        <DashboardPlanSkeleton />
        <DashboardPlanSkeleton />
        <DashboardPlanSkeleton />
        <DashboardPlanSkeleton />
      </div>
    </>
  );
};

export default BundleBoardSkeleton;
