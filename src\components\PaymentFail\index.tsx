import Button from "../Button";
import styles from "./payment-fail.module.scss";
import Modal from "../Modal";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import { useEffect } from "react";

const PaymentFail = ({ show, setShow}: any) => {
  const navigate = useNavigate();

  return (
    <Modal show={show} style={{ maxWidth: 490 }}>
      <div className={styles.container}>
        <img src="/images-int/payment/payment_fail.svg" />
        <h4>{ t('account.checkout.unsuccessPayment') }</h4>
        <p>{ t('account.checkout.paymentProcess') }</p>
        <p>{ t('account.checkout.checkAgain') }</p>
        <ul>
          <li>{ t('account.checkout.cardDetails') }</li>
          <li>{ t('account.checkout.anotherMethod') }</li>
          <li>{ t('account.checkout.bankCheck') }</li>
        </ul>
        <div className={styles.buttons}>
          <Button
            style={{ height: 48 }}
            onClick={() => {
              setShow(false);
            }}
          >
            { t('buttons.tryAgain') }
          </Button>
          <Button
            style={{ height: 48 }}
            onClick={() => {
              navigate("/dashboard/home");
            }}
            color="secondary"
          >
            { t('general.dashboard')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default PaymentFail;
