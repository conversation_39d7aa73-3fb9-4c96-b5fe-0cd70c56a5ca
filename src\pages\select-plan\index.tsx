import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate, useParams } from "react-router-dom";
import Button from "../../components/Button";
import styles from "../../styles/select-plan.module.scss";
import { ApiGet } from "../api/api";
import PlanTypeSkeleton from "../../components/PlanTypeSkeleton";
import { isNumeric } from "../../components/utils/CardDetailsCheckers";
import { PopularStar } from "../../components/svgs";

const SelectPlan = () => {
  const { countryCode }: any = useParams();
  const dispatch = useDispatch();
  const { toastersOnLoad, userInfo } = useSelector(
    (state: any) => state
  );

  const { countries, zones } = useSelector((state: any) => state);

  const [selectedCountry, setSelectedCountry] = useState({
    countryCode: "",
    iconUrl: "",
    countryImage: "",
    countryName: "",
  });

  useEffect(() => {
    if (countries.length) {
      let country = countries.find(
        (item: any) => item.countryCode === countryCode.toUpperCase()
      );

      if (!country) {
        if (isNumeric(countryCode)) {
          let zone = zones[parseInt(countryCode) - 1];

          if (zone) {
            country = {
              countryCode: "GB",
              countryImage: zone.zoneImage,
              countryName: zone.zoneName,
              iconUrl: zone.zoneImage,
            };
          }
        }
      }

      if (country) {
        setSelectedCountry(country);

        dispatch({
          type: "set",
          selectedCountry: country,
        });
      }
    }
  }, [countries, countryCode]);

  const navigate = useNavigate();

  const [showBasket, setShowBasket] = useState(false);

  const [loading, setLoading] = useState(true);

  const [hasPlanType, setHasPlanType] = useState({
    combo: false,
    esim: false,
    credit: false,
    vn: false,
  });

  useEffect(() => {
    if (toastersOnLoad.addedToBasket) {
      setShowBasket(true);
      dispatch({
        type: "set",
        toastersOnLoad: {
          ...toastersOnLoad,
          addedToBasket: false,
        },
      });
    }
  }, []);

  useEffect(() => {
    ApiGet(
      `/plans?planType=all&${
        isNumeric(countryCode) ? "zoneId" : "countryCode"
      }=${countryCode}`
    )
      .then((response) => {
        const plansAvailable = response.data;
        setHasPlanType({
          combo: plansAvailable.comboPlans.length !== 0,
          esim: plansAvailable.esimPlans.length !== 0,
          credit: plansAvailable.creditPlans.length !== 0,
          vn: plansAvailable.didPlans.length !== 0,
        });
        setLoading(false);
      })
      .catch((error) => {
        navigate("/login");
      });
  }, [countryCode]);

  return (
    <div className={styles.main}>
      <div className={styles.countryName}>
        <div
          className={styles.flag}
          style={{ backgroundImage: `url(${selectedCountry.iconUrl})` }}
        />
        <div className={styles.text}>{selectedCountry.countryName}</div>
      </div>
      <h2 className={styles.shopHeading}>Shop</h2>
      <p className={styles.shopTagline}>
        Take Gist with you! Select your product
      </p>
      {/*<Fade in={showBasket} unmountOnExit>
        <div className={styles.addedToBasket}>
          <div
            style={{ display: "flex", alignItems: "center", marginRight: 20 }}
          >
            <img src="/images-int/shop/trolley.png" />
            <div className={styles.planInfo}>
              <h5>Added to cart!</h5>
              {basket && (
                <div className={styles.data}>
                  {basket.classification} plan |{" "}
                  {formatPlanData(basket[basket.length - 1])}
                </div>
              )}
            </div>
          </div>
          <div className={styles.buttons}>
            <Button
              color="secondary"
              onClick={() => {
                setShowBasket(false);
              }}
              style={{ marginRight: 12, whiteSpace: "nowrap" }}
            >
              Continue Shopping
            </Button>
            <Link style={{ textDecoration: "none" }} to="/cart-summary">
              <Button style={{ whiteSpace: "nowrap" }}>Go to Cart</Button>
            </Link>
          </div>
        </div>
      </Fade>*/}
      <div className={styles.planTypes}>
        {loading ? (
          [0, 1, 2].map((i) => (
            <PlanTypeSkeleton key={`plan-type-sekelton-${i}`} />
          ))
        ) : (
          <>
            {hasPlanType.esim && (
              <div className={styles.planTypeContainer}>
                <div style={{ height: 35 }} />
                <div className={`${styles.imageContainer} ${styles.data}`}>
                  <div className={styles.imageMask} />
                </div>
                <div className={styles.infoContainer}>
                  <div>
                    <div className={styles.name}>Worldwide Data</div>
                    <div className={styles.info}>100+ countries</div>
                  </div>
                  <Link to={`/shop/esim/${countryCode}`}>
                    <Button>Select</Button>
                  </Link>
                </div>
              </div>
            )}
            {hasPlanType.combo && (
              <div className={styles.planTypeContainer}>
                <div className={styles.popularContainer}>
                  <PopularStar />
                  <div className={styles.popularText}>Most Popular!</div>
                </div>
                <div className={`${styles.imageContainer} ${styles.combo}`}>
                  <div className={styles.imageMask} />
                  {userInfo.comboCreditEligible && (
                    <img
                      src="/images-int/shop/free-credit.png"
                      className={styles.freeCredit}
                    />
                  )}
                </div>
                <div className={styles.infoContainer}>
                  <div>
                    <div className={styles.name}>Combos</div>
                    <div className={styles.info}>Data + Virtual Number</div>
                  </div>
                  <Link to={`/shop/combo/${countryCode}`}>
                    <Button>Select</Button>
                  </Link>
                </div>
              </div>
            )}
            {/*<Link
              to={hasPlanType.credit ? `/shop/credit/${countryCode}` : ""}
              onClick={() => {
                if (!hasPlanType.credit) {
                  dispatch({
                    type: "notify",
                    payload: {
                      error: true,
                      heading: "Top up limit exceeded for today",
                      message: "Come back tomorrow to top-up your account",
                    },
                  });
                }
              }}
            >
              <div className={styles.planTypeContainer}>
                <div className={styles.name}>Global Credit</div>
                <div className={`${styles.imageContainer} ${styles.credit}`}>
                  <div className={styles.imageMask} />
                </div>
              </div>
            </Link>*/}
            {hasPlanType.vn && (
              <div className={styles.planTypeContainer}>
                <div style={{ height: 35 }} />
                <div className={`${styles.imageContainer} ${styles.number}`}>
                  <div className={styles.imageMask} />
                </div>
                <div className={styles.infoContainer}>
                  <div>
                    <div className={styles.name}>Virtual Number</div>
                    <div className={styles.info}>Mobile & Local Numbers</div>
                  </div>
                  <Link to={`/shop/vn/${countryCode}`}>
                    <Button>Select</Button>
                  </Link>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default SelectPlan;
