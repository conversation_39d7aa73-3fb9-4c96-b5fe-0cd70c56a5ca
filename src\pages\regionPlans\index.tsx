import { CaretDown } from "../../components/svgs";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import SearchBar from "../../components/SearchBar";
import { ApiGet } from "../api/api";
import styles from "../../styles/allCountires.module.scss";
import plansStyles from "../../styles/region-plans.module.scss";
import { useParams } from "react-router-dom";
import { NoSearchResults } from "../../components/NoSearchResults/NoSearchResults";
import PlanTile from "../../components/PlanTile";
import { useTranslation } from "react-i18next";
import { Helmet } from "react-helmet-async";
import PlanTileSkeleton from "../../components/PlanTileSkeleton";
import { t } from "i18next";
import { useMediaQuery } from "@mui/material";

export type regionTypes = {
  countries?: [];
  zoneName?: String;
  zoneId?: Number;
};

const RegionPlans = () => {
  const { i18n } = useTranslation();
  const { zones } = useSelector((state: any) => state);
  const [isOpen, setIsOpen] = useState(false);
  const [plans, setPlans] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [selectedZone, setSelectedZone] = useState<regionTypes>({});
  const [selectedCountry, setSelectedCountry] = useState("");
  const [selectedIndex, setSelectedIndex] = useState(null);
  const [plansLoading, setPlansLoading] = useState(false);

  const { id } = useParams();

  const under768 = useMediaQuery("(max-width: 768px)");

  useEffect(() => {
    if (zones.length > 0) {
      let filteredZone = zones.filter(
        (zone: any) => zone.zoneId === Number(id)
      );
      setSelectedZone(filteredZone[0]);
      setSearchResults(filteredZone[0].countries);
    }
  }, [zones, id]);

  useEffect(() => {
    if (searchQuery === "") {
      clearSearch();
    } else {
      handleSearch();
    }
  }, [searchQuery]);

  const handleSearchChange = (e: any) => {
    setSearchQuery(e.target.value);
  };

  const clearSearch = () => {
    setSearchQuery("");
    if (selectedZone.countries) {
      setSearchResults(selectedZone.countries);
    }
  };

  const handleSelect = (code: any, index: any) => {
    if (code === selectedCountry) {
      setIsOpen(false);
      setSelectedCountry("");
      setSelectedIndex(null);
    } else {
      setPlansLoading(true);
      setSelectedCountry(code);
      setSelectedIndex(index);
      setIsOpen(true);
      ApiGet(`/plans?countryCode=${code}`).then((response: any) => {
        if (response) {
          setPlansLoading(false);
          setPlans(
            response.data.esimPlans.map((plan: any) => ({
              ...plan,
              countryCode: code,
            }))
          );
        }
      });
    }
  };

  const handleSearch = () => {
    if (searchQuery === "") {
      clearSearch();
      return;
    }
    const searchStrings = searchQuery.split(" ");
    if (selectedZone && selectedZone.countries && searchQuery.length > 0) {
      const filteredCountries = selectedZone.countries.filter((country: any) =>
        searchStrings.some(
          (str: string) =>
            country.countryName.toLowerCase().includes(str.toLowerCase()) ||
            country.countryCode.toLowerCase().includes(str.toLowerCase())
        )
      );
      setSearchResults(filteredCountries);
    }
  };

  const returnPlans = () => {
    return (
      <div className={plansStyles.selectedCountry}>
        {plansLoading ? (
          Array.from({ length: 5 }).map((x, i) => (
            <PlanTileSkeleton index={i} />
          ))
        ) : plans.length > 0 ? (
          plans.map((plan: any, index) => {
            return <PlanTile plan={plan} index={index} />;
          })
        ) : (
          <p>No Plans Found</p>
        )}
      </div>
    );
  };

  return (
    <>
      <div className={styles.plansView}>
        <Helmet>
          <title>
            {t("general.orbit")}|{" "}
            {i18n.language === "en"
              ? `${selectedZone.zoneName} Countries`
              : `دول ${selectedZone.zoneName}`}
          </title>
          <meta name="description" content={t("plan.ifTravelCovered")} />
        </Helmet>
        {i18n.language === "en" ? (
          <h2>{selectedZone.zoneName} Countries</h2>
        ) : (
          <h2>دول {selectedZone.zoneName}</h2>
        )}
        <SearchBar
          handleSearch={handleSearch}
          id="plan-search-input"
          searchQuery={searchQuery}
          placeholder={t("placeholders.searchCountry")}
          handleSearchChange={handleSearchChange}
          clearSearch={clearSearch}
          maxWidth={420}
        />
      </div>
      <div className={"flex " + plansStyles.contianer}>
        <div
          className={
            "flex justify-content-center flex-wrap " +
            plansStyles.countryContainer
          }
        >
          {searchResults.length > 0 ? (
            searchResults.map((country: any, index: number) => {
              return (
                <>
                  <div
                    key={country.countryCode}
                    onClick={() => handleSelect(country.countryCode, index)}
                    className={
                      (selectedCountry === country.countryCode
                        ? plansStyles.open
                        : "") +
                      " " +
                      plansStyles.countryBox +
                      " " +
                      plansStyles.countryBoxRegion
                    }
                  >
                    <div
                      className={plansStyles.flag}
                      style={{ backgroundImage: `url(${country.iconUrl})` }}
                    />
                    <p>{country.countryName}</p>
                    <CaretDown />
                  </div>
                  {under768
                    ? selectedCountry === country.countryCode && (
                        <>{isOpen && returnPlans()}</>
                      )
                    : ((index + 1) % 5 === 0 ||
                        index + 1 === searchResults.length) && (
                        <div
                          className="w-full"
                          style={{
                            display:
                              selectedIndex !== null &&
                              Math.ceil((index + 1) / 5) ===
                                Math.ceil((selectedIndex + 1) / 5)
                                ? "block"
                                : "none",
                          }}
                          key={country.countryCode + index}
                        >
                          {isOpen && returnPlans()}
                        </div>
                      )}
                </>
              );
            })
          ) : (
            <NoSearchResults query={searchQuery} />
          )}
        </div>
      </div>
    </>
  );
};

export default RegionPlans;
