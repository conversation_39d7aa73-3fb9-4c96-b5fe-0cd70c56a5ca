import { useState } from "react";
import Button from "../../components/Button";
import { Input } from "../../components/Input";
import { Helmet } from "react-helmet-async";
import {
  createStateObject,
  handleInputChange,
  clearInput,
} from "../../components/utils/InputHandlers";
import styles from "../../styles/forgot-password.module.scss";
import auth from "../../components/utils/firebaseAuth";
import { t } from "i18next";
import { ApiPost } from "../api/api";
import { useDispatch } from "react-redux";

const ForgotPassword = () => {
  // Set up initial states
  const dispatch = useDispatch();
  const [personal, setPersonal] = useState(createStateObject(["email"]));
  const [timer, setTimer] = useState(null as any);
  const [timeToReset, setTimeToReset] = useState(30);
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const setTimeToResetCounter = () => {
    const clearTimer = () => {
      clearInterval(timer);
    };
    clearTimer();
    setTimer(
      setInterval(() => {
        setTimeToReset((prev) => {
          if (prev > 0) {
            return prev - 1;
          } else {
            clearTimer();
            return 0;
          }
        });
      }, 1000)
    );
  };

  const submitEmail = () => {
    setLoading(true);
    ApiPost("/users/send/email/check", {
      email: personal.email,
    })
      .then((response: any) => {
        if (response?.data?.passwordPresent) {
          ApiPost("/users/forgotpassword", {
            emailId: personal.email,
          })
            .then((response) => {
              setLoading(false);
              setEmailSent(true);
              setTimeToReset(30);
              setTimeToResetCounter();
            })
            .catch((error) => {
              setLoading(false);
              dispatch({
                type: "notify",
                payload: {
                  error: true,
                  heading: "Something went wrong",
                  message: error.response.data.message,
                },
              });
            });
        } else {
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              heading: `Your account is registered using ${response.data.accountType.replace(
                "Social-",
                ""
              )}`,
              message: "Please log in via the social login options",
            },
          });
        }
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: error.response.data.message,
          },
        });
      });

    /**/
  };

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          {t("general.orbit")}| {t("account.forgetPass")}
        </title>
      </Helmet>
      <div id="recaptcha-container" />
      <div className={styles.innerContainer}>
        {!emailSent ? (
          <>
            <h2>{t("account.forgetPass")}</h2>
            <p className={styles.text}>{t("account.msgs.resetPasswordLink")}</p>
            <Input
              value={personal["email"]}
              label={t("placeholders.email")}
              disabled={loading}
              onChange={(e: any) => {
                handleInputChange("email", e, personal, setPersonal);
              }}
              clear={() => {
                clearInput("email", setPersonal);
              }}
              error={personal.errors["email"]}
              key="input-email-forget-password"
              onKeyDown={submitEmail}
            />
            <Button onClick={submitEmail} loading={loading}>
              {t("account.msgs.resetMail")}
            </Button>
          </>
        ) : (
          <div className={styles.verifyMain}>
            <h3 className={styles.verifyTitle}>
              {t("account.msgs.checkMail")}
            </h3>
            <p>{t("account.msgs.onWayMail")}</p>
            <p>
              {t("account.msgs.resetPasswordCheckMail", {
                email: personal.email,
              })}
            </p>
            <div className={styles.resend}>
              <h5>{t("account.msgs.notRecieved")}</h5>
              {timeToReset !== 0 ? (
                <p>
                  {t("account.requestNewMailCounter")} 00:
                  {timeToReset.toString().padStart(2, "0")}
                </p>
              ) : (
                <Button
                  onClick={submitEmail}
                  style={{ height: 48, marginTop: 16, padding: "0px 50px" }}
                >
                  {t("account.requestNewMail")}
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ForgotPassword;
