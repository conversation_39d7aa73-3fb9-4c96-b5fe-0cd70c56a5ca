import styles from "./confirmation-modal.module.scss";
import Button from "../Button";
import { Fade } from "@mui/material";
import { useEffect } from "react";
import $ from "jquery";

const ConfirmationModal = ({
  show,
  setShow,
  proceed,
  heading,
  text,
  continueButton,
  cancelButton = "Cancel",
}: any) => {
  return (
    <Fade in={show} unmountOnExit>
      <div className={styles.container}>
        <div className={styles.modal}>
          <div className={styles.main} id="modal-scroll">
            <h3 className={styles.heading}>{heading}</h3>
            <p>{text}</p>
            <div className={styles.buttons}>
              <Button
                onClick={() => {
                  setShow(false);
                }}
                style={{ marginRight: 16 }}
                color="secondary"
              >
                {cancelButton}
              </Button>
              <Button
                onClick={() => {
                  setShow(false);
                  proceed();
                }}
                style={{ height: "auto", padding: "13px 24px" }}
              >
                {continueButton}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Fade>
  );
};

export default ConfirmationModal;
