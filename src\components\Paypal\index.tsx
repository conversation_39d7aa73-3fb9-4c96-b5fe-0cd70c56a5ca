import { useSelector } from "react-redux";
import styles from "./google-pay.module.scss";
import { useEffect } from "react";
import { ApiPostAuth } from "../../pages/api/api";

const Paypal = ({ completePayment }: any) => {
  const initPaypal = () => {
    // Loop over each payment method
    (window as any).paypal
      .getFundingSources()
      .forEach(function (fundingSource: any) {
        // Initialize the buttons
        var button = (window as any).paypal.Buttons({
          fundingSource: fundingSource,
          style: {
            height: 52,
          },
        });
        // Check if the button is eligible
        if (button.isEligible()) {
          // Render the standalone button for that payment method
          button.render("#paypal-button-container");
        }
      });
  };

  useEffect(initPaypal, []);

  return <div />;
};

export default Paypal;
