import { Link } from "react-router-dom";
import styles from "./see-all-country.module.scss";

const SeeAllCountry = ({ link }: any) => {
  return (
    <Link to={link}>
      <div className={styles.main}>
        <div className={styles.imageContainer}>
          <div className={styles.stickyImageContainer}>
            <svg
              width="32"
              height="32"
              viewBox="0 0 32 32"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M8 24L24 8"
                stroke="#7448B0"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M11 8H24V21"
                stroke="#7448B0"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <div>See all</div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default SeeAllCountry;
