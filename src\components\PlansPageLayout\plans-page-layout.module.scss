@use "../../styles/theme.scss" as *;

.container {
  width: 100%;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  flex-grow: 1;
  display: flex;
  align-items: stretch;
}

.background {
  background: linear-gradient(
    180deg,
    rgba(22, 11, 42, 0.8) 0%,
    rgba(22, 11, 42, 0.75) 48.96%,
    rgba(22, 11, 42, 0.8) 100%
  );
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: stretch;
  justify-content: stretch;
}

.widthContainer {
  max-width: 1600px;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  justify-content: space-between;
}

.backButton {
  padding: 0;
  background: none;
  border: none;
  cursor: pointer;
  color: #fff;
  svg {
    vertical-align: middle;
  }
}

.topBar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 50px 0 55px;
  color: #fff;
  margin-bottom: 36px;
  @media (max-width: 768px) {
    padding: 24px 24px 0 24px;
    margin-bottom: 18px;
  }
  .countryName {
    display: flex;
    align-items: center;
    .flag {
      width: 24px;
      height: 24px;
      border-radius: 24px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      border: 1px solid rgba(0, 0, 0, 0.3);
      margin-right: 12px;
    }
    .text {
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
    }
  }
}
