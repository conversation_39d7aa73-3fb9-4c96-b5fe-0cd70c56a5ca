import styles from "./cookie-pop-up.module.scss";
import { motion } from "framer-motion";
import Button from "../Button";
import { <PERSON><PERSON> } from "../svgs";
import { Link } from "react-router-dom";
import { t } from "i18next";

const CookiePopUp = ({ accept }: any) => {
  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 200 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 200 }}
      transition={{
        type: "tween",
        ease: "easeInOut",
        duration: 0.3,
      }}
      className={styles.main}
      key="cookie">
      <div className={styles.textContainer}>
        <div className={styles.cookieContainer}>
          <Cookie />
          <h5 className={styles.heading}>{ t('cookies.cookies') }!</h5>
        </div>
        <p className={styles.message}>
          { t('cookies.cookiesMsg') }
        </p>
      </div>
      <div className={styles.buttons}>
        <Link style={{ textDecoration: "none" }} to="/privacy-policy">
          <Button style={{ whiteSpace: "nowrap" }} color="tertiary">
            { t('general.learnMore') }
          </Button>
        </Link>
        <Button onClick={accept}>{ t('general.continue') }</Button>
      </div>
    </motion.div>
  );
};

export default CookiePopUp;
