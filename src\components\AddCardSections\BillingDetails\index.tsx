import { Input } from "../../Input";
import styles from "./billing-details.module.scss";
import {
  clearInput,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  placeholders,
} from "../../utils/InputHandlers";
import { validateAll } from "indicative/validator";
import Button from "../../Button";
import CountryDropdown from "../../CountryDropdown";
import PhoneInput from "../../PhoneInput";
import { motion } from "framer-motion";
import { v4 as uuid } from "uuid";
import { t } from "i18next";

// Set up required fields and FLV data for card details
const primaryBillingFields = ["address", "city", "postcode"];
const otherBillingFields = ["country", "countryCode", "phoneNumber"];
const combinedBillingFields = [...primaryBillingFields, ...otherBillingFields];
const cardRules = getRules(combinedBillingFields);
const cardMessages = getMessages(combinedBillingFields);

const BillingDetails = ({
  billingDetails,
  setBillingDetails,
  cancel,
  proceed,
  loading,
  edit,
}: any) => {
  const submitBillingDetails = () => {
    const data = {
      address: billingDetails.address,
      city: billingDetails.city,
      postcode: billingDetails.postcode,
      country: billingDetails.country.value,
      countryCode: billingDetails.countryCode,
      phoneNumber: billingDetails.phoneNumber,
    };

    validateAll(data, cardRules, cardMessages)
      .then((response) => {
        proceed({
          id: uuid(),
          addressOne: data.address,
          city: data.city,
          postcode: data.postcode,
          country: data.country,
          phoneCountryCode: data.countryCode,
          phoneNumber: data.phoneNumber,
        });
      })
      .catch((errors) => {
        displayErrors(errors, setBillingDetails);
      });
  };

  return (
    <div className={styles.cardDetails} style={{ padding: edit ? 0 : "" }}>
      <h3>{edit ? "Edit" : "New"} billing details</h3>
      <div className={styles.inputContainer}>
        {primaryBillingFields.map((prop) => (
          <Input
            placeholder={placeholders[prop]}
            value={billingDetails[prop]}
            disabled={loading}
            onChange={(e: any) => {
              handleInputChange(prop, e, billingDetails, setBillingDetails);
            }}
            error={billingDetails.errors[prop]}
            clear={() => {
              clearInput(prop, setBillingDetails);
            }}
            onKeyDown={submitBillingDetails}
            key={`input-${prop}`}
          />
        ))}
        <CountryDropdown
          cardData={billingDetails}
          setCardData={setBillingDetails}
          disabled={loading}
        />
        <PhoneInput
          state={billingDetails}
          setState={setBillingDetails}
          onKeyDown={submitBillingDetails}
          disabled={loading}
        />
      </div>
      <motion.div
        transition={{ durarion: 300, easing: "ease-out" }}
        layoutId="add-card-buttons"
        className={styles.buttons}
      >
        <Button style={{ marginRight: 16 }} color="secondary" onClick={cancel}>
          { t('buttons.cancel') }
        </Button>
        <Button onClick={submitBillingDetails}>{t('buttons.save')}</Button>
      </motion.div>
    </div>
  );
};

export default BillingDetails;
