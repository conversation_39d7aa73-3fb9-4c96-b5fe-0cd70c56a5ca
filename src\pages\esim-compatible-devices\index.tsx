import { useEffect, useState } from "react";
import styles from "../../styles/esim-compatible-devices.module.scss";
import {
  androidSteps,
  androidImages,
  iosSteps,
  iosImages,
} from "../../components/utils/esimInstructions";
import { Collapse } from "@mui/material";
import { SwitchTransition, CSSTransition } from "react-transition-group";

const EsimCompatibleDevices = () => {
  const [os, setOs] = useState("android");
  const [stepsToShow, setStepsToShow] = useState(androidSteps);
  const [activeStep, setActiveStep] = useState("1");

  useEffect(() => {
    if (os === "android") {
      setStepsToShow(androidSteps);
    } else {
      setStepsToShow(iosSteps);
    }
    setActiveStep("1");
  }, [os]);

  return (
    <div className={styles.container}>
      <div className={styles.topSection}>
        <div className={styles.background}>
          <h2>Check if your device is eSIM compatible</h2>
          <p>
            Stay tech-savvy and ensure that your device carrier is unlocked and
            that your device supports eSIM.
            <br />
            By proceeding you are taking full responsibility for the
            compatiability of your device. Please note, Gist Mobile will not be
            liable for any issues due to device incompatiblity.
          </p>
        </div>
      </div>
      <div className={styles.phoneListContainer}>
        <div>
          <div className={styles.singleList}>
            <h5>Samsung</h5>
            <ul>
              <li>Samsung Galaxy S22 5G, Ultra 5G, S22</li>
              <li>Samsung Fold LTE model</li>
              <li>Samsung Z Flip 4</li>
              <li>Samsung Z Fold 4</li>
              <li>Samsung Galaxy Z Fold 3 5G</li>
              <li>Samsung Galaxy Z Flip 5G</li>
              <li>Samsung Galaxy Z Flip</li>
              <li>Samsung Galaxy Z Fold2 5G</li>
              <li>Samsung Galaxy Fold</li>
              <li>Samsung Galaxy S21+ 5G</li>
              <li>Samsung Galaxy S21 Ultra 5G</li>
              <li>Samsung Galaxy Note 20 Ultra, Ultra 5G</li>
              <li>Samsung Galaxy Note 20 FE 5G</li>
              <li>Samsung Galaxy Note 20 FE</li>
              <li>Samsung Galaxy S20, S20+ and S20 Ultra</li>
            </ul>
          </div>
          <div className={styles.singleList}>
            <h5>Google Pixel</h5>
            <ul>
              <li>Google Pixel 7 Pro</li>
              <li>Google Pixel 7</li>
              <li>Google Pixel 6 Pro</li>
              <li>Google Pixel 6</li>
              <li>Google Pixel 5a 5G</li>
              <li>Google Pixel 5</li>
              <li>Google Pixel 4a</li>
              <li>Google Pixel 4</li>
              <li>Google Pixel 3 & 3XL (Limited support)</li>
              <li>Google Pixel 2</li>
            </ul>
          </div>
          <div className={styles.singleList}>
            <h5>Huawei</h5>
            <ul>
              <li>P40, P40 Pro</li>
              <li>Mate P40 Pro</li>
            </ul>
          </div>
          <div className={styles.singleList}>
            <h5>Other</h5>
            <ul>
              <li>Oppo Find X5, Find X5 Pro</li>
              <li>Oppo Find X3, Find X3 Pro</li>
              <li>Oppo Reno 5A, Reno 6 Pro 5G</li>
              <li>Oppo A55s</li>
              <li>Motorola Razr, Razr 5G</li>
              <li>Microsoft Surface Duo, Surface Pro X</li>
              <li>Sony Xperia 1 IV, Xperia 10 III Lite</li>
            </ul>
          </div>
        </div>
        <div>
          <div className={styles.singleList}>
            <h5>iPhone</h5>
            <ul>
              <li>iPhone 14</li>
              <li>iPhone 14 Plus</li>
              <li>iPhone 14 Pro Max</li>
              <li>iPhone 14 Pro</li>
              <li>iPhone 13, 13 Pro, 13 Pro Max, 13 Mini</li>
              <li>iPhone 12, 12 Pro, 12 Pro Max, 12 Mini</li>
              <li>iPhone SE</li>
              <li>iPhone 11, 11 Pro, 11 Pro Max</li>
              <li>iPhone XS, XS Max</li>
              <li>iPhone XR</li>
            </ul>
          </div>
          <div className={styles.singleList}>
            <h5>iPad</h5>
            <ul>
              <li>iPad Pro 12.9‑inch (4th generation)</li>
              <li>iPad Pro 12.9‑inch (3rd generation)</li>
              <li>iPad Pro 11‑inch (2nd generation)</li>
              <li>iPad Pro 11‑inch (1st generation)</li>
              <li>iPad Air (4th generation)</li>
              <li>iPad Air (3rd generation)</li>
              <li>iPad (8th generation)</li>
              <li>iPad (7th generation)</li>
              <li>iPad mini (5th generation)</li>
            </ul>
          </div>
        </div>
      </div>
      <div className={`${styles.instructions} max-width-container`}>
        <h3>Installing and Setting Up Your eSIM</h3>
        <p className={styles.info}>
          Whether you're using an iOS or Android device, you're just a few steps
          away from accessing Gist's plans with utmost ease. Follow the simple
          instructions below to seamlessly install your eSIM and get ready to
          enjoy Gist's services in no time.
        </p>
        <div className={styles.toggle}>
          <div
            className={`${styles.label} ${os === "android" && styles.active}`}
            onClick={() => {
              setOs("android");
            }}
          >
            Android
          </div>
          <div
            className={`${styles.label} ${os === "ios" && styles.active}`}
            onClick={() => {
              setOs("ios");
            }}
          >
            iOS
          </div>
          <div
            className={`${styles.toggleThumb} ${os === "ios" && styles.ios}`}
          />
        </div>
        <div className={styles.stepsContainer}>
          <SwitchTransition>
            <CSSTransition
              key={os}
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="fade"
            >
              <div className={styles.stepTile}>
                {stepsToShow.map((step: any) => (
                  <div
                    className={styles.stepItem}
                    onClick={() => {
                      setActiveStep(step.stepNumber);
                    }}
                  >
                    <div
                      className={`${styles.stepTitle} ${
                        activeStep === step.stepNumber && styles.active
                      }`}
                    >
                      <ol start={step.stepNumber}>
                        <li>{step.title}</li>
                      </ol>
                    </div>
                    <Collapse in={activeStep === step.stepNumber}>
                      <div className={styles.content}>{step.content}</div>
                    </Collapse>
                  </div>
                ))}
              </div>
            </CSSTransition>
          </SwitchTransition>
          <SwitchTransition>
            <CSSTransition
              key={os + activeStep}
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="fade"
            >
              <div
                className={`${styles.imageContainer} ${
                  os === "ios" && styles.iosImage
                }`}
              >
                <img
                  src={
                    os === "android"
                      ? androidImages[activeStep]
                      : iosImages[activeStep]
                  }
                  className={styles.stepImage}
                />
              </div>
            </CSSTransition>
          </SwitchTransition>
        </div>
      </div>
    </div>
  );
};

export default EsimCompatibleDevices;
