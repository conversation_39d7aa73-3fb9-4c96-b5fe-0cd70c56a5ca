import {
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  clearInput,
} from "../utils/InputHandlers";
import Button from "../Button";
import { Input } from "../Input";
import { useState } from "react";
import Modal from "../Modal";
import styles from "../../styles/register.module.scss";
import { validateAll } from "indicative/validator";
import { useDispatch } from "react-redux";
import { ApiPostAuth } from "../../pages/api/api";
import { t } from "i18next";

// Set up new password fields and FLV data
const passwordFields = ["password", "newPassword", "confirmNewPassword"];
const passwordRules = getRules(passwordFields);
const passwordMessages = getMessages(passwordFields);

const ChangePasswordModal = ({
  show,
  setShow,
}: {
  show: boolean;
  setShow: Function;
}) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [passwordData, setPasswordData] = useState(
    createStateObject(passwordFields)
  );

  // Handle reset password
  const changePassword = () => {
    const data = {
      password: passwordData.password,
      newPassword: passwordData.newPassword,
      confirmNewPassword: passwordData.confirmNewPassword,
    };
    validateAll(data, passwordRules, passwordMessages)
      .then((response) => {
        if (data.newPassword !== data.confirmNewPassword) {
          setPasswordData({
            ...passwordData,
            errors: {
              password: "",
              confirmPassword: "Passwords do not match",
            },
          });
        } else {
          setLoading(true);
          ApiPostAuth("/users/changepassword", {
            currentPassword: data.password,
            newPassword: data.newPassword,
            confirmNewPassword: data.confirmNewPassword,
          })
            .then((response) => {
              setLoading(false);
              dispatch({
                type: "notify",
                payload: {
                  error: false,
                  heading: response.data.message,
                },
              });
              setShow(false);
            })
            .catch((error) => {
              setLoading(false);
              dispatch({
                type: "notify",
                payload: {
                  error: true,
                  heading: t('buttons.oops'),
                  message:
                    error.response.data.message || "Something went wrong.",
                },
              });
            });
        }
      })
      .catch((errors) => {
        displayErrors(errors, setPasswordData);
      });
  };
  return (
    <Modal show={show} style={{ height: 420, maxWidth: 500, padding: 20 }}>
      <h2>{ t('account.editDetails') }</h2>
      <div className={styles.inputs}>
        {passwordFields.map((prop: any) => {
          return (
            <Input
              label={t('placeholders.' + prop)}
              value={passwordData[prop]}
              onChange={(e: any) => {
                handleInputChange(prop, e, passwordData, setPasswordData);
              }}
              error={passwordData.errors[prop]}
              clear={() => {
                clearInput(prop, passwordData);
              }}
              onKeyDown={changePassword}
              key={`input-${prop}`}
              password
              disabled={loading}
            />
          );
        })}
      </div>
      <div className="flex justify-content-between">
        <Button color="primary" onClick={changePassword}>
        { t('buttons.saveChanges') }
        </Button>
        <Button color="secondary" onClick={() => setShow(false)}>
          { t('buttons.cancel') }
        </Button>
      </div>
    </Modal>
  );
};

export default ChangePasswordModal;
