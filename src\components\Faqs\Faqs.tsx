import FaqQuestion from "../../components/FaqQuestion";
import { CmsApiGet } from "../../pages/api/cms-api";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

type questionsType = {
  id: number;
  attributes: {
    Question: String;
    Answer: String;
    Favourite: Boolean;
    faqs_category: any;
    top_faqs_type: any;
    topQuestionsType: String;
    slug: string;
  };
};

type faqsProps = {
  getQuestions?: Function;
  filter?: String;
  filterType?: String;
  favourite?: Boolean;
};

const Faqs = ({ getQuestions, filter, filterType, favourite }: faqsProps) => {
  const [questions, setQuestions] = useState<questionsType[]>([]);
  const { i18n } = useTranslation();

  useEffect(() => {
    CmsApiGet(
      `/api/faqs?locale=${i18n.language}&populate=deep${
        favourite ? "&filters[Favourite][$eq]=true" : ""
      }`
    ).then((response: any) => {
      setQuestions(response.data.data);
      if (getQuestions) {
        getQuestions(response.data.data);
      }
    });
  }, [i18n.language]);

  useEffect(() => {
    console.log(filter);
  }, [filter]);

  if (filter && filterType) {
    return (
      <>
        {questions.map((ques) => {
          if (
            ques.attributes.faqs_category.data?.attributes?.name === filter ||
            ques.attributes.top_faqs_type?.data?.attributes?.label === filter
          ) {
            return (
              <FaqQuestion
                key={`faq-${ques.id}`}
                question={ques.attributes.Question}
                answer={ques.attributes.Answer}
                id={ques.attributes.slug}
              />
            );
          }
        })}
      </>
    );
  }

  if (favourite) {
    return (
      <>
        {questions.map((ques) => {
          return (
            <FaqQuestion
              key={`faq-${ques.id}`}
              question={ques.attributes.Question}
              answer={ques.attributes.Answer}
              id={ques.attributes.slug}
            />
          );
        })}
      </>
    );
  }

  return <></>;
};

export default Faqs;
