import { useSelector } from "react-redux";
import PlanSelection from "../../components/PlanSelection";
import { useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import { ApiGet } from "../api/api";
import { isNumeric } from "../../components/utils/CardDetailsCheckers";

const SelectPhone = () => {
  const { countryCode } = useParams();

  const [countryPlans, setCountryPlans] = useState([]);

  const sortPlans = (a: any, b: any) => {
    const aCost = a.prices[0].cost,
      bCost = b.prices[0].cost,
      aMins = a.voiceAllowance,
      bMins = b.voiceAllowance,
      aSms = a.smsAllowance,
      bSms = b.smsAllowance,
      aValid = a.validity,
      bValid = b.validity;

    if (aCost === bCost) {
      if (aMins === bMins) {
        if (aSms === bSms) {
          if (aValid === bValid) {
            return 0;
          } else {
            return aValid - bValid;
          }
        } else {
          return aSms - bSms;
        }
      } else {
        return aMins - bMins;
      }
    } else {
      return aCost - bCost;
    }
  };

  useEffect(() => {
    ApiGet(
      `/plans?planType=vn&${
        isNumeric(countryCode) ? "zoneId" : "countryCode"
      }=${countryCode}`
    )
      .then((response) => {
        console.log(response);
        /*const vnPlans = response.data.didPlans;
        if (
          vnPlans.some(
            (plan: any) => plan.classification === "Local Numbers"
          ) &&
          vnPlans.some((plan: any) => plan.classification === "Mobile Numbers")
        ) {
          setActiveNumberType("mobile");
        } else {
          if (
            vnPlans.some((plan: any) => plan.classification === "Local Numbers")
          ) {
            setActiveNumberType("landline");
            setLocalOnly(true);
          } else {
            setActiveNumberType("mobile");
            setMobileOnly(true);
          }
        }*/
        setCountryPlans(response.data.didPlans.sort(sortPlans));
      })
      .catch((error) => {
        console.log(error);
      });
  }, [countryCode]);

  return (
    <PlanSelection
      planTypes={countryPlans}
      pageTitle="Phone number"
      pageText="Select a number of your choice and upgrade your travel experience with a local number "
      showNumberType
    />
  );
};

export default SelectPhone;
