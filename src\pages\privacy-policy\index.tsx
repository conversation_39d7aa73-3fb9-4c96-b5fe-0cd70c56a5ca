import { useEffect, useState } from "react";
import styles from "../../styles/legals.module.scss";
import { CmsApiGet } from "../../pages/api/cms-api";
import LegalFooter from "../../components/LegalFooter/LegalFooter";
import { Helmet } from 'react-helmet-async';
import { resolveRichText } from "../../components/utils/richTextConverter";
import { t } from "i18next";
import { useTranslation } from "react-i18next";

const PrivacyPolicy = () => {
  const { i18n } = useTranslation();
  const [policy, setPolicy] = useState("");

  useEffect(() => {
    CmsApiGet(`/api/privacy-policy?locale=${i18n.language}&populate=deep`).then((response: any) => {
      setPolicy(response.data.data.attributes.richText);
    });
  }, [i18n.language]);

  return (
    <div className={styles.main}>
      <Helmet>
        <title>{t("general.orbit")}| { t('pages.privacyPolicy') }</title>
      </Helmet>
      <div className={styles.container}>
        <h1 className={styles.heading}>{ t('pages.privacyPolicy') }</h1>
        <div
          className={styles.text}
          dangerouslySetInnerHTML={{
            __html: policy ? resolveRichText(policy) : "",
          }}
          id="privacy-content"
        />
        <LegalFooter />
      </div>
    </div>
  );
};

export default PrivacyPolicy;
