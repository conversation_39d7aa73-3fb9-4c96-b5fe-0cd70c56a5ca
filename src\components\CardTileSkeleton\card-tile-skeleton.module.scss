@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  max-width: 350px;
  height: 218px;
  background: #fff;
  border: 2px solid #efeeed;
  border-radius: 20px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  line-height: 24px;
  position: relative;
  overflow: hidden;
}

.network,
.number,
.expiry,
.button,
.primary,
.toggle {
  background: $skeleton;
  overflow: hidden;
  position: relative;
  border-radius: 3px;
}

.network,
.number,
.expiry {
  height: 28px;
}

.network {
  margin-right: 10px;
  width: 60px;
  height: 33px;
}

.number {
  width: 100px;
  margin-right: 10px;
}

.expiry {
  width: 50px;
}

.button {
  width: 40px;
  height: 40px;
  margin-left: auto;
  border-radius: 1000px;
}

.cardInfo {
  display: flex;
  align-items: center;
  margin-top: auto;
}

.primary {
  width: 100px;
  height: 22px;
}

.toggle {
  width: 40px;
  height: 22px;
}

.bottomSection {
  width: calc(100% + 32px);
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 16px;
  margin: 16px -16px -16px -16px;
  background-color: #fafaf9;
}
