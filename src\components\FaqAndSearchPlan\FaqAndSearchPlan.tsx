import Faqs from "../../components/Faqs/Faqs";
import SearchBar from "../../components/SearchBar";
import { useSelector } from "react-redux";
import Button from "../../components/Button";
import { Link } from "react-router-dom";
import { useState, useEffect } from "react";
import styles from "./FaqAndSearchPlan.module.scss";
import { t } from "i18next";
import { CmsApiGet } from "../../pages/api/cms-api";
import { useTranslation } from "react-i18next";

const FaqAndSearchPlan = ({ faqOnly }: { faqOnly?: boolean }) => {
  const { t, i18n } = useTranslation();
  const { countries } = useSelector((state: any) => state);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);

  useEffect(() => {
    if (searchQuery === "") {
      clearSearch();
    } else {
      handleSearch();
    }
  }, [searchQuery]);

  const handleSearchChange = (e: any) => {
    setSearchQuery(e.target.value);
  };

  const clearSearch = () => {
    setSearchQuery("");
    setSearchResults([]);
  };

  const handleSearch = () => {
    if (searchQuery === "") {
      clearSearch();
      return;
    }
    const searchStrings = searchQuery.split(" ");
    let filteredCountries = countries.filter((country: any) =>
      searchStrings.some(
        (str: string) =>
          country.countryName.toLowerCase().includes(str.toLowerCase()) ||
          country.countryCode.toLowerCase().includes(str.toLowerCase())
      )
    );
    setSearchResults(filteredCountries);
  };

  const [content, setContent] = useState<any>(null);

  useEffect(() => {
    CmsApiGet(`/api/faqs-preview?locale=${i18n.language}&populate=deep`).then(
      (response: any) => {
        setContent(response.data.data.attributes);
      }
    );
  }, [i18n, i18n.language]);

  return (
    <div className={styles.faqAndSearchPlan}>
      {/*
        !faqOnly && (
          <div className={styles.searchSection}>
            <h2
              dangerouslySetInnerHTML={{ __html: t("slogans.stayConnected") }}
            ></h2>
            <p style={{ margin: "0px 0px 28px 0px" }}>
              {t("slogans.exploreLowCost")}
            </p>
            <SearchBar
              handleSearch={handleSearch}
              id={styles.countries_search_input}
              searchQuery={searchQuery}
              placeholder={t("placeholders.searchCountryRegion")}
              handleSearchChange={handleSearchChange}
              clearSearch={clearSearch}
              maxWidth={420}
              lightMode
            />
            {searchQuery && searchResults.length > 0 && (
              <div className={styles.searchResults}>
                {searchResults.length > 0 &&
                  searchResults.map((country: any, index: number) => {
                    return (
                      <Link to={`/plans/country/${country.countryCode}`}>
                        <div className="flex items-center" key={index}>
                          <img src={country.iconUrl} />{" "}
                          <span>{country.countryName}</span>
                        </div>
                      </Link>
                    );
                  })}
              </div>
            )}
          </div>
        )
      */}
      <section className={`${styles.faqSection} max-width-container`}>
        <div>
          <h1 className={styles.faqHeading}>{content?.title}</h1>
          <p className={styles.faqText}>{content?.subtitle}</p>
        </div>
        <Faqs favourite />
        <Link to={content?.link?.url}>
          <Button>{content?.link?.label}</Button>
        </Link>
      </section>
    </div>
  );
};

export default FaqAndSearchPlan;
