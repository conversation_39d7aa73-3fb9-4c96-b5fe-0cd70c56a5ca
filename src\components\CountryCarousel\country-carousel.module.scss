@use "../../styles/theme.scss" as *;

.container {
  margin-bottom: 100px;
  overflow: hidden;
  @media (max-width: 768px) {
    margin-bottom: 50px;
  }
  .heading {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 50px;
    margin-bottom: 24px;
    @media (max-width: 768px) {
      padding: 0 24px;
    }
    h3 {
      font-weight: 700;
      font-size: 24px;
      line-height: 36px;
      color: $dark-dark-purple;
      margin: 0;
    }
  }
  .swiperButtons {
    display: flex;
    align-items: center;
    button {
      padding: 0;
      background: none;
      border: none;
      svg {
        vertical-align: middle;
      }
    }
  }
}
