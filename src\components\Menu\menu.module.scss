@use "../../styles/theme.scss" as *;

.box {
  height: 50px;
  position: relative;
  z-index: 1000;
}
.szh-menu {
  top: 0 !important;
}
.menu {
  button {
    background: #9be5e4 !important;
    color: #0f133a !important;
  }
  ul {
    padding: 16px !important;
  }
  span {
    svg {
      position: relative;
      inset-inline-end: -12px;
      width: 17px;
    }
  }
}

.menuItem {
  padding: 0;
  display: flex;
  align-items: center;
  transition: all 0.1s ease;
  font-size: 14px;
  padding: 8px 12px !important;
  border-radius: 12px;
  margin-bottom: 2px;
  svg {
    margin-inline-end: 8px;
    color: #088bdd;
    width: 24px;
    height: 24px;
  }
  &:hover {
    background: #efeeed;
  }
}
