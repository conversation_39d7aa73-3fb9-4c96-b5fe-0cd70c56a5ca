import Shimmer from "../Shimmer";
import styles from "./blog-skeleton.module.scss";

const BlogSkeleton = () => {
  return (
    <div className={styles.container}>
      <div className={styles.image}>
        <Shimmer />
      </div>
      <div className={styles.textContainer}>
        <div style={{ width: "20%" }} className={styles.text}>
          <Shimmer />
        </div>
        <div style={{ width: "50%" }} className={styles.text}>
          <Shimmer />
        </div>
        <div style={{ width: "30%" }} className={styles.text}>
          <Shimmer />
        </div>
      </div>
      <div className={styles.textContainer}>
        <div style={{ width: "60%" }} className={styles.text}>
          <Shimmer />
        </div>
      </div>
    </div>
  );
};

export default BlogSkeleton;
