@use "../../styles/theme.scss" as *;

.label {
  font-size: 14px;
  line-height: 21px;
  color: $dark-dark-purple;
  margin-bottom: 4px;
}

.heading {
  width: 100%;
  text-align: center;
  font-size: 30px;
  font-weight: 800;
  line-height: 38px;
  letter-spacing: -0.025em;
  color: $secondary;
  margin: 0;
}

.expand {
  display: flex;
  align-items: center;
}

.box {
  height: 100%;
}

.countryDisplay {
  display: grid;
  grid-template-columns: 20px 1fr;
  grid-column-gap: 8px;
  align-items: center;
  background-color: #fafaf9;
  padding: 16px;
  background-color: #fafaf9;
  border-radius: 12px;
  font-size: 16px;
  line-height: 24px;
  color: $secondary;
  .flag {
    width: 20px;
    height: 20px;
    border-radius: 100px;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
  }
}

.modalMain {
  padding: 32px;
}

.itemsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  margin-top: 22px;
  @media (max-width: 1025px) {
    grid-template-columns: 1fr 1fr 1fr;
  }
  @media (max-width: 550px) {
    grid-template-columns: 1fr 1fr;
  }
  @media (max-width: 470px) {
    grid-template-columns: 1fr;
  }
}

.searchContainer {
  margin-bottom: 16px;
}
