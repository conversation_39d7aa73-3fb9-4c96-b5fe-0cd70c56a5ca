@use "../../styles/theme.scss" as *;

.videoSection {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    90deg,
    rgba(22, 11, 42, 1) 0%,
    rgba(22, 11, 42, 0.8) 50%,
    rgba(22, 11, 42, 1) 100%
  );
  .videoPlayer {
    max-height: calc(100vh - 150px);
    width: 100%;
    height: auto;
  }
  .pauseButton {
    display: none;
    &:hover {
      display: block;
      @media (pointer: none), (pointer: coarse) {
        display: none;
      }
    }
  }
  .playButton,
  .pauseButton {
    position: absolute;
    background: none;
    border: none;
    color: $secondary;
    cursor: pointer;
    z-index: 1000;
    @media (pointer: none), (pointer: coarse) {
      display: none;
    }
  }
  .playButton {
    svg {
      vertical-align: middle;
      width: 100px;
    }
  }
  .pauseButton {
    svg {
      vertical-align: middle;
      width: 80px;
    }
  }
  video {
    height: 100%;
    transition: opacity 0.3s ease;
    &:hover {
      & ~ .pauseButton {
        display: block;
        @media (pointer: none), (pointer: coarse) {
          display: none;
        }
      }
    }
  }
}
