@use "../../styles/theme.scss" as *;

.input {
  height: 47px;
  width: 100%;
  border-radius: 25px;
  border: 1px solid $input-grey;
  padding-inline-start: 16px;
  padding-inline-end: 32px;
  transition: border 0.1s ease;
  font-size: 14px;
  line-height: 24px;
  color: $dark-dark-purple;
  &.noClear {
    padding-inline-end: 14px;
    padding-inline-start: 43px;
  }
  &::placeholder {
    color: rgba(22, 11, 42, 0.6);
  }
  &:hover {
    border: 1px solid #d6d6d6 !important;
  }
  &:focus {
    border: 2px solid #088bdd !important;
    caret-color: #088bdd;
    outline: none;
    & ~ .errorIcon {
      display: none;
    }
    & ~ .clearIcon {
      display: flex;
    }
  }
  &.error {
    border: 2px solid $error !important;
  }
  &:disabled {
    border: 1px solid $disabled;
    opacity: 0.5;
    * {
      opacity: 0.38;
    }
  }
}

.password {
  padding-inline-end: 60px;
}

.inputContainer {
  width: 100%;
  margin-bottom: 16px;
}

.inputWrapper {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  label {
    font-size: 14px;
    color: #667085;
    margin-bottom: 5px;
  }
  button {
    padding-right: 0;
  }
}

.eyeIcon {
  display: flex;
  align-items: center;
}

.eyeIcon,
.clearIcon {
  position: absolute;
  right: 15px;
  bottom: 12px;
  svg {
    width: 20px;
    height: 20px;
  }

  &:dir(rtl) {
    right: auto;
    left: 15px;
  }
}

.clearIcon,
.eyeIcon {
  cursor: pointer;
}

.clearIcon {
  display: none;
  align-items: center;
  padding: 0;
  color: $secondary;
  inset-inline-end: 12px;

  svg {
    vertical-align: middle;
  }

  &.isPasswordInput {
    inset-inline-end: 38px;
  }
}

.errorText {
  margin: 8px 0px 0px 0px;
  font-size: 14px;
  color: $error-text;
  line-height: 20px;
  text-align: start;
  display: flex;
  align-items: flex-start;

  .errorIcon {
    margin-inline-end: 4px;
  }
}
