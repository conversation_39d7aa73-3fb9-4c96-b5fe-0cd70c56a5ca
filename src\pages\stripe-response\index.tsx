import { useEffect } from "react";
import { ApiPostAuth } from "../api/api";
import { useNavigate, useSearchParams } from "react-router-dom";
import { CircularProgress } from "@mui/material";
import PaymentLoading from "../../components/PaymentLoading";

const StripeResponse = () => {
  const navigate = useNavigate();

  const [searchParams, setSearchParams] = useSearchParams();

  const handleError = () => {
    const location = localStorage.getItem("stripeLocation");
    localStorage.removeItem("sessionId");
    if (location === "checkout" || location === "processPayment") {
      navigate("/checkout?failure=true");
    } else if (location === "wallet") {
      navigate("/dashboard/wallet?failure=true");
    }
  };

  useEffect(() => {
    const sessionId = localStorage.getItem("sessionId");
    const location = localStorage.getItem("stripeLocation");
    const success = searchParams.get("success");
    if (sessionId && location) {
      if (success) {
        if (location === "processPayment") {
          navigate("/process-payment?payment_type=card");
        } else {
          ApiPostAuth("/payments/cards", {
            sessionId: sessionId,
          })
            .then((response) => {
              localStorage.removeItem("sessionId");
              if (location === "checkout") {
                navigate("/checkout?success=true");
              } else if (location === "wallet") {
                navigate("/dashboard/wallet?success=true");
              }
            })
            .catch(handleError);
        }
      } else {
        handleError();
      }
    }
  }, []);

  return <PaymentLoading loading />;
};

export default StripeResponse;
