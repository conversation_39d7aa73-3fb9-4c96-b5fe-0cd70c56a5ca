import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import Button from "../../components/Button";
import PlanSummary from "../../components/PlanSummary";
import {
  <PERSON>ert,
  ArrowBack,
  Globe,
  Student,
  Suitcase,
  Trash,
  Umbrella,
} from "../../components/svgs";
import styles from "../../styles/cart-summary.module.scss";
import ConfirmationModal from "../../components/ConfirmationModal";
import EsimCompatibleModal from "../../components/EsimCompatibleModal";
import { formatPrice } from "../../components/utils/formatPrice";
import { getCurrencySymbol } from "../../components/utils/getCurrencySymbol";
import Tooltip from "../../components/Tooltip";
import LowBalanceModal from "../../components/LowBalanceModal";
import { ApiPostAuth } from "../api/api";
import PaymentLoading from "../../components/PaymentLoading";
import TopupLimitModal from "../../components/TopupLimitModal";
import { t } from "i18next";

const CartSummary = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [activeUse, setActiveUse] = useState(0);

  const { basket, loggedIn, userInfo, creditSelection } = useSelector(
    (state: any) => state
  );

  const getTotal = () => {
    let total = 0;
    basket.forEach((item: any) => (total += item.prices[0].cost));
    return formatPrice(total);
  };

  const [deleteAllModal, setDeleteAllModal] = useState(false);
  const [confirmEsimModal, setConfirmEsimModal] = useState(false);

  const emptyBasket = () => {
    dispatch({
      type: "set",
      basket: null,
    });
    localStorage.removeItem("basket");
  };

  const [loading, setLoading] = useState(false);

  // Create plan if payment successful
  const createProductPlan = () => {
    let planData = {
      id: basket.planId,
      currencyCode: basket.prices[0].currencyCode,
      planType: basket.planType,
      automaticPayment: basket.autoRenew,
      didType: basket.didType,
      cityId: basket.cityId,
    } as any;

    const paymentData = {
      defaultPayment: false,
      paymentType: "credit",
    };

    const overallData = {
      plans: [planData],
      payment: paymentData,
    };

    setLoading(true);

    if (basket.isRenew) {
      ApiPostAuth("/subscriptions/renew/manual", {
        subscriptionId: basket.subscriptionId.toString(),
        payment: paymentData,
      })
        .then((response) => {
          dispatch({
            type: "set",
            failedPlans: [],
            basket: null,
            creditSelection: null,
          });
          localStorage.removeItem("basket");
          localStorage.removeItem("creditSelection");
          if (response.data.failedPlans.length > 0) {
            dispatch({
              type: "set",
              failedPlans: response.data.failedPlans,
            });
          }
          // If success navigate to success page
          setLoading(false);
          navigate("/payment-success");
        })
        .catch((error) => {
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: error.response.data.message,
            },
          });
        });
    } else {
      ApiPostAuth("/plans/create", overallData)
        .then((response) => {
          dispatch({
            type: "set",
            failedPlans: [],
            basket: null,
            creditSelection: null,
          });
          localStorage.removeItem("basket");
          localStorage.removeItem("creditSelection");
          if (response.data.failedPlans.length > 0) {
            dispatch({
              type: "set",
              failedPlans: response.data.failedPlans,
            });
          }
          // If success navigate to success page
          setLoading(false);
          navigate("/payment-success");
        })
        .catch((error) => {
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: error.response.data.message,
            },
          });
        });
    }
  };

  const goToCheckout = () => {
    if (basket.prices[0].cost > parseFloat(userInfo.credit)) {
      setShowLowBalance(true);
    } else if (basket.dataAllowance > 0) {
      setConfirmEsimModal(true);
    } else {
      routeUser();
    }
  };

  const routeUser = () => {
    if (loggedIn) {
      if (basket.prices[0].cost <= parseFloat(userInfo.credit)) {
        createProductPlan();
      } else {
        navigate("/checkout");
      }
    } else {
      navigate("/register");
    }
  };

  const handleAutoRenewChange = (planToChange: any, isAutoRenew: boolean) => {
    let basketCopy = { ...basket };
    basketCopy.autoRenew = isAutoRenew;
    dispatch({
      type: "set",
      basket: basketCopy,
    });
    localStorage.setItem("basket", JSON.stringify(basketCopy));
  };

  const [showLowBalance, setShowLowBalance] = useState(false);

  const [showDailyLimit, setShowDailyLimit] = useState(false);
  const [showWeeklyLimit, setShowWeeklyLimit] = useState(false);

  const addFunds = () => {
    if (userInfo) {
      if (userInfo?.topUpLimit?.dailyLimitExceeded) {
        setShowDailyLimit(true);
      } else if (userInfo?.topUpLimit.weeklyLimitExceeded) {
        setShowWeeklyLimit(true);
      } else {
        navigate("/shop/add-credit");
      }
    }
  };

  return (
    <div className={styles.container}>
      <TopupLimitModal
        show={showDailyLimit}
        setShow={setShowDailyLimit}
        type="daily"
      />
      <TopupLimitModal
        show={showWeeklyLimit}
        setShow={setShowWeeklyLimit}
        type="weekly"
      />
      <PaymentLoading loading={loading} />
      <div className={styles.main}>
        <EsimCompatibleModal
          show={confirmEsimModal}
          setShow={setConfirmEsimModal}
          proceed={routeUser}
        />
        <ConfirmationModal
          show={deleteAllModal}
          setShow={setDeleteAllModal}
          heading="Oh no, are you sure?"
          text="You're about to remove everything in your basket. You'll have to shop again if you wish to get these items back."
          continueButton="Yes, empty my cart"
          proceed={emptyBasket}
        />
        <LowBalanceModal show={showLowBalance} setShow={setShowLowBalance} />
        {!basket ? (
          <div className={styles.emptyCart}>
            <div style={{ width: "100%", maxWidth: 266 }}>
              <div className={styles.imageContainer}>
                <div className={styles.stickyContainer}>
                  <img src="/images-int/shop/trolley_empty.png" />
                </div>
              </div>
            </div>
            <h4>Your cart is empty</h4>
            <p>
              Items added to your cart will appear here. Start shopping now!
            </p>
            <Link style={{ textDecoration: "none" }} to="/shop">
              <Button style={{ width: 182 }}>Go to Shop</Button>
            </Link>
          </div>
        ) : (
          <div className={styles.cartSummary}>
            <h3>Summary</h3>
            <div className={styles.balanceContainer}>
              <div>
                <div className={styles.balanceLabel}>Balance</div>
                <div className={styles.balanceAmount}>
                  {getCurrencySymbol(userInfo.currency)}
                  {userInfo.credit
                    ? formatPrice(parseFloat(userInfo.credit))
                    : "0.00"}
                </div>
              </div>
              <Button
                onClick={addFunds}
                style={{ height: 43, padding: "0 16px", fontSize: 12 }}
              >
                Add funds
              </Button>
            </div>
            <div className={styles.plansContainer}>
              <PlanSummary
                key={`basket-item-${basket?.basketId}`}
                planObj={basket}
                handleAutoRenewChange={handleAutoRenewChange}
              />
            </div>
            <div className={styles.useTitle}>Use of plan</div>
            <div className={styles.useContainer}>
              <div
                className={`${styles.singleUse} ${
                  activeUse === 1 && styles.active
                }`}
                onClick={() => {
                  setActiveUse(1);
                }}
              >
                <Suitcase /> Business
              </div>
              <div
                className={`${styles.singleUse} ${
                  activeUse === 2 && styles.active
                }`}
                onClick={() => {
                  setActiveUse(2);
                }}
              >
                <Globe /> Travel
              </div>
              <div
                className={`${styles.singleUse} ${
                  activeUse === 3 && styles.active
                }`}
                onClick={() => {
                  setActiveUse(3);
                }}
              >
                <Umbrella /> Leisure
              </div>
            </div>
            <div className={styles.useTitle}>Payment Method</div>
            <div className={styles.paymentMethod}>Gist Wallet</div>
          </div>
        )}
      </div>
      {basket && (
        <div className={styles.bottomSection}>
          <div className={styles.left}>
            <Alert />
            <p className={styles.message}>
              To avoid an error message, make sure that your device is not tied
              to a specific network provider and that your phone is{" "}
              <a
                target="_blank"
                href="https://gistmobile.com/esim-compatibility/"
                style={{ color: "#000" }}
              >
                eSIM-compatible
              </a>
            </p>
          </div>
          <div className={styles.right}>
            <div className={styles.price}>
              Total {basket?.prices[0].currencySymbol}
              {formatPrice(basket?.prices[0].cost)}
            </div>
            <Tooltip show={!activeUse} text="Please select a use of plan">
              <Button
                disabled={!activeUse}
                style={{ height: 56, padding: "0 26px" }}
                onClick={goToCheckout}
              >
                { t('general.continue') }
              </Button>
            </Tooltip>
          </div>
        </div>
      )}
    </div>
  );
};

export default CartSummary;
