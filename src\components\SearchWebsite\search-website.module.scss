@use "../../styles/theme.scss" as *;

.container {
  position: fixed;
  height: 100vh;
  width: 100%;
  background: #fff;
  z-index: 10000;
  left: 0;
  top: 0;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  overflow-y: scroll;
  &.showContainer {
    pointer-events: all;
    opacity: 1;
  }
}

.topSection {
  position: sticky;
  left: 0;
  top: 0;
  height: 100px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 50px;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.2);
  transition: border-bottom 0.3s ease;
  background: #fff;
  z-index: 1000;
  @media (max-width: 768px) {
    padding: 0 24px;
    position: relative;
  }
  @media (max-width: 550px) {
    height: auto;
    padding: 24px;
  }
}

.loadingBox {
  position: relative;
  overflow: hidden;
  height: 24px;
  width: 300px;
  background: $skeleton;
  border-radius: 5px;
  margin-bottom: 38px;
}

.inputContainer {
  display: flex;
  align-items: center;
  width: 100%;
  padding-right: 8px;
  height: 100%;
  svg {
    vertical-align: middle;
  }
  input {
    border: none;
    background: none;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    caret-color: $secondary;
    color: $dark-dark-purple;
    width: 100%;
    margin-left: 30px;
    height: 100%;
    &::placeholder {
      color: $placeholder;
    }
    &:focus {
      outline: none;
    }
  }
  .clearButton {
    background: none;
    border: none;
    padding: 0;
    color: $secondary;
    cursor: pointer;
  }
}

.buttons {
  display: flex;
  align-items: center;
  margin-left: auto;
  @media (max-width: 550px) {
    display: none;
  }
}

.mobileButtons {
  display: none;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 24px;
  button {
    width: 100%;
  }
  @media (max-width: 550px) {
    display: flex;
  }
}

.resultsContainer {
  width: 100%;
  height: 100%;
  position: relative;
}

.topSearches {
  padding: 50px;
  @media (max-width: 768px) {
    padding: 24px;
  }
  h3 {
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    color: $dark-dark-purple;
    margin-bottom: 33px;
  }
  a {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    display: block;
    margin-bottom: 38px;
    text-decoration: none;
    color: $grey;
  }
}

.results {
  padding: 32px 164px;
  display: flex;
  flex-direction: column;
  align-items: center;
  @media (max-width: 768px) {
    padding: 24px;
  }
  .sections {
    display: flex;
  }
  .sectionButton {
    width: 100px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    line-height: 24px;
    color: #000;
    border-radius: 1000px;
    margin-right: 11px;
    cursor: pointer;
    position: relative;
    @media (max-width: 768px) {
      width: auto;
      padding: 0 24px;
      font-size: 12px;
      height: 40px;
      margin-right: 0px;
    }
    &:last-child {
      margin-right: 0;
    }
    span {
      position: relative;
      z-index: 10;
    }
    .highlight {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: $light-primary;
      z-index: 5;
      border-radius: 1000px;
    }
    &:hover {
      background: #eff1f7;
    }
    &.active {
      font-weight: 600;
      &:hover {
        background: none;
      }
    }
  }
}

.numberOfResults {
  margin: 32px 0;
  font-size: 16px;
  line-height: 24px;
}

.noResults {
  .suggestions {
    font-size: 16px;
    line-height: 24px;
    .text {
      margin-bottom: 25px;
    }
    ul {
      padding-inline-start: 30px;
    }
  }
}

.noResultImage {
  position: absolute;
  right: 0;
  top: 32px;
  height: calc(100vh - 132px);
  @media (max-width: 1220px) {
    top: 176px;
  }
  @media (max-width: 786px) {
    top: 350px;
  }
}
