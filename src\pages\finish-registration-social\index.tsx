import { useDispatch } from "react-redux";
import styles from "../../styles/register.module.scss";
import RegSuccessModal from "../../components/RegSuccessModal";
import { useState } from "react";
import Button from "../../components/Button";
import PhoneInput from "../../components/PhoneInput";
import {
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
} from "../../components/utils/InputHandlers";
import auth from "../../components/utils/firebaseAuth";
import { ApiPost } from "../api/api";
import { Helmet } from 'react-helmet-async';
import { useSearchParams } from "react-router-dom";
import { validateAll } from "indicative/validator";
import { GoogleAuthProvider, signInWithCredential } from 'firebase/auth'
import useHandleRegistrationSuccess from "../../hooks/useHandleRegistrationSuccess";
import { t } from "i18next";

const fields = ["phoneNumber", "countryCode"];
const rules = getRules(fields);
const messages = getMessages(fields);

const FinishRegistrationSocial = () => {
  const dispatch = useDispatch();
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [data, setData] = useState(createStateObject(fields));
  const [loading, setLoading] = useState(false);

  const [searchParams] = useSearchParams();
  const {
    type,
    token,
    email,
    googleToken,
    firstName,
    lastName,
    tacAccepted,
    fbAccessToken,
    fbUserId,
  } = Object.fromEntries([...searchParams]);

  const { handleRegistrationSuccess } = useHandleRegistrationSuccess();

  const handleGoogleSignIn = async () => {
      try {
        const credential = GoogleAuthProvider.credential(googleToken);
        const result = await signInWithCredential(auth, credential);
        const user = result.user;
      } catch (error) {
        console.error('Error signing in with Google: ', error);
      }
  };
  const handleRegistrationByType = async () => {
    if (type === "google") {
      return completeGoogleRegister({
        token: googleToken,
        firstName,
        lastName,
        tacAccepted,
        phoneNumber: data.phoneNumber,
      });
    } else if (type === "facebook") {
      return completeFacebookRegister({
        accessToken: fbAccessToken,
        firstName,
        lastName,
        tacAccepted,
        phoneNumber: data.phoneNumber,
        userId: fbUserId,
      });
    } else if (type === "apple") {
      return completeAppleRegister({
        token,
        firstName,
        email,
        lastName,
        tacAccepted,
        phoneNumber: data.phoneNumber,
      });
    } else {
      throw { response: { data: { message: "Invalid social type" } } };
    }
  };

  const submit = () => {
    validateAll(data, rules, messages)
      .then(async () => {
        setLoading(true);
        handleRegistrationByType()
          .then((response: any) => {
            handleRegistrationSuccess(response);
            setLoading(false);
            setIsSuccessModalOpen(true);
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                heading: "Something went wrong!",
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const completeAppleRegister = async (registrationData: any) => {
    return ApiPost("/users/apple/register", registrationData);
  };

  const completeFacebookRegister = async (registrationData: any) => {
    return ApiPost("/users/facebook/register", registrationData);
  };

  const completeGoogleRegister = async (registrationData: any) => {
    await handleGoogleSignIn()
    return ApiPost("/users/google/register", registrationData);
  };

  return (
    <div className={styles.finishReg}>
      <Helmet>
        <title>{t("general.orbit")}| { t('account.verifyMsg') }</title>
        <meta name="description" content={ t('account.verifyMsg') } />
      </Helmet>
      <RegSuccessModal show={isSuccessModalOpen} />
      <img src="/images-int/help/help_activating.svg" />
      <h2>{ t('account.mailVerified') }</h2>
      <p className={styles.text}>
        { t('account.verifyMsg') }
      </p>

      <div className={styles.inputs}>
        <PhoneInput
          state={data}
          setState={setData}
          onKeyDown={() => {}}
          disabled={loading}
        />
        <Button
          style={{ width: "100%", height: 48 }}
          onClick={submit}
          loading={loading}
        >
          { t('buttons.finish') }
        </Button>
      </div>
    </div>
  );
};

export default FinishRegistrationSocial;
