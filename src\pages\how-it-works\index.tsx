import styles from "../../styles/how-it-works.module.scss";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Slider from "react-slick";
import Button from "../../components/Button";
import { ArrowBack, ArrowNext } from "../../components/svgs";
import HowItWorksModal from "../../components/HowItWorksModal";
import { useState, useEffect } from "react";
import { HashLink } from "react-router-hash-link";
import { useTranslation } from "react-i18next";
import { Helmet } from 'react-helmet-async';
import { cmsURL, CmsApiGet } from "../api/cms-api";
import { organizationSchema } from "../../components/utils/schemaMarkups";

const HowItWorks = () => {
  const { t, i18n } = useTranslation();
  const [osType, setOsType] = useState<"ios" | "android">("ios");
  const [showModal, setShowModal] = useState(false);
  const [data, setData] = useState<any>(null);

  const NextEvent = ({ onClick }: any) => (
    <Button style={{ background: "transparent" }} onClick={onClick}>
      <ArrowNext />
    </Button>
  );
  const BackEvent = ({ onClick }: any) => (
    <Button style={{ background: "transparent" }} onClick={onClick}>
      <ArrowBack />
    </Button>
  );

  useEffect(() => {
    CmsApiGet(`/api/how-it-work?locale=${i18n.language}&populate=deep`).then(
      (response: any) => {
        setData(response.data.data.attributes);
      }
    );
  }, [i18n, i18n.language]);

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    rtl: i18n.language === 'ar',
    nextArrow: <NextEvent />,
    prevArrow: <BackEvent />,
  };

  const handleModal = (type: "ios" | "android") => {
    setOsType(type);
    setShowModal(true);
  };

  return (
    <div className={styles.container}>
      {
        data && (
          <Helmet>
            <title>{t("general.orbit")}| {data?.metaTitle}</title>
            <script type="application/ld+json">
              {JSON.stringify(organizationSchema(data))}
            </script>
            <meta name="description" content={data?.metaDescription} />
          </Helmet>
        )
      }
      <div className={styles.howItWorks + " howItWorks max-width-container"}>
        <HowItWorksModal
          show={showModal}
          type={osType}
          setShow={(val: boolean) => setShowModal(val)} />
        <Slider {...settings}>
          {data?.slide.map((item: any, index: number) => (
            <div className={styles.slide} key={index} dir={i18n.dir()}>
              <div className="flex items-center">
                <div className={styles.rightElement}>
                  <h2>{item.title}</h2>
                  {item.subtitle && <h2>{item.subtitle}</h2>}
                  <p>{item.text}</p>
                  {item.extra_text && (
                    <p className={styles.bgHowItWorks}>{item.extra_text}</p>
                  )}
                  {item.IosAndriodHelpButtons && (
                    <div className="flex">
                      <Button
                        style={{
                          background: "#8AD6D6",
                          color: "#0F133A",
                          marginInlineEnd: 15,
                        }}
                        onClick={() => handleModal("ios")}>
                        IOS
                      </Button>
                      <Button
                        style={{ background: "#8AD6D6", color: "#0F133A" }}
                        onClick={() => handleModal("android")}>
                        Android
                      </Button>
                    </div>
                  )}
                  {item.ctaText && item.ctaLink && (
                    <Button
                      style={{
                        background: "#8AD6D6",
                        color: "#0F133A",
                        marginBottom: 20,
                      }}>
                      <HashLink to={item.ctaLink}>{item.ctaText}</HashLink>
                    </Button>
                  )}
                  {item.AddTroubleshootSection && (
                    <div className={styles.small + " flex items-center"}>
                      <img
                        src="/images-int/how-it-works/how_it_works_4.png"
                        alt="Orbit Mobile - Troubleshooting eSIM data plans" />
                      <div>
                        <h5>{ i18n.language === 'en' ? t("howItWorks.troubleFree") : t("howItWorks.moneyBackGuarantee")}</h5>
                        <p>{ i18n.language === 'en' ? t("howItWorks.moneyBackGuarantee")  : t("howItWorks.troubleFree") }</p>
                      </div>
                    </div>
                  )}
                </div>
                <div className={styles.leftElement}>
                  <img
                    src={cmsURL + item.image.data.attributes.url}
                    alt={`Orbit Mobile ESIM - ${item.title} ${item.subtitle}`} />
                </div>
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </div>
  );
};

export default HowItWorks;
