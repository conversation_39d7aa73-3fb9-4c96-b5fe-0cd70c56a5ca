import { useDispatch } from "react-redux";
import { ApiPostAuth } from "../../pages/api/api";
import { Trash } from "../svgs";
import { getCardNetworkImage } from "../utils/getCardNetworkImage";
import styles from "./saved-card.module.scss";
import { ToggleGlobal } from "../ToggleGlobal/ToggleGlobal";
import { checkExpiry } from "../utils/checkExpiry";
import { useEffect, useState } from "react";
import { t } from "i18next";

const SavedCard = ({
  card,
  getCards,
  setCardsLoading,
  handleDelete,
  displayOnly,
  handleEdit,
}: any) => {
  const dispatch = useDispatch();
  const [cardStatus, setCardStatus] = useState<"" | "expired" | "soon">("");

  useEffect(() => {
    let expiryStatus = checkExpiry(card.expiry);
    if (expiryStatus.status === "soon") {
      setCardStatus("soon");
    }
    if (expiryStatus.status === "expired") {
      setCardStatus("expired");
    }
  }, []);

  const setAsPrimary = () => {
    if (!card.primaryCard) {
      setCardsLoading(true);
      ApiPostAuth("/payments/cards/setdefault", {
        cardId: card.id,
        defaultPayment: true,
      })
        .then((response) => {
          getCards(() => {
            dispatch({
              type: "notify",
              payload: {
                error: false,
                heading: t('account.msgs.success'),
                message: response.data.message,
              },
            });
          });
        })
        .catch((error) => {
          setCardsLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              heading: t("buttons.oops"),
              message: error.response.data.message,
            },
          });
        });
    }
  };

  return (
    <div className={styles.main}>
      {card.primaryCard && (
        <div className={styles.primary}>
          <span>
          {t("account.paymentCards.primary")}
          </span>
        </div>
      )}
      {cardStatus && !card.primaryCard && (
        <div
          className={styles.cardStatus}
          style={{
            background: cardStatus === "expired" ? "#F8D4D3" : "#FFDD99",
          }}
        >
          <img src="/images-int/warning.svg" />{" "}
          {cardStatus === "expired"
            ? t("account.paymentCards.expired")
            : t("account.paymentCards.expiredSoon")}
        </div>
      )}
      <div className={styles.trashButton} onClick={() => handleDelete(card)}>
        <Trash />
      </div>
      {cardStatus && card.primaryCard && (
        <div
          className={styles.cardStatusPrimary}
          style={{
            background: cardStatus === "expired" ? "#F8D4D3" : "#FFDD99",
          }}
        >
          <img src="/images-int/warning.svg" />{" "}
          {cardStatus === "expired"
            ? t("account.paymentCards.expired")
            : t("account.paymentCards.expiredSoon")}
        </div>
      )}
      <div className={styles.cardInfo}>
        {getCardNetworkImage(card)}
        <div style={{ marginInlineStart: 10 }}>
          <span>••••</span><span> {card.last4Digits}</span><span>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;</span>
          {card.expiry}
        </div>
      </div>
      <div className={styles.action}>
        <div>
          {t("account.paymentCards.primaryMethod")}
          <div className="flex" style={{ alignItems: "center" }}>
            <span style={{ marginInlineEnd: "6px" }}>
              {card.primaryCard ? t("buttons.on") : t("buttons.off")}
            </span>{" "}
            <ToggleGlobal on={card.primaryCard} onChange={setAsPrimary} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SavedCard;
