@use "./theme.scss" as *;

.container {
  flex-grow: 1;
  background-image: url("/images-int/register/register_success.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: stretch;
  position: relative;
}

.main {
  background: linear-gradient(
    180deg,
    rgba(22, 11, 42, 0.48) 0%,
    rgba(22, 11, 42, 0.36) 50%,
    rgba(22, 11, 42, 0.48) 100%
  );
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 186px;
  @media (max-width: 768px) {
    padding-top: 60px;
  }
  .text {
    padding: 0 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff;
    margin-bottom: 84px;
    margin-top: auto;
    @media (max-width: 768px) {
      padding: 0 24px;
    }
    h2 {
      margin: 0 0 12px 0;
      font-weight: 700;
      font-size: 40px;
      line-height: 60px;
      text-align: center;
      @media (max-width: 768px) {
        font-size: 30px;
        line-height: 45px;
      }
    }
    p {
      margin: 0 0 50px;
      font-size: 20px;
      line-height: 30px;
      text-align: center;
      @media (max-width: 768px) {
        font-size: 16px;
        line-height: 24px;
      }
    }
    .buttons {
      display: flex;
      @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
        width: 100%;
        a {
          width: 100%;
          margin-bottom: 16px;
        }
        button {
          width: 100%;
        }
      }
    }
  }
  .appBar {
    width: 100%;
    background: rgba(22, 11, 42, 0.6);
    margin-top: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 33px 164px;
    @media (max-width: 1000px) {
      padding: 33px 24px;
    }
    @media (max-width: 768px) {
      padding: 24px;
      flex-direction: column;
      align-items: flex-start;
    }
    .appText {
      color: #eff1f7;
      margin-right: 16px;
      @media (max-width: 768px) {
        margin: 0 0 16px 0;
      }
      h5 {
        margin: 0 0 8px 0;
        font-weight: 700;
        font-size: 20px;
        line-height: 30px;
      }
      p {
        margin: 0;
        font-size: 16px;
        line-height: 24px;
      }
    }
  }
}

.backButton {
  padding: 0;
  background: none;
  border: none;
  cursor: pointer;
  vertical-align: middle;
  position: absolute;
  top: 27px;
  left: 55px;
  color: #fff;
  @media (min-width: 1700px) {
    left: calc((100vw - 1600px) / 2);
  }
  @media (max-width: 768px) {
    left: 24px;
    top: 24px;
  }
  svg {
    vertical-align: middle;
  }
}

.continuePurchase {
  background: #eff1f7;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 734px;
  padding: 24px;
  margin-bottom: 40px;
  .buttons {
    display: flex;
  }
}
