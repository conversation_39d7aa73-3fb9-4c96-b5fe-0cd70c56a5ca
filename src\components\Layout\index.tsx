import styles from "./layout.module.scss";
import Header from "../Header";
import { AnimatePresence } from "framer-motion";
import Footer from "../Footer";
import Notification from "../Notification";
import { useSelector } from "react-redux";
import DataLoader from "../utils/DataLoader";
import { useLocation } from "react-router-dom";
import { useEffect, useState } from "react";
import $ from "jquery";
import LegalPopUp from "../LegalPopUp";
import CookiePopUp from "../CookiePopUp";

const Layout = ({ children }: any) => {
  const { notifications, isBannerOpen, loggedIn } = useSelector(
    (state: any) => state
  );
  const location = useLocation();

  useEffect(() => {
    $(document.body).css("overflow-y", "scroll");
  }, [location]);

  const [showCookieMessage, setShowCookieMessage] = useState(false);
  const [showTermsMessage, setShowTermsMessage] = useState(false);
  const [showPrivacyMessage, setShowPrivacyMessage] = useState(false);
  const [consentMode, setConsentMode] = useState({
    ad_storage: "denied",
    ad_personalization: "denied",
    ad_user_data: "denied",
    analytics_storage: "denied",
  });

  useEffect(() => {
    let getConsetMode = localStorage.getItem("consentMode");
    if (getConsetMode) {
      setConsentMode(JSON.parse(getConsetMode));
    } else {
      setConsentMode({
        ad_storage: "denied",
        ad_personalization: "denied",
        ad_user_data: "denied",
        analytics_storage: "denied",
      });
    }
  }, []);

  useEffect(() => {
    let getConsetMode = localStorage.getItem("consentMode");
    if (
      (getConsetMode && JSON.parse(getConsetMode).ad_storage === "denied") ||
      !getConsetMode
    ) {
      setShowCookieMessage(true);
    }
    /*
      if ((getConsetMode && JSON.parse(getConsetMode).ad_user_data === 'denied' && loggedIn) || (!getConsetMode && loggedIn)) {
      setShowTermsMessage(true)
    }
    if ((getConsetMode && JSON.parse(getConsetMode).ad_personalization === 'denied' && loggedIn) || (!getConsetMode && loggedIn)) {
      setShowPrivacyMessage(true)
    }
      */
  }, [consentMode]);

  const acceptCookies = () => {
    let updateConsent = {
      event: "consent_update",
      ad_storage: "granted",
      ad_personalization: "granted",
      ad_user_data: "granted",
      analytics_storage: "granted",
    };
    localStorage.setItem("consentMode", JSON.stringify(updateConsent));
    window.dataLayer.push("consent", "update", updateConsent);
    setShowCookieMessage(false);
    setConsentMode(updateConsent);
  };

  const acceptTerms = () => {
    let updateConsent = {
      ...consentMode,
      ad_user_data: "granted",
    };
    localStorage.setItem("consentMode", JSON.stringify(updateConsent));
    window.dataLayer.push("consent", "update", updateConsent);
    setShowTermsMessage(false);
    setConsentMode(updateConsent);
  };

  const acceptPrivacy = () => {
    let updateConsent = {
      ...consentMode,
      ad_personalization: "granted",
    };
    localStorage.setItem("consentMode", JSON.stringify(updateConsent));
    window.dataLayer.push("consent", "update", updateConsent);
    setShowPrivacyMessage(false);
    setConsentMode(updateConsent);
  };

  return (
    <div className={`${styles.main} ${isBannerOpen && styles.bannerOpen}`}>
      <DataLoader />
      <Header />
      <div className={styles.notificationWrapper}>
        <AnimatePresence>
          {notifications.map((notification: any) => (
            <Notification
              id={notification.id}
              key={notification.id}
              message={notification.message}
              error={notification.error}
              heading={notification.heading}
            />
          ))}
        </AnimatePresence>
      </div>
      <div className={styles.content}>{children}</div>
      <Footer />
      <div className={styles.legalPopUpContainer}>
        <AnimatePresence>
          {showCookieMessage ? (
            <CookiePopUp accept={acceptCookies} />
          ) : showTermsMessage ? (
            <LegalPopUp accept={acceptTerms} />
          ) : showPrivacyMessage ? (
            <LegalPopUp accept={acceptPrivacy} privacy />
          ) : (
            ""
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Layout;
