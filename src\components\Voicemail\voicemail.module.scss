@use "../../styles/theme.scss" as *;

.main {
  padding: 68px 50px 50px 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  @media (max-width: 768px) {
    padding: 68px 24px 50px 24px;
  }
  h5 {
    color: $primary;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    margin: 0 0 39px 0;
    text-align: center;
  }
  .toggleContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 341px;
    .toggle {
      display: flex;
      align-items: center;
    }
  }
  .buttons {
    display: flex;
    align-items: center;
    margin-top: 64px;
    @media (max-width: 420px) {
      flex-direction: column-reverse;
      width: 100%;
      button {
        width: 100%;
        margin: 0 0 12px 0 !important;
      }
    }
  }
}
