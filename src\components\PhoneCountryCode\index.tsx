import { useSelector } from "react-redux";
import { Input } from "../Input";
import styles from "./phone-country-code.module.scss";
import { countryCodeList } from "../utils/countryCodeList";
import { useEffect, useState } from "react";
import { t } from "i18next";

const PhoneCountryCode = ({
  value,
  onChange,
  error,
  onKeyDown,
  password,
  clear,
  disabled,
  id = null,
}: any) => {
  const { countries } = useSelector((state: any) => state);

  const [flagUrl, setFlagUrl] = useState(
    "https://public-trill-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/GB.png"
  );

  useEffect(() => {
    let country = countryCodeList.filter(
      (countryItem: any) => countryItem.dial_code === value
    );
    if (country.length) {
      let countryWithFlag = countries.filter(
        (countryWithFlagItem: any) =>
          countryWithFlagItem.countryCode === country[0].code
      );
      if (countryWithFlag.length) setFlagUrl(countryWithFlag[0].iconUrl);
    } else if (value[1] === "1") {
      setFlagUrl(
        "https://public-trill-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/US.png"
      );
    } else {
      setFlagUrl(
        value.length
          ? ""
          : "https://public-trill-bucket.s3.eu-north-1.amazonaws.com/flag-icons/png/GB.png"
      );
    }
  }, [value]);

  return (
    <div className={styles.container}>
      <div
        className={styles.flag}
        style={{
          backgroundImage: `url(${flagUrl})`,
        }}
      />
      <Input
        placeholder="+44"
        label={ t('account.countryCode') }
        value={value}
        onChange={onChange}
        error={error}
        onKeyDown={onKeyDown}
        password={password}
        clear={clear}
        disabled={disabled}
        id={id}
        noClear
      />
    </div>
  );
};

export default PhoneCountryCode;
