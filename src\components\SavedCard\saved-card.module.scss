@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  max-width: 350px;
  height: 218px;
  border: 2px solid #efeeed;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  color: $secondary;
  font-weight: 600;
  justify-content: end;
  font-size: 16px;
  line-height: 24px;
  position: relative;
  overflow: hidden;
  background-color: #fff;
  svg,
  img {
    width: 60px;
    height: 33px;
  }
}

.actions {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  inset-inline-start: 0;
  background: $secondary;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.action {
  display: flex;
  flex-direction: column;
  line-height: 22px;
  padding: 10px 16px;
  background: #fafaf9;
  font-size: 12px;
  font-weight: 400;
  > div {
    display: flex;
    justify-content: space-between;
  }
}

.name {
  margin-top: auto;
  margin-bottom: 12px;
}

.cardInfo {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 800;
  padding: 0px 16px;
  margin-bottom: 12px;

  & > div {
    display: flex;

    &:dir(rtl) {
      flex-direction: row-reverse;
    }
  }
}

.primary {
  position: relative;
  top: -45px;
  inset-inline-start: -12px;
  width: 152px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-inline-end: 24px;
  padding-top: 30px;
  font-size: 14px;
  font-weight: 700;
  &:dir(rtl) {
    font-size: 10px;
  }
  &::before {
    content: '';
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("/images-int/primarybg.png");
  background-size: cover;
  background-position: center;
}

&:dir(rtl) {
  &::before {
  transform: scaleX(-1);
  }
}

  span {
    z-index: 10;
  }
}

.trashButton {
  position: absolute;
  top: 16px;
  inset-inline-end: 16px;
  width: 40px;
  height: 40px;
  background: #cee8f8;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: #088bdd;
  svg {
    width: 24px;
    height: 24px;
  }
}

.cardStatus {
  position: absolute;
  top: 22px;
  display: flex;
  align-items: center;
  inset-inline-start: 20px;
  padding: 6px 14px;
  border-radius: 13px;
  font-size: 12px;
  font-weight: 400;
  img {
    width: 20px;
    height: 20px;
    margin-inline-end: 10px;
  }
}

.cardStatusPrimary {
  display: flex;
  align-items: center;
  padding: 4px 14px;
  border-radius: 13px;
  font-size: 12px;
  font-weight: 400;
  width: 66%;
  margin-inline-start: 10px;
  margin-bottom: 5px;
  img {
    width: 20px;
    height: 20px;
    margin-inline-end: 10px;
  }
}
