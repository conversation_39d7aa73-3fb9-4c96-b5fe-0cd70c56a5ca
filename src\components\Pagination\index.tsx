import styles from "./pagination.module.scss";
import { Fade } from "@mui/material";
import { CaretLeft, CaretRight } from "../svgs";
import { useState, useEffect } from "react";

const Pagination = ({ currentPage, setCurrentPage, disabled }: any) => {
  const [pages, setPages] = useState([] as any);

  useEffect(() => {
    if (pages.length === 0 || currentPage < 5) {
      setPages([0, 1, 2, 3, 4, -1]);
    } else {
      setPages([0, -1, currentPage - 2, currentPage - 1, currentPage, -1]);
    }
  }, [currentPage]);

  return (
    <div className={styles.pageNumbers}>
      <Fade in={currentPage !== 1}>
        <div
          className={styles.pageArrowButton}
          onClick={() => {
            if (currentPage !== 1 && !disabled) {
              setCurrentPage(currentPage - 1);
            }
          }}
        >
          <CaretLeft />
        </div>
      </Fade>
      {pages.map((num: number, index: number) => {
        if (num === -1) {
          return (
            <div key={`dots-${index}`} className={styles.dots}>
              •••
            </div>
          );
        } else {
          return (
            <div
              className={`${styles.pageNumber} ${
                currentPage === num + 1 && styles.activePageNumber
              }`}
              onClick={() => {
                if (!disabled) {
                  setCurrentPage(num + 1);
                }
              }}
              key={`pagination-button-` + num}
            >
              {num + 1}
            </div>
          );
        }
      })}
      <div
        className={styles.pageArrowButton}
        onClick={() => {
          if (!disabled) {
            setCurrentPage(currentPage + 1);
          }
        }}
      >
        <CaretRight />
      </div>
    </div>
  );
};

export default Pagination;
