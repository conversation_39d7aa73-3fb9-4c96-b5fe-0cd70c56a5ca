import Shimmer from "../Shimmer";
import styles from "./dashboard-plan-skeleton.module.scss";

const DashboardPlanSkeleton = () => {
  return (
    <div className={styles.main}>
      <div
        className={styles.box}
        style={{ width: 73, height: 34, marginBottom: 6 }}
      >
        <Shimmer />
      </div>
      <div className={styles.box} style={{ width: 150, height: 43 }}>
        <Shimmer />
      </div>
      <div className={styles.bars}>
        <div
          className={styles.box}
          style={{ width: 100, height: 100, borderRadius: 100 }}
        >
          <Shimmer />
        </div>
        <div>
          <div
            className={styles.box}
            style={{ width: 100, height: 39, marginBottom: 15 }}
          >
            <Shimmer />
          </div>
          <div className={styles.box} style={{ width: 100, height: 39 }}>
            <Shimmer />
          </div>
        </div>
      </div>
      <div className={styles.box} style={{ width: "100%", height: 34 }}>
        <Shimmer />
      </div>
    </div>
  );
};

export default DashboardPlanSkeleton;
