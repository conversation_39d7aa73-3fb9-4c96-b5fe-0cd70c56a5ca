@use "../../styles/theme.scss" as *;

.legalFooter {
    background-image: url('/public/images-int/legalBg.png');
    padding: 50px;
    margin-top: 160px;
    background-size: 100% 100%;
    color: $secondary;
    position: relative;
    background-repeat: no-repeat;
    @media (max-width: 768px) {
        background-size: 100% 90%;
        padding: 60px;
        background-position: center;
        margin-top: 100px;
    }
    @media (max-width: 500px) {
        background-image: url('/public/images-int/legalBgMob.png');
        background-size: 100% 100%;
        padding: 50px 10px;
    }
    @media (max-width: 500px) {
        > div {
            flex-direction: column-reverse;
        }
    }
    .appBtns {
        display: flex;
        gap: 9px;

        button {
            &:last-of-type {
                margin-inline-start: 15px;
                @media (max-width: 400px) {
                    margin-inline-start: 0;
                }
            }
            @media (max-width: 500px) {
                gap: 15px;
            }
            @media (max-width: 400px) {
                font-size: 0;
                padding: 0px 19px;
            }
        }
    }
    h2 {
        margin-top: 15px;
        margin-bottom: 15px;
        width: 70%;
        line-height: 33px;
        @media (max-width: 768px) {
            font-size: 20px;
        }
        @media (max-width: 500px) {
            font-size: 18px;
            width: 100%;
            text-align: center;
        }
    }
    p {
        margin-top: 40px;
        @media (max-width: 500px) {
            margin-top: 0;
            text-align: center;
        }
    }
    .phoneImg
    {
        img {
            position: absolute;
            bottom: 0px;
            width: 48%;
            right: -30px;

            @media (max-width: 500px) {
                position: relative;
                width: 83%;
                right: -45px
            }

            &:dir(rtl) {
                right: auto;
                left: 30px;
            }
        }
    }
}  