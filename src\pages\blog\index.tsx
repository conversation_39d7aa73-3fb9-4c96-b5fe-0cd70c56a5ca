import BlogTile from "../../components/BlogTile";
import styles from "../../styles/blog.module.scss";
import SearchBar from "../../components/SearchBar";
import { useEffect, useState } from "react";
import { CmsApiGet } from "../api/cms-api";
import { Helmet } from 'react-helmet-async';
import FaqAndSearchPlan from "../../components/FaqAndSearchPlan/FaqAndSearchPlan";
import { t } from "i18next";
import { Pagination, PaginationItem, useMediaQuery } from "@mui/material";
import { CaretLeft, CaretRight } from "../../components/svgs";

const Blog = () => {
  const [blogs, setBlogs] = useState([] as any);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(1)
  const [searchQuery, setSearchQuery] = useState("");

  const under530 = useMediaQuery("(max-width: 530px)");

  const handleChange = (event: any, value: any) => {
    setCurrentPage(value);
  };

  useEffect(() => {
      getBlogs('')
  }, [currentPage])

  const clearSearch = () => {
    setSearchQuery("");
    setCurrentPage(1)
    getBlogs('')
  };

  const getBlogs = (filter:string) => {
    CmsApiGet(`/api/blogs?populate=deep&_sort[0]=createdAt:asc&pagination[pageSize]=7&pagination[page]=${currentPage}${filter ? '&filters[title][$containsi]=' + searchQuery : '' }`).then(
      (response) => {
        setBlogs(response.data.data)
        setTotalItems(response.data.meta.pagination.total)
      }
    );
  }

  const handleSearchChange = (e: any) => {
    setSearchQuery(e.target.value);
  };

  const handleSearch = async () => {
    if (searchQuery === "") {
      clearSearch()
      return
    } else {
      await getBlogs(searchQuery)
    }
  }

  return (
    <div>
      <Helmet>
        <title>{t("general.orbit")}| {t("blog.blog")}</title>
        <meta name="description" content={t("blog.description")} />
      </Helmet>
      <div className={styles.blogHeaderContainer}>
        <div className={`${styles.blogHeader} max-width-container`}>
          <div>
            <h1>{t("blog.blog")}</h1>
            <p>{t("blog.description")}</p>
          </div>
          <div>
            <img
              src="/images-int/blog/blogHeader.png"
              alt="Orbit Mobile ESIM - Blog articles"
            />
          </div>
        </div>
      </div>
      <div className={`${styles.blogs} max-width-container`}>
        <div className={styles.titleBar}>
          <h2>{t("blog.latestInsights")}</h2>
          <SearchBar
            handleSearch={handleSearch}
            id="blogs-search-input"
            searchQuery={searchQuery}
            placeholder={t("general.search")}
            clearSearch={clearSearch}
            handleSearchChange={handleSearchChange}
            maxWidth={420}
          />
        </div>
        <div className={styles.blogArticles}>
          {blogs.map((blog: any, index: number) => (
            <BlogTile blog={blog} featured={index === 0} />
          ))}
        </div>
        <div className={`${styles.paginationContainer} blog-pagination`}>
          <Pagination
              count={Math.ceil(totalItems / 7)}
              onChange={handleChange}
              page={currentPage}
              renderItem={(item) => (
                <PaginationItem
                  slots={{ previous: CaretLeft, next: CaretRight }}
                  {...item}
                />
              )}
              size={under530 ? "medium" : "large"}
              siblingCount={under530 ? 0 : 1}
              boundaryCount={1}
            />
          </div>

      </div>
      <FaqAndSearchPlan />
    </div>
  );
};

export default Blog;
