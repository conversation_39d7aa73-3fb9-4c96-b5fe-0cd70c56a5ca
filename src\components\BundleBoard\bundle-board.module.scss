@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  @media (max-width: 768px) {
    padding-top: 36px;
  }
}

.title {
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  display: flex;
  @media (max-width: 420px) {
    flex-direction: column;
    align-items: flex-start;
  }
  .titleText {
    color: $secondary;
    font-weight: 800;
    font-size: 26px;
    line-height: 32px;
    @media (max-width: 420px) {
      margin-bottom: 16px;
    }
  }
}

.mainBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  background: #eff1f7;
  border-radius: 24px;
  padding: 0 24px;
  color: $dark-dark-purple;
  @media (max-width: 1070px) {
    flex-direction: column;
    height: auto;
    padding: 12px 24px;
  }
  .section,
  .phoneNumber {
    display: flex;
    align-items: center;
  }
  .section {
    @media (max-width: 850px) {
      justify-content: space-between;
      width: 100%;
    }
  }
  .phoneNumber {
    @media (max-width: 850px) {
      margin-right: auto;
    }
  }
  .flag {
    width: 24px;
    height: 24px;
    border-radius: 40px;
    margin-right: 12px;
    background-position: center;
    background-size: cover;
    background-color: #aaaaaa;
    @media (max-width: 850px) {
      display: none;
    }
  }
  .number {
    font-weight: 600;
    font-size: 14px;
    line-height: 21px;
    margin-right: 20px;
  }
  .actionButtons {
    display: flex;
    align-items: center;
    .actionButton {
      background: none;
      border: none;
      cursor: pointer;
      margin-right: 10px;
      width: 40px;
      height: 40px;
      border-radius: 4px;
      color: #000;
      &:last-child {
        margin-right: 0px;
      }
      &:hover {
        background-color: #ccd2e6;
      }
      svg {
        width: 20px;
        height: 20px;
        vertical-align: middle;
      }
    }
  }
  .expiresIn {
    margin-left: auto;
    font-size: 16px;
    line-height: 24px;
    margin-right: 20px;
    @media (max-width: 850px) {
      margin: 0;
    }
  }
}

.mobileNumberSelection {
  display: none;
  margin-bottom: 17px;
  display: none;
  align-items: center;
  justify-content: space-evenly;
  grid-template-columns: auto auto auto;
  .flag {
    width: 24px;
    height: 24px;
    border-radius: 40px;
    margin-right: 12px;
    background-position: center;
    background-size: cover;
    background-color: #aaaaaa;
  }
  .countrySelect {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 100px;
    color: $dark-dark-purple;
    font-size: 12px;
    line-height: 18px;
    height: 48px;
    &.active {
      background: $light-primary;
      color: $secondary;
      font-weight: 600;
    }
  }
  @media (max-width: 850px) {
    display: grid;
  }
  @media (max-width: 450px) {
    justify-content: space-between;
  }
}

.planTilesContainer {
  margin-top: 24px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-column-gap: 15px;
  grid-row-gap: 15px;
  align-items: start;
  margin-bottom: 45px;
  @media (max-width: 1150px) {
    grid-template-columns: 1fr;
  }
}

.noBundles {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 137px;
  padding-bottom: 150px;
  color: $secondary;
  text-align: center;
  @media (max-width: 850px) {
    padding: 50px 0;
  }
  img {
    width: 150px;
  }
  h5 {
    font-weight: 700;
    font-size: 20px;
    line-height: 28px;
    margin: 20px 0 6px 0;
  }
  p {
    font-size: 14px;
    line-height: 28px;
    margin: 0 0 32px 0;
    max-width: 511px;
    text-align: center;
  }
}
