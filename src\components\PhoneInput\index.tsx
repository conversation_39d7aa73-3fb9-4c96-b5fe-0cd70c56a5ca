import { t } from "i18next";
import { Input } from "../Input";
import PhoneCountryCode from "../PhoneCountryCode";
import { clearInput, handleInputChange } from "../utils/InputHandlers";

const PhoneInput = ({
  state,
  setState,
  style = {},
  onKeyDown,
  disabled,
}: any) => {
  return (
    <div
      style={{
        ...style,
        display: "grid",
        gridTemplateColumns: "109px auto",
        gridColumnGap: 16,
      }}
    >
      <PhoneCountryCode
        value={state.countryCode}
        onChange={(e: any) => {
          handleInputChange("countryCode", e, state, setState);
        }}
        error={state.errors.countryCode}
        onKeyDown={onKeyDown}
        clear={() => {
          clearInput("countryCode", setState);
        }}
        disabled={disabled}
      />
      <Input
        id="phone-number"
        label={t('placeholders.phoneNumber')}
        value={state.phoneNumber}
        onChange={(e: any) => {
          handleInputChange("phoneNumber", e, state, setState);
        }}
        error={state.errors.phoneNumber}
        onKeyDown={onKeyDown}
        clear={() => {
          clearInput("phoneNumber", setState);
        }}
        disabled={disabled}
      />
    </div>
  );
};

export default PhoneInput;
