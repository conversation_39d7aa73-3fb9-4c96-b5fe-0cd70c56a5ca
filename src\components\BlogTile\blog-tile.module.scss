@use "../../styles/theme.scss" as *;

.main {
  margin-bottom: 25px;
  background-color: #fff;
  border-radius: 24px;
  grid-column: span 1;
  cursor: pointer;
  @media (max-width: 600px) {
    display: flex;
    flex-direction: column;
    margin-bottom: 50px;
  }
  &.featured {
    grid-column: span 2;
    @media (max-width: 768px) {
      grid-column: span 1;
    }
  }
  .authorData {
    display: flex;
    align-items: center;
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-inline-end: 15px
    }
  }
  .text {
    color: $dark-dark-purple;
    padding: 5px 20px;
    h5 {
      font-weight: 600;
      font-size: 22px;
      line-height: 27px;
      margin: 8px 0;
      @media (max-width: 900px) {
        font-size: 20px;
        line-height: 30px;
        margin-top: 12px;
      }
    }
    .excerpt {
      p, img, h1, h2, h3, h4, h5, h6, blockquote {
        display: none;
      }
      p:first-of-type {
        overflow: hidden;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        margin-bottom: 10px;
      }
    }
    h6 {
      font-size: 14px;
      font-weight: bold;
      margin-top: 5px;
      margin-bottom: 5px;
    }
    p {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      margin: 0;
      @media (max-width: 900px) {
        font-size: 12px;
        line-height: 24px;
      }
    }
  }
}
.imageWrapper {
  position: relative;
  background-repeat: no-repeat;
  height: 340px;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  background-size: cover;
  background-position: center;
  .readingCounter {
    position: absolute;
    right: 30px;
    background: #fff;
    border-radius: 20px;
    top: 20px;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    span {
      margin-inline-start: 6px;
    }
  }
}

