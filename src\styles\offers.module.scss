@use "./theme.scss" as *;

.main {
  text-align: center;
  color: rgba(15, 19, 58, 1);
  h2 {
    font-size: 48px;
    font-weight: 800;
    margin-bottom: 15px;
    @media (max-width: 425px) {
      font-size: 37px;
    }
  }
  h4 {
    font-size: 24px;
    font-weight: 700;
  }
  p {
    font-size: 16px;
  }
  svg,
  span {
    vertical-align: middle;
  }
  svg {
    color: rgba(15, 19, 58, 1);
  }
  .offersView {
    display: grid;
    text-align: initial;
    grid-template-columns: auto auto auto;
    padding: 50px 100px;
    gap: 20px;
    @media (max-width: 425px) {
      grid-template-columns: auto;
      padding: 15px 20px;
    }
    .offerItem {
      background-color: #cdf2f2;
      border-radius: 20px;
      padding: 20px;
      .percentageSymbol {
        background-color: #9bf0de;
        font-size: 16px;
        font-weight: 700;
        padding: 6px 8px;
        border-radius: 50%;
      }
      .countriesFlags {
        position: relative;
        span {
          font-size: 12px;
          font-weight: 400;
          margin-left: 48px;
          text-decoration: underline;
          cursor: pointer;
        }
        .countriesFlag {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          position: absolute;
        }
      }
      .offerCta {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        align-items: center;
        a {
          color: #fff;
          text-decoration: none;
        }
      }
    }
  }
  .countiresCoveredModal {
    position: relative;
    h2 {
      @media (max-width: 425px) {
        font-size: 27px;
      }
    }
    .close {
      position: absolute;
      right: 20px;
      top: 0;
      cursor: pointer;
      svg {
        stroke: #088bdd;
        @media (max-width: 425px) {
          width: 17px;
        }
      }
    }
    .countriesListCovered {
      display: grid;
      grid-template-columns: auto auto auto auto;
      padding: 30px 50px;
      gap: 20px;
      @media (max-width: 425px) {
        padding: 10px 25px;
        grid-template-columns: auto;
      }
      div {
        background-color: #fafaf9;
        border-radius: 13px;
        padding: 4px 8px;
        img {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          margin-right: 10px;
        }
        p {
          font-size: 13px;
        }
      }
    }
  }
}

.comingSoon {
  flex-grow: 1;
  background: #cdf7ee;
  display: flex;

  .container {
    flex-grow: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: $secondary;
    padding: 120px 24px;
    @media (max-width: 1250px) {
      padding-bottom: 350px;
    }
    @media (max-width: 768px) {
      padding-right: 16px;
      padding-left: 16px;
      padding-bottom: 250px;
    }
    h1 {
      font-size: 48px;
      font-weight: 700;
      line-height: 61.32px;
      letter-spacing: -0.025em;
      margin: 0px 0px 16px 0px;
      @media (max-width: 768px) {
        font-size: 30px;
        line-height: 38px;
      }
      .gradient {
        background-image: linear-gradient(90deg, #049efe 0%, #04dcad 100%);
        color: transparent;
        background-clip: text;
      }
    }
    p {
      margin: 0px;
      max-width: 640px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
    }
    .manImage {
      position: absolute;
      bottom: 0px;
      right: 0px;
      @media (max-width: 1650px) {
        right: 50px;
      }
    }
    .starsLeft {
      position: absolute;
      left: -200px;
      bottom: 10px;
      @media (max-width: 800px) {
        display: none;
      }
    }
    .starsRight {
      position: absolute;
      right: -150px;
      top: -33px;
      @media (max-width: 800px) {
        display: none;
      }
    }
  }
}
