@use "./theme.scss" as *;

.mainContainer {
  display: flex;
  flex-direction: column;
  .blogArticle {
    padding: 0px 120px;
    @media (max-width: 768px) {
      padding: 0px 50px
    }
    > .blogInfo {
      margin: 30px  150px;
      @media (max-width: 768px) {
        margin: 30px 40px
      }
      @media (max-width: 425px) {
        margin: 30px 5px;
      }
    }
    .blogInfo {
      display: flex;
      justify-content: space-between;
      align-items: center;
      @media (max-width: 425px) {
        flex-direction: column;
        align-items: baseline;
      }
      .shareLink {
        display: flex;
        align-items: center;
        gap: 5px;
        @media (max-width: 425px) {
          margin-top: 20px
        }
        a {
          display: flex;
          width: 35px;
          height: 35px;
          align-items: center;
          justify-content: center;
          background: #CDF7EE;
          border-radius: 50%;
          svg {
            width: 20px;
            height: 20px;
          }
        }
      }
      .authorData {
        display: flex;
        align-items: center;
        .author, .date {
          font-size: 14px;
          margin: 0;
        }
        .author {
          font-weight: 700;
          padding-bottom: 7px;
        }
        img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 15px
        }
      }
    }
    .headerImg {
      height: 400px;
      border-radius: 20px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      @media (max-width: 768px) {
        height: 300px
      }
      @media (max-width: 425px) {
        height: 230px
      }
    }
    h2 {
      font-size: 48px;
      color: $secondary;
      font-weight: 700;
      text-align: center;
      @media (max-width: 768px) {
        font-size: 40px;
      }
      @media (max-width: 425px) {
        font-size: 26px;
      }
    }
    .container {
      border-bottom: 1px solid #667085;
      padding-bottom: 30px;
      margin: 30px 150px;
      @media (max-width: 768px) {
        margin: 30px
      }
      @media (max-width: 425px) {
        margin: 30px 0
      }
      .content {
        p {
          font-size: 16px;
          line-height: 28px;
          text-align: justify;
          padding-bottom: 10px;
        }
        blockquote {
          font-size: 20px;
          line-height: 28px;
          font-style: italic;
          border-left: 2px solid #05BFBE;
          padding-left: 40px;
          margin-left: 0;
          font-weight: 200;
          margin-bottom: 40px;
          margin-top: 40px;
          @media (max-width: 425px) {
            margin-right: 0;
            font-size: 15px;
          }
        }
        img {
          height: 400px;
          width: 900px;
          border-radius: 24px;
          margin-top: 35px;
          margin-bottom: 35px;
          @media (max-width: 768px) {
            height: 280px;
            width: 100%;
            margin-top: 10px;
            margin-bottom: 10px;
          }
          @media (max-width: 425px) {
            height: 240px;
          }
        }
      }
    }
    .relatedArticles {
      h3 {
        font-size: 30px;
        font-weight: 800;
      }
      > div {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 25px;
        @media (max-width: 768px) {
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
        }
        @media (max-width: 425px) {
          grid-template-columns: repeat(1, 1fr);
        }
      }
    }
  }
}
