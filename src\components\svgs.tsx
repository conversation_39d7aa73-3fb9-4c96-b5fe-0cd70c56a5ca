export const BookOpen = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_12457_3460)">
      <path
        d="M12 8.25C12 7.45435 12.3161 6.69129 12.8787 6.12868C13.4413 5.56607 14.2044 5.25 15 5.25H21C21.1989 5.25 21.3897 5.32902 21.5303 5.46967C21.671 5.61032 21.75 5.80109 21.75 6V18C21.75 18.1989 21.671 18.3897 21.5303 18.5303C21.3897 18.671 21.1989 18.75 21 18.75H15C14.2044 18.75 13.4413 19.0661 12.8787 19.6287C12.3161 20.1913 12 20.9544 12 21.75"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.25 18C2.25 18.1989 2.32902 18.3897 2.46967 18.5303C2.61032 18.671 2.80109 18.75 3 18.75H9C9.79565 18.75 10.5587 19.0661 11.1213 19.6287C11.6839 20.1913 12 20.9544 12 21.75V8.25C12 7.45435 11.6839 6.69129 11.1213 6.12868C10.5587 5.56607 9.79565 5.25 9 5.25H3C2.80109 5.25 2.61032 5.32902 2.46967 5.46967C2.32902 5.61032 2.25 5.80109 2.25 6V18Z"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_12457_3460">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const MagnifyingGlass = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.875 18.75C15.2242 18.75 18.75 15.2242 18.75 10.875C18.75 6.52576 15.2242 3 10.875 3C6.52576 3 3 6.52576 3 10.875C3 15.2242 6.52576 18.75 10.875 18.75Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.4436 16.4438L20.9999 21.0001"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Trolley = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.375 14.375H5.45313L3.27344 2.39063C3.24793 2.2471 3.17307 2.11701 3.06179 2.02285C2.95051 1.9287 2.80982 1.8764 2.66406 1.875H1.25"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.25 17.5C7.11294 17.5 7.8125 16.8004 7.8125 15.9375C7.8125 15.0746 7.11294 14.375 6.25 14.375C5.38706 14.375 4.6875 15.0746 4.6875 15.9375C4.6875 16.8004 5.38706 17.5 6.25 17.5Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.375 17.5C15.2379 17.5 15.9375 16.8004 15.9375 15.9375C15.9375 15.0746 15.2379 14.375 14.375 14.375C13.5121 14.375 12.8125 15.0746 12.8125 15.9375C12.8125 16.8004 13.5121 17.5 14.375 17.5Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.88281 11.25H14.6953C14.9875 11.2509 15.2706 11.1488 15.4949 10.9616C15.7193 10.7744 15.8704 10.5142 15.9219 10.2266L16.875 5H3.75"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Facebook = () => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M23.9983 12.6586C23.9983 18.7194 19.5059 23.7299 13.6698 24.543C13.1242 24.6187 12.5659 24.6582 11.9992 24.6582C11.345 24.6582 10.7026 24.6061 10.0771 24.5052C4.36379 23.5853 0 18.6311 0 12.6586C0 6.03115 5.37276 0.658203 12 0.658203C18.6272 0.658203 24 6.03115 24 12.6586H23.9983Z"
      fill="white"
    />
    <path
      d="M13.6689 10.2938V12.908H16.9026L16.3906 16.4294H13.6689V24.5427C13.1232 24.6183 12.5649 24.6579 11.9982 24.6579C11.344 24.6579 10.7017 24.6057 10.0761 24.5048V16.4294H7.09375V12.908H10.0761V9.70946C10.0761 7.72509 11.6846 6.11572 13.6697 6.11572V6.11741C13.6756 6.11741 13.6806 6.11572 13.6865 6.11572H16.9034V9.16124H14.8014C14.1767 9.16124 13.6697 9.66826 13.6697 10.293L13.6689 10.2938Z"
      fill="#0F133A"
    />
  </svg>
);
export const LinkIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2.22367 17.778C2.68754 18.2425 3.23864 18.6108 3.84529 18.8617C4.45194 19.1126 5.10219 19.2411 5.75867 19.24C6.4153 19.2411 7.06569 19.1125 7.6725 18.8617C8.2793 18.6108 8.83058 18.2425 9.29467 17.778L12.1227 14.949L10.7087 13.535L7.88067 16.364C7.31714 16.925 6.55434 17.2399 5.75917 17.2399C4.964 17.2399 4.2012 16.925 3.63767 16.364C3.07617 15.8007 2.76088 15.0378 2.76088 14.2425C2.76088 13.4471 3.07617 12.6842 3.63767 12.121L6.46667 9.29296L5.05267 7.87896L2.22367 10.707C1.28748 11.6454 0.761719 12.9169 0.761719 14.2425C0.761719 15.568 1.28748 16.8395 2.22367 17.778ZM17.7797 9.29296C18.7154 8.35425 19.2408 7.08288 19.2408 5.75746C19.2408 4.43204 18.7154 3.16068 17.7797 2.22196C16.8412 1.28577 15.5698 0.76001 14.2442 0.76001C12.9186 0.76001 11.6471 1.28577 10.7087 2.22196L7.88067 5.05096L9.29467 6.46496L12.1227 3.63596C12.6862 3.07495 13.449 2.75999 14.2442 2.75999C15.0393 2.75999 15.8021 3.07495 16.3657 3.63596C16.9272 4.19923 17.2425 4.96213 17.2425 5.75746C17.2425 6.55279 16.9272 7.31569 16.3657 7.87896L13.5367 10.707L14.9507 12.121L17.7797 9.29296Z" fill="black"/>
    <path d="M6.46566 14.95L5.05066 13.536L13.5377 5.05005L14.9517 6.46505L6.46566 14.95Z" fill="black"/>
  </svg>
)
export const LinkedinSquare = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 3.24268C3.67157 3.24268 3 3.91425 3 4.74268V19.7427C3 20.5711 3.67157 21.2427 4.5 21.2427H19.5C20.3284 21.2427 21 20.5711 21 19.7427V4.74268C21 3.91425 20.3284 3.24268 19.5 3.24268H4.5ZM8.52076 7.2454C8.52639 8.20165 7.81061 8.79087 6.96123 8.78665C6.16107 8.78243 5.46357 8.1454 5.46779 7.24681C5.47201 6.40165 6.13998 5.72243 7.00764 5.74212C7.88795 5.76181 8.52639 6.40728 8.52076 7.2454ZM12.2797 10.0044H9.75971H9.7583V18.5643H12.4217V18.3646C12.4217 17.9847 12.4214 17.6047 12.4211 17.2246C12.4203 16.2108 12.4194 15.1959 12.4246 14.1824C12.426 13.9363 12.4372 13.6804 12.5005 13.4455C12.7381 12.568 13.5271 12.0013 14.4074 12.1406C14.9727 12.2291 15.3467 12.5568 15.5042 13.0898C15.6013 13.423 15.6449 13.7816 15.6491 14.129C15.6605 15.1766 15.6589 16.2242 15.6573 17.2719C15.6567 17.6417 15.6561 18.0117 15.6561 18.3815V18.5629H18.328V18.3576C18.328 17.9056 18.3278 17.4537 18.3275 17.0018C18.327 15.8723 18.3264 14.7428 18.3294 13.6129C18.3308 13.1024 18.276 12.599 18.1508 12.1054C17.9638 11.3713 17.5771 10.7638 16.9485 10.3251C16.5027 10.0129 16.0133 9.81178 15.4663 9.78928C15.404 9.78669 15.3412 9.7833 15.2781 9.77989C14.9984 9.76477 14.7141 9.74941 14.4467 9.80334C13.6817 9.95662 13.0096 10.3068 12.5019 10.9241C12.4429 10.9949 12.3852 11.0668 12.2991 11.1741L12.2797 11.1984V10.0044ZM5.68164 18.5671H8.33242V10.01H5.68164V18.5671Z" fill="black"/>
  </svg>
)

export const FacebookBlack = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M22 12.3038C22 6.74719 17.5229 2.24268 12 2.24268C6.47715 2.24268 2 6.74719 2 12.3038C2 17.3255 5.65684 21.4879 10.4375 22.2427V15.2121H7.89844V12.3038H10.4375V10.0872C10.4375 7.56564 11.9305 6.1728 14.2146 6.1728C15.3088 6.1728 16.4531 6.36931 16.4531 6.36931V8.84529H15.1922C13.95 8.84529 13.5625 9.6209 13.5625 10.4166V12.3038H16.3359L15.8926 15.2121H13.5625V22.2427C18.3432 21.4879 22 17.3257 22 12.3038Z" fill="black"/>
  </svg>
)

export const XLogoBlack = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.1761 4.24268H19.9362L13.9061 11.0201L21 20.2427H15.4456L11.0951 14.6493L6.11723 20.2427H3.35544L9.80517 12.9935L3 4.24268H8.69545L12.6279 9.3553L17.1761 4.24268ZM16.2073 18.6181H17.7368L7.86441 5.78196H6.2232L16.2073 18.6181Z" fill="black"/>
  </svg>
)

export const Instagram = () => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.0273 0.658203H11.9727C5.36035 0.658203 0 6.01855 0 12.6309V12.6855C0 19.2979 5.36035 24.6582 11.9727 24.6582H12.0273C18.6397 24.6582 24 19.2979 24 12.6855V12.6309C24 6.01855 18.6397 0.658203 12.0273 0.658203Z"
      fill="white"
    />
    <path
      d="M15.7095 5.54053H8.2832C6.23156 5.54053 4.5625 7.20959 4.5625 9.26123V16.0552C4.5625 18.1068 6.23156 19.7759 8.2832 19.7759H15.7095C17.7611 19.7759 19.4302 18.1068 19.4302 16.0552V9.26123C19.4302 7.20959 17.7611 5.54053 15.7095 5.54053ZM5.87505 9.26123C5.87505 7.93355 6.95552 6.85307 8.2832 6.85307H15.7095C17.0372 6.85307 18.1176 7.93355 18.1176 9.26123V16.0552C18.1176 17.3829 17.0372 18.4633 15.7095 18.4633H8.2832C6.95552 18.4633 5.87505 17.3829 5.87505 16.0552V9.26123Z"
      fill="#0F133A"
    />
    <path
      d="M11.9921 16.1185C13.9 16.1185 15.453 14.5664 15.453 12.6577C15.453 10.749 13.9008 9.19678 11.9921 9.19678C10.0834 9.19678 8.53125 10.749 8.53125 12.6577C8.53125 14.5664 10.0834 16.1185 11.9921 16.1185ZM11.9921 10.5102C13.1769 10.5102 14.1405 11.4738 14.1405 12.6585C14.1405 13.8432 13.1769 14.8068 11.9921 14.8068C10.8074 14.8068 9.8438 13.8432 9.8438 12.6585C9.8438 11.4738 10.8074 10.5102 11.9921 10.5102Z"
      fill="#0F133A"
    />
    <path
      d="M15.776 9.7556C16.2896 9.7556 16.7082 9.33771 16.7082 8.82311C16.7082 8.30852 16.2904 7.89062 15.776 7.89062C15.2615 7.89062 14.8438 8.30852 14.8438 8.82311C14.8438 9.33771 15.2615 9.7556 15.776 9.7556Z"
      fill="#0F133A"
    />
  </svg>
);

export const Linkedin = () => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.0273 0.658203H11.9727C5.36036 0.658203 0 6.01855 0 12.6309V12.6855C0 19.2979 5.36036 24.6582 11.9727 24.6582H12.0273C18.6396 24.6582 24 19.2979 24 12.6855V12.6309C24 6.01855 18.6396 0.658203 12.0273 0.658203Z"
      fill="white"
    />
    <path
      d="M5.72338 8.63518C5.40723 8.34173 5.25 7.97849 5.25 7.5463C5.25 7.11411 5.40807 6.73489 5.72338 6.4406C6.03954 6.14715 6.4465 6 6.94512 6C7.44373 6 7.83473 6.14715 8.15004 6.4406C8.4662 6.73405 8.62342 7.10318 8.62342 7.5463C8.62342 7.98942 8.46536 8.34173 8.15004 8.63518C7.83389 8.92863 7.4328 9.07578 6.94512 9.07578C6.45743 9.07578 6.03954 8.92863 5.72338 8.63518ZM8.35773 10.3185V19.3155H5.51486V10.3185H8.35773Z"
      fill="#0F133A"
    />
    <path
      d="M17.8228 11.2072C18.4425 11.8799 18.752 12.8032 18.752 13.9786V19.1565H16.0521V14.3436C16.0521 13.7508 15.8982 13.29 15.5913 12.9621C15.2844 12.6341 14.8707 12.4693 14.3527 12.4693C13.8348 12.4693 13.4211 12.6333 13.1142 12.9621C12.8073 13.29 12.6534 13.7508 12.6534 14.3436V19.1565H9.9375V10.2933H12.6534V11.4687C12.9283 11.0769 13.2992 10.7675 13.765 10.5396C14.2308 10.3118 14.7546 10.1982 15.3373 10.1982C16.3749 10.1982 17.204 10.5346 17.8228 11.2064V11.2072Z"
      fill="#0F133A"
    />
  </svg>
);

export const Tiktok = () => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.0273 0.658203H11.9727C5.36035 0.658203 0 6.01855 0 12.6309V12.6855C0 19.2979 5.36035 24.6582 11.9727 24.6582H12.0273C18.6397 24.6582 24 19.2979 24 12.6855V12.6309C24 6.01855 18.6397 0.658203 12.0273 0.658203Z"
      fill="white"
    />
    <path
      d="M17.7971 9.34064V11.6067C17.401 11.568 16.8864 11.478 16.3172 11.2695C15.5739 10.9971 15.0206 10.6246 14.6582 10.3362V14.9162L14.649 14.9019C14.6549 14.9927 14.6582 15.0852 14.6582 15.1786C14.6582 17.453 12.8075 19.3046 10.5322 19.3046C8.25693 19.3046 6.40625 17.453 6.40625 15.1786C6.40625 12.9041 8.25693 11.0517 10.5322 11.0517C10.7551 11.0517 10.9737 11.0694 11.1873 11.1039V13.3371C10.9821 13.264 10.7618 13.2245 10.5322 13.2245C9.45512 13.2245 8.57813 14.1006 8.57813 15.1786C8.57813 16.2565 9.45512 17.1327 10.5322 17.1327C11.6093 17.1327 12.4863 16.2557 12.4863 15.1786C12.4863 15.1382 12.4855 15.0978 12.483 15.0575V6.15723H14.7473C14.7558 6.34894 14.7633 6.54233 14.7717 6.73404C14.7869 7.11158 14.9214 7.47398 15.156 7.77079C15.4309 8.11974 15.8371 8.52502 16.4072 8.84875C16.9411 9.15061 17.4422 9.28094 17.7971 9.34232V9.34064Z"
      fill="#0F133A"
    />
  </svg>
);

export const Play = () => (
  <svg
    width="102"
    height="102"
    viewBox="0 0 102 102"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M90.5042 48.0889L33.4085 13.1728C32.9279 12.8763 32.3768 12.7135 31.8122 12.701C31.2477 12.6885 30.69 12.8268 30.1967 13.1018C29.7034 13.3767 29.2924 13.7783 29.0061 14.2651C28.7198 14.7518 28.5685 15.3062 28.5679 15.8709V85.703C28.5685 86.2678 28.7198 86.8221 29.0061 87.3089C29.2924 87.7956 29.7034 88.1972 30.1967 88.4721C30.69 88.7471 31.2477 88.8854 31.8122 88.8729C32.3768 88.8605 32.9279 88.6976 33.4085 88.4011L90.5042 53.485C90.9731 53.2078 91.3616 52.8131 91.6314 52.34C91.9013 51.8669 92.0432 51.3316 92.0432 50.787C92.0432 50.2423 91.9013 49.707 91.6314 49.2339C91.3616 48.7608 90.9731 48.3661 90.5042 48.0889Z"
      fill="currentColor"
    />
  </svg>
);

export const CaretLeft = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20 26L10 16L20 6"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CaretRight = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 6L22 16L12 26"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Clear = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 9L9 15"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 15L9 9"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Pause = () => (
  <svg
    fill="currentColor"
    width="800px"
    height="800px"
    viewBox="0 0 277.338 277.338"
  >
    <g>
      <path
        d="M14.22,45.665v186.013c0,25.223,16.711,45.66,37.327,45.66c20.618,0,37.339-20.438,37.339-45.66V45.665
		c0-25.211-16.721-45.657-37.339-45.657C30.931,0,14.22,20.454,14.22,45.665z"
      />
      <path
        d="M225.78,0c-20.614,0-37.325,20.446-37.325,45.657V231.67c0,25.223,16.711,45.652,37.325,45.652s37.338-20.43,37.338-45.652
		V45.665C263.109,20.454,246.394,0,225.78,0z"
      />
    </g>
  </svg>
);

export const Esim = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_11752_1978)">
      <path
        d="M18.75 21H5.25C5.05109 21 4.86032 20.921 4.71967 20.7803C4.57902 20.6397 4.5 20.4489 4.5 20.25V3.75C4.5 3.55109 4.57902 3.36032 4.71967 3.21967C4.86032 3.07902 5.05109 3 5.25 3H14.25L19.5 8.25V20.25C19.5 20.4489 19.421 20.6397 19.2803 20.7803C19.1397 20.921 18.9489 21 18.75 21Z"
        stroke="#088BDD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.5 11.25H7.5V18H16.5V11.25Z"
        stroke="#088BDD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5 14.25V18"
        stroke="#088BDD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.5 14.25V18"
        stroke="#088BDD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_11752_1978">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PaymentCard = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_11752_454)">
      <path
        d="M21 5.25H3C2.58579 5.25 2.25 5.58579 2.25 6V18C2.25 18.4142 2.58579 18.75 3 18.75H21C21.4142 18.75 21.75 18.4142 21.75 18V6C21.75 5.58579 21.4142 5.25 21 5.25Z"
        stroke="#088BDD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.75 15.75H18.75"
        stroke="#088BDD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.25 15.75H12.75"
        stroke="#088BDD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.25 9H21.75"
        stroke="#088BDD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_11752_454">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Signal = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 6C18.552 6 19 6.446 19 6.995V17.005C19 17.555 18.552 18 18 18C17.448 18 17 17.554 17 17.005V6.995C17 6.445 17.448 6 18 6ZM14 9C14.2652 9 14.5196 9.10536 14.7071 9.29289C14.8946 9.48043 15 9.73478 15 10V17C15 17.2652 14.8946 17.5196 14.7071 17.7071C14.5196 17.8946 14.2652 18 14 18C13.7348 18 13.4804 17.8946 13.2929 17.7071C13.1054 17.5196 13 17.2652 13 17V10C13 9.73478 13.1054 9.48043 13.2929 9.29289C13.4804 9.10536 13.7348 9 14 9ZM10 12C10.552 12 11 12.445 11 12.994V17.006C11 17.555 10.552 18 10 18C9.448 18 9 17.555 9 17.006V12.994C9.00039 12.8631 9.02657 12.7335 9.07704 12.6127C9.12751 12.4919 9.20128 12.3822 9.29413 12.2899C9.38699 12.1976 9.49712 12.1245 9.61824 12.0747C9.73935 12.025 9.86907 11.9996 10 12ZM6 15C6.552 15 7 15.44 7 15.984V17.016C6.99895 17.1463 6.97226 17.2751 6.92144 17.395C6.87061 17.515 6.79666 17.6237 6.7038 17.7151C6.61094 17.8065 6.50099 17.8787 6.38024 17.9276C6.25948 17.9764 6.13027 18.0011 6 18C5.448 18 5 17.56 5 17.016V15.984C5.00105 15.8537 5.02774 15.7249 5.07856 15.605C5.12939 15.485 5.20334 15.3763 5.2962 15.2849C5.38906 15.1935 5.49901 15.1213 5.61976 15.0724C5.74052 15.0236 5.86973 14.9989 6 15Z"
      fill="currentColor"
    />
  </svg>
);

export const Password = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_10824_7136)">
      <path
        d="M3.75 5.25V18.75"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5 9V12"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.64697 11.0728L10.4998 12"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.73633 14.4272L10.4998 12"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.2634 14.4272L10.5 12"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.3528 11.0728L10.5 12"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.5 9V12"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.647 11.0728L19.4998 12"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.7363 14.4272L19.4998 12"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21.2634 14.4272L19.5 12"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.3528 11.0728L19.5 12"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_10824_7136">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const EmailSign = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_10824_6341)">
      <path
        d="M12 15.75C14.0711 15.75 15.75 14.0711 15.75 12C15.75 9.92893 14.0711 8.25 12 8.25C9.92893 8.25 8.25 9.92893 8.25 12C8.25 14.0711 9.92893 15.75 12 15.75Z"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.25 19.5C15.8241 20.4478 13.8403 21 12 21C10.22 21 8.47991 20.4722 6.99987 19.4832C5.51983 18.4943 4.36628 17.0887 3.68509 15.4442C3.0039 13.7996 2.82567 11.99 3.17294 10.2442C3.5202 8.49836 4.37737 6.89472 5.63604 5.63604C6.89472 4.37737 8.49836 3.5202 10.2442 3.17294C11.99 2.82567 13.7996 3.0039 15.4442 3.68509C17.0887 4.36628 18.4943 5.51983 19.4832 6.99987C20.4722 8.47991 21 10.22 21 12C21 14.0709 20.25 15.75 18.375 15.75C16.5 15.75 15.75 14.0709 15.75 12V8.25"
        stroke="#0F133A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_10824_6341">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Phone = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.67187 11.6998C9.44364 13.2935 10.7324 14.579 12.3281 15.3467C12.4458 15.4024 12.576 15.4265 12.7059 15.4167C12.8358 15.4068 12.9608 15.3633 13.0687 15.2904L15.4125 13.7248C15.516 13.6546 15.6357 13.6117 15.7603 13.6002C15.8849 13.5887 16.0104 13.609 16.125 13.6592L20.5125 15.5435C20.6625 15.6059 20.7877 15.7159 20.869 15.8565C20.9504 15.9971 20.9832 16.1606 20.9625 16.3217C20.8234 17.407 20.2937 18.4046 19.4723 19.1276C18.6509 19.8506 17.5943 20.2495 16.5 20.2498C13.1185 20.2498 9.87548 18.9065 7.48439 16.5154C5.0933 14.1243 3.75 10.8813 3.75 7.49979C3.75025 6.40553 4.1492 5.34886 4.87221 4.5275C5.59522 3.70613 6.59274 3.17635 7.67812 3.03729C7.83922 3.01659 8.00266 3.04943 8.14326 3.13074C8.28386 3.21206 8.39384 3.33733 8.45625 3.48729L10.3406 7.88416C10.3896 7.99699 10.4101 8.12013 10.4003 8.24275C10.3905 8.36537 10.3507 8.48369 10.2844 8.58729L8.71875 10.9685C8.64905 11.0762 8.60814 11.2 8.59993 11.328C8.59172 11.4561 8.61649 11.5841 8.67187 11.6998V11.6998Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SMS = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.3749 20.25H4.47182C4.37667 20.2513 4.28223 20.2334 4.19408 20.1976C4.10593 20.1618 4.02585 20.1087 3.95856 20.0414C3.89128 19.9741 3.83815 19.894 3.80232 19.8059C3.76649 19.7177 3.74868 19.6233 3.74994 19.5281V11.625C3.74994 9.33751 4.65864 7.14371 6.27614 5.5262C7.89365 3.9087 10.0874 3 12.3749 3V3C13.5076 3 14.6292 3.22309 15.6756 3.65654C16.722 4.08999 17.6728 4.7253 18.4737 5.5262C19.2746 6.32711 19.91 7.27792 20.3434 8.32436C20.7768 9.37079 20.9999 10.4923 20.9999 11.625V11.625C20.9999 12.7577 20.7768 13.8792 20.3434 14.9256C19.91 15.9721 19.2746 16.9229 18.4737 17.7238C17.6728 18.5247 16.722 19.16 15.6756 19.5935C14.6292 20.0269 13.5076 20.25 12.3749 20.25V20.25Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.375 10.5H15"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.375 13.5H15"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Gift = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 7.5H3.75C3.33579 7.5 3 7.83579 3 8.25V11.25C3 11.6642 3.33579 12 3.75 12H20.25C20.6642 12 21 11.6642 21 11.25V8.25C21 7.83579 20.6642 7.5 20.25 7.5Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M19.5 12V18.75C19.5 18.9489 19.421 19.1397 19.2803 19.2803C19.1397 19.421 18.9489 19.5 18.75 19.5H5.25C5.05109 19.5 4.86032 19.421 4.71967 19.2803C4.57902 19.1397 4.5 18.9489 4.5 18.75V12"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 7.5V19.5"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.2469 6.44091C15.1781 7.50029 12 7.50029 12 7.50029C12 7.50029 12 4.32216 13.0594 3.25341C13.4821 2.83073 14.0554 2.59326 14.6531 2.59326C15.2509 2.59326 15.8242 2.83073 16.2469 3.25341C16.6696 3.6761 16.907 4.24939 16.907 4.84716C16.907 5.44494 16.6696 6.01823 16.2469 6.44091V6.44091Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.75293 6.44091C8.82168 7.50029 11.9998 7.50029 11.9998 7.50029C11.9998 7.50029 11.9998 4.32216 10.9404 3.25341C10.5177 2.83073 9.94445 2.59326 9.34668 2.59326C8.7489 2.59326 8.17561 2.83073 7.75293 3.25341C7.33024 3.6761 7.09277 4.24939 7.09277 4.84716C7.09277 5.44494 7.33024 6.01823 7.75293 6.44091V6.44091Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Clock = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
      stroke="currentColor"
      strokeMiterlimit="10"
    />
    <path
      d="M12 6.75V12H17.25"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Close = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M25 7L7 25"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M25 25L7 7"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Mobile = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.75 1.875H6.25C5.55964 1.875 5 2.43464 5 3.125V16.875C5 17.5654 5.55964 18.125 6.25 18.125H13.75C14.4404 18.125 15 17.5654 15 16.875V3.125C15 2.43464 14.4404 1.875 13.75 1.875Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5 4.375H15"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5 15.625H15"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Trash = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 5.25H3.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.75 9.75V15.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.25 9.75V15.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18.75 5.25V19.5C18.75 19.6989 18.671 19.8897 18.5303 20.0303C18.3897 20.171 18.1989 20.25 18 20.25H6C5.80109 20.25 5.61032 20.171 5.46967 20.0303C5.32902 19.8897 5.25 19.6989 5.25 19.5V5.25"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.75 5.25V3.75C15.75 3.35218 15.592 2.97064 15.3107 2.68934C15.0294 2.40804 14.6478 2.25 14.25 2.25H9.75C9.35218 2.25 8.97064 2.40804 8.68934 2.68934C8.40804 2.97064 8.25 3.35218 8.25 3.75V5.25"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Suitcase = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 6.75H3.75C3.33579 6.75 3 7.08579 3 7.5V19.5C3 19.9142 3.33579 20.25 3.75 20.25H20.25C20.6642 20.25 21 19.9142 21 19.5V7.5C21 7.08579 20.6642 6.75 20.25 6.75Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.75 6.75V5.25C15.75 4.85218 15.592 4.47064 15.3107 4.18934C15.0294 3.90804 14.6478 3.75 14.25 3.75H9.75C9.35218 3.75 8.97064 3.90804 8.68934 4.18934C8.40804 4.47064 8.25 4.85218 8.25 5.25V6.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3 15H21"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Globe = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.5 21C17.4706 21 21.5 16.9706 21.5 12C21.5 7.02944 17.4706 3 12.5 3C7.52944 3 3.5 7.02944 3.5 12C3.5 16.9706 7.52944 21 12.5 21Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20.5813 15.9753L15.4906 12.844C15.403 12.7877 15.3035 12.7524 15.2 12.7409L13.0531 12.4503C12.893 12.4273 12.7297 12.4581 12.5889 12.5378C12.4481 12.6175 12.3377 12.7417 12.275 12.8909L10.9906 15.769C10.9303 15.9029 10.9105 16.0516 10.9338 16.1966C10.9572 16.3416 11.0226 16.4765 11.1219 16.5847L12.8844 18.4878C12.9641 18.5754 13.0224 18.6801 13.055 18.794C13.0875 18.9078 13.0933 19.0276 13.0719 19.144L12.7063 21.0003"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.59379 5.21289L5.75004 7.20039C5.67655 7.37684 5.67319 7.57468 5.74066 7.75352L6.81879 10.6223C6.86092 10.7418 6.93305 10.8486 7.02825 10.9323C7.12346 11.016 7.23856 11.0739 7.36254 11.1004L9.36879 11.5316C9.48014 11.5542 9.58472 11.6023 9.67422 11.6723C9.76372 11.7423 9.83569 11.8321 9.88441 11.9348L10.2407 12.6754C10.304 12.8006 10.4003 12.9061 10.5193 12.9805C10.6383 13.0549 10.7754 13.0952 10.9157 13.0973H12.1813"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.7968 3.29102L15.6687 4.86602C15.7492 5.01409 15.7781 5.18473 15.7509 5.35107C15.7238 5.51741 15.6421 5.66998 15.5187 5.78477L12.9968 8.06289C12.9546 8.1032 12.9073 8.13787 12.8562 8.16602L11.703 8.80352C11.5931 8.86184 11.4712 8.89393 11.3468 8.89727H9.34053C9.19273 8.89802 9.04837 8.94188 8.92514 9.02348C8.80191 9.10508 8.70516 9.22087 8.64678 9.35664L7.86865 11.2035"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Student = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.75 9L12 3L23.25 9L12 15L0.75 9Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17.625 22.5V12L12 9"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20.625 10.397V15.5157C20.6243 15.675 20.5717 15.8297 20.475 15.9563C19.8469 16.8001 17.1844 19.8751 12 19.8751C6.81563 19.8751 4.15312 16.8001 3.525 15.9563C3.42834 15.8297 3.37567 15.675 3.375 15.5157V10.397"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Eye = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 4.375C3.75 4.375 1.25 10 1.25 10C1.25 10 3.75 15.625 10 15.625C16.25 15.625 18.75 10 18.75 10C18.75 10 16.25 4.375 10 4.375Z"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 13.125C11.7259 13.125 13.125 11.7259 13.125 10C13.125 8.27411 11.7259 6.875 10 6.875C8.27411 6.875 6.875 8.27411 6.875 10C6.875 11.7259 8.27411 13.125 10 13.125Z"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EyeSlash = () => (
  <svg
    width="21"
    height="21"
    viewBox="0 0 21 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.72461 3.16016L17.2246 16.9102"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.0758 12.3475C12.5024 12.8729 11.752 13.163 10.9743 13.16C10.3435 13.16 9.7276 12.969 9.2074 12.6124C8.68721 12.2557 8.28713 11.7499 8.05974 11.1616C7.83236 10.5733 7.78833 9.92994 7.93343 9.31612C8.07853 8.70231 8.40597 8.14677 8.87271 7.72253"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.75586 5.39465C3.56836 7.00403 2.22461 10.0353 2.22461 10.0353C2.22461 10.0353 4.72461 15.6603 10.9746 15.6603C12.4391 15.6722 13.8854 15.3348 15.1934 14.6759"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17.2715 13.2461C18.9746 11.7227 19.7246 10.0352 19.7246 10.0352C19.7246 10.0352 17.2246 4.41018 10.9746 4.41018C10.4328 4.40911 9.89182 4.45353 9.35742 4.54299"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.5605 6.96484C12.2251 7.09076 12.8307 7.42943 13.2859 7.92972C13.7411 8.43002 14.0212 9.06481 14.084 9.73828"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ExclamationMark = () => (
  <svg
    width="120"
    height="120"
    viewBox="0 0 120 120"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_10841_6165)">
      <path
        d="M60.3964 3.20001C92.7687 3.20001 117.6 28.6366 117.6 59.9963C117.6 91.3634 92.7762 116.8 60.404 116.8C28.0317 116.8 3.2002 91.3708 3.2002 60.0037C3.2002 28.6366 28.0242 3.20001 60.3964 3.20001ZM60.3964 91.8351C80.04 91.8351 93.0854 77.7877 93.0854 59.9963C93.0854 42.2048 80.04 28.1649 60.3964 28.1649C40.7529 28.1649 27.7075 42.2123 27.7075 60.0037C27.7075 77.7952 40.7529 91.8351 60.3964 91.8351Z"
        fill="#0F133A"
      />
      <path
        d="M54.1335 64.1969L51.2002 38.4H68.8002L65.8669 64.1969H54.1335ZM60.0002 80.8C57.4836 80.8 55.4159 80.095 53.8049 78.6778C52.194 77.2679 51.3925 75.5863 51.3925 73.633C51.3925 71.6797 52.194 69.9687 53.8049 68.6175C55.4078 67.2664 57.4756 66.5834 60.0002 66.5834C62.5248 66.5834 64.6406 67.259 66.2275 68.6175C67.8144 69.9687 68.6078 71.643 68.6078 73.633C68.6078 75.623 67.8144 77.2679 66.2275 78.6778C64.6406 80.095 62.5649 80.8 60.0002 80.8Z"
        fill="#0F133A"
      />
    </g>
    <defs>
      <clipPath id="clip0_10841_6165">
        <rect width="120" height="120" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const EnvelopeWhite = () => (
  <svg
    width="64"
    height="64"
    viewBox="0 0 64 64"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_10955_35982)">
      <g clip-path="url(#clip1_10955_35982)">
        <path
          d="M45.6027 19.8914C48.6584 19.8914 51.1596 22.3926 51.1596 25.4483V37.9417C51.1596 40.9973 48.6584 43.4985 45.6027 43.4985H18.5125C15.4568 43.4985 12.9556 40.9973 12.9556 37.9417V25.4483C12.9556 22.3926 15.4568 19.8914 18.5125 19.8914H45.5985M45.6027 8.04126L18.5125 8.04126C8.91386 8.04126 1.10547 15.8496 1.10547 25.4483L1.10547 37.9417C1.10547 47.5403 8.91386 55.3487 18.5125 55.3487H45.5985C55.1971 55.3487 63.0055 47.5403 63.0055 37.9417V25.4483C63.0097 15.8496 55.2013 8.04126 45.6027 8.04126Z"
          fill="white"
        />
        <path
          d="M51.1941 18.5073L48.3204 15.6336C48.1046 15.4178 47.821 15.3162 47.5417 15.3204C47.2582 15.3162 46.9788 15.4178 46.763 15.6336L28.4588 33.9378C28.0398 34.3568 28.0398 35.0382 28.4588 35.4614L31.3324 38.3351C31.5483 38.5509 31.8318 38.6525 32.1112 38.6483C32.3947 38.6525 32.674 38.5509 32.8899 38.3351L51.1941 20.0309C51.6131 19.6119 51.6131 18.9305 51.1941 18.5073Z"
          fill="white"
        />
        <path
          d="M32.7508 38.3521L35.6245 35.4784C35.8403 35.2626 35.9419 34.979 35.9377 34.6997C35.9419 34.4161 35.8403 34.1368 35.6245 33.9209L17.316 15.6167C16.897 15.1977 16.2157 15.1977 15.7924 15.6167L12.9188 18.4904C12.7029 18.7062 12.6014 18.9898 12.6056 19.2691C12.6014 19.5527 12.7029 19.832 12.9188 20.0478L31.223 38.3521C31.642 38.771 32.3234 38.771 32.7466 38.3521H32.7508Z"
          fill="white"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_10955_35982">
        <rect
          width="63.4828"
          height="63.4828"
          fill="white"
          transform="translate(0.257812)"
        />
      </clipPath>
      <clipPath id="clip1_10955_35982">
        <rect
          width="61.9042"
          height="47.3074"
          fill="white"
          transform="translate(1.10547 8.04126)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const Envelope = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3 5.25H21V18C21 18.1989 20.921 18.3897 20.7803 18.5303C20.6397 18.671 20.4489 18.75 20.25 18.75H3.75C3.55109 18.75 3.36032 18.671 3.21967 18.5303C3.07902 18.3897 3 18.1989 3 18V5.25Z"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M21 5.25L12 13.5L3 5.25"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CaretDown = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M26 12L16 22L6 12"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ArrowBack = () => (
  <svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g>
      <path
        d="M23 10H1"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 1L1 10L10 19"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_11574_1822">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ArrowNext = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_12705_4247)">
      <path
        d="M3.75 12H20.25"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.5 5.25L20.25 12L13.5 18.75"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_12705_4247">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const NoResults = () => (
  <svg
    width="124"
    height="124"
    viewBox="0 0 124 124"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g opacity="0.25">
      <path
        d="M69.137 21.4075C55.9631 8.23364 34.5929 8.23364 21.4116 21.4075C8.23779 34.5888 8.23779 55.9515 21.4116 69.1328C32.6481 80.3693 49.8307 82.0235 62.8257 74.1102C65.076 72.7392 67.1922 71.085 69.137 69.1402C71.0817 67.1955 72.7359 65.0719 74.1069 62.829C82.0276 49.834 80.3735 32.6514 69.137 21.4149V21.4075Z"
        fill="white"
      />
    </g>
    <path
      d="M45.2389 79.5422C43.9573 79.5422 42.6757 79.4677 41.4015 79.3261C33.6298 78.4469 26.5884 75.0491 21.0447 69.5053C14.577 63.0376 11.0078 54.4314 11.0078 45.2738C11.0078 36.1162 14.5695 27.5175 21.0447 21.0423C27.5198 14.5672 36.126 11.0055 45.2762 11.0055C54.4263 11.0055 63.0325 14.5672 69.5077 21.0423C75.0514 26.5861 78.4492 33.6275 79.3284 41.3992C80.1853 48.9473 78.4864 56.6519 74.5596 63.0973C73.1513 65.4072 71.4524 67.568 69.5151 69.5053C67.5778 71.4427 65.4169 73.1416 63.107 74.5498C57.7496 77.8135 51.5278 79.5347 45.2464 79.5347L45.2389 79.5422ZM45.2762 12.071C36.7668 12.071 28.2575 15.3123 21.7823 21.7875C15.5084 28.0614 12.051 36.4068 12.051 45.2813C12.051 54.1558 15.5084 62.5012 21.7823 68.7751C32.6612 79.6614 49.4265 81.6733 62.5556 73.6706C64.7985 72.2996 66.8923 70.6528 68.77 68.7751C70.6477 66.8974 72.2944 64.8036 73.6655 62.5608C81.6681 49.4391 79.6563 32.6738 68.77 21.7875C62.2948 15.3123 53.7855 12.071 45.2762 12.071Z"
      fill="#69D8D7"
    />
    <path
      d="M74.1102 62.829C72.7392 65.0793 71.085 67.1955 69.1402 69.1402C67.1955 71.085 65.0793 72.7392 62.829 74.1102C49.8415 82.0309 32.6514 80.3693 21.4149 69.1402C8.24108 55.9664 8.24108 34.5962 21.4149 21.4149C34.5962 8.23363 55.9664 8.23363 69.1402 21.4149C80.3767 32.6514 82.0309 49.8415 74.1102 62.829ZM82.1352 19.88C80.6152 17.6818 78.879 15.5806 76.9193 13.6283C59.4461 -3.84486 31.109 -3.84486 13.6283 13.6283C-3.84486 31.109 -3.84486 59.4387 13.6283 76.9193C29.1791 92.4701 53.3287 94.1839 70.7721 82.0607C72.9329 80.5556 74.9895 78.8418 76.9193 76.9193C78.8492 74.9969 80.563 72.9329 82.0607 70.7721C82.0905 70.7348 82.1129 70.6975 82.1352 70.6603C92.6564 55.4225 92.6564 35.1178 82.1352 19.88Z"
      fill="#69D8D7"
    />
    <path
      d="M45.259 90.5477C43.9922 90.5477 42.7181 90.4956 41.4514 90.3838C30.8259 89.4896 20.8188 84.8326 13.2632 77.2845C4.7092 68.7304 0 57.3673 0 45.2739C0 33.1805 4.7092 21.8098 13.2632 13.2632C21.8173 4.7092 33.1879 0 45.2813 0C57.3747 0 68.7453 4.7092 77.2919 13.2632C79.2442 15.2155 81.0176 17.3465 82.5674 19.5894C87.7907 27.1524 90.5477 36.0343 90.5477 45.2813C90.5477 54.5283 87.7833 63.4028 82.5674 70.9658C82.5376 71.0031 82.5153 71.0478 82.4855 71.085C80.9579 73.2906 79.2069 75.377 77.2919 77.2919C75.377 79.2069 73.2906 80.9579 71.0776 82.4929C63.5145 87.7535 54.4687 90.5552 45.259 90.5552V90.5477ZM45.2739 1.06553C33.9479 1.06553 22.622 5.37981 13.9935 14.0009C-3.24875 31.2506 -3.24875 59.3046 13.9935 76.5543C29.1493 91.7101 52.904 93.8486 70.474 81.636C72.6349 80.1309 74.6765 78.4245 76.5468 76.5543C78.4171 74.684 80.1309 72.6423 81.6286 70.4815C81.6584 70.4442 81.6807 70.407 81.7031 70.3772C92.0678 55.3629 92.0678 35.1923 81.7031 20.1855C80.1905 17.9873 78.4543 15.9084 76.5468 14.0009C67.9257 5.37981 56.5998 1.06553 45.2739 1.06553Z"
      fill="#69D8D7"
    />
    <path
      d="M85.5273 74.2369L74.2461 85.5181L70.7812 82.0533C72.9421 80.5481 74.9987 78.8344 76.9285 76.9119C78.1356 75.7048 79.2533 74.453 80.2891 73.1565C80.4977 72.8957 80.6989 72.6349 80.9001 72.3741C81.1981 71.9792 81.4962 71.5768 81.7793 71.1745C81.8762 71.0403 81.9656 70.9062 82.0625 70.7721L85.5273 74.2369Z"
      fill="#69D8D7"
    />
    <path
      d="M122.161 116.24L116.237 122.163C114.754 123.646 112.355 123.646 110.872 122.163L89.0547 100.346L100.336 89.0649L122.153 110.882C123.636 112.365 123.636 114.764 122.153 116.247L122.161 116.24Z"
      fill="#69D8D7"
    />
    <g opacity="0.75">
      <path
        d="M100.989 88.4241L100.348 89.0649L88.4264 100.987C87.5769 101.836 86.1985 101.836 85.3416 100.987L82.1375 97.7828L73.6058 89.2512C72.7564 88.4017 72.7564 87.0232 73.6058 86.1663L86.1612 73.611C87.0107 72.7615 88.3891 72.7615 89.246 73.611L100.989 85.3541C101.839 86.2036 101.839 87.5821 100.989 88.439V88.4241Z"
        fill="white"
      />
    </g>
    <path
      d="M86.878 102.142C86.1553 102.142 85.4772 101.859 84.9705 101.352L73.2273 89.6089C72.7206 89.1022 72.4375 88.4241 72.4375 87.7013C72.4375 86.9786 72.7206 86.3005 73.2273 85.7938L85.7827 73.2384C86.2894 72.7318 86.9749 72.4486 87.6902 72.4486C88.4056 72.4486 89.0911 72.7318 89.5978 73.2384L101.341 84.9816C102.392 86.0323 102.392 87.746 101.341 88.8041L88.7781 101.359C88.2714 101.866 87.5934 102.149 86.8706 102.149L86.878 102.142ZM87.6977 73.4843C87.2581 73.4843 86.8408 73.6557 86.5278 73.9687L73.9725 86.524C73.6595 86.837 73.4881 87.2543 73.4881 87.6939C73.4881 88.1335 73.6595 88.5508 73.9725 88.8637L85.7157 100.607C86.0286 100.92 86.4459 101.091 86.8855 101.091C87.3251 101.091 87.7424 100.92 88.0553 100.607L100.618 88.0515C101.259 87.4033 101.259 86.3527 100.618 85.7118L88.875 73.9687C88.562 73.6557 88.1448 73.4843 87.7051 73.4843H87.6977Z"
      fill="#69D8D7"
    />
    <path
      d="M61.6546 58.2688L58.2718 61.6517C57.1466 62.7769 55.3136 62.7769 54.1885 61.6517L45.2768 52.74L36.3651 61.6517C35.2399 62.7769 33.4144 62.7769 32.2892 61.6517L28.9064 58.2688C27.7812 57.1437 27.7812 55.3107 28.9064 54.1856L37.8181 45.2813L28.9064 36.3696C27.7812 35.2444 27.7812 33.4189 28.9064 32.2863L32.2892 28.9034C33.4144 27.7783 35.2399 27.7783 36.3651 28.9034L45.2768 37.8151L54.1885 28.9034C55.3136 27.7783 57.1466 27.7783 58.2718 28.9034L61.6546 32.2863C62.7872 33.4114 62.7798 35.237 61.6546 36.3696L52.7429 45.2813L61.6546 54.1856C62.7872 55.3107 62.7872 57.1437 61.6546 58.2688Z"
      fill="#69D8D7"
    />
  </svg>
);

export const CreditCardThin = () => (
  <svg
    width="34"
    height="35"
    viewBox="0 0 34 35"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M29.75 7.9375H4.25C3.6632 7.9375 3.1875 8.4132 3.1875 9V26C3.1875 26.5868 3.6632 27.0625 4.25 27.0625H29.75C30.3368 27.0625 30.8125 26.5868 30.8125 26V9C30.8125 8.4132 30.3368 7.9375 29.75 7.9375Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22.3125 22.8125H26.5625"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.9375 22.8125H18.0625"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.1875 13.3691H30.8125"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CreditCard = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.5 4.375H2.5C2.15482 4.375 1.875 4.65482 1.875 5V15C1.875 15.3452 2.15482 15.625 2.5 15.625H17.5C17.8452 15.625 18.125 15.3452 18.125 15V5C18.125 4.65482 17.8452 4.375 17.5 4.375Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.125 13.125H15.625"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.375 13.125H10.625"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.875 7.57031H18.125"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Mastercard = () => (
  <svg
    width="38"
    height="22"
    viewBox="0 0 38 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M22.5149 4.8623H15.4844V17.5972H22.5149V4.8623Z" fill="#FF5F00" />
    <path
      d="M15.9313 11.2299C15.9302 10.0034 16.206 8.79276 16.7376 7.68959C17.2693 6.58642 18.043 5.61964 19.0002 4.86243C17.8149 3.92331 16.3914 3.33929 14.8923 3.17712C13.3933 3.01494 11.8792 3.28115 10.5232 3.94532C9.16719 4.6095 8.0239 5.64484 7.22404 6.93301C6.42417 8.22117 6 9.71019 6 11.2299C6 12.7495 6.42417 14.2385 7.22404 15.5267C8.0239 16.8149 9.16719 17.8502 10.5232 18.5144C11.8792 19.1786 13.3933 19.4448 14.8923 19.2826C16.3914 19.1204 17.8149 18.5364 19.0002 17.5973C18.043 16.8401 17.2693 15.8733 16.7377 14.7701C16.206 13.667 15.9302 12.4563 15.9313 11.2299Z"
      fill="#EB001B"
    />
    <path
      d="M31.9998 11.2299C31.9999 12.7495 31.5757 14.2385 30.7759 15.5267C29.9761 16.8148 28.8328 17.8502 27.4768 18.5144C26.1208 19.1786 24.6068 19.4448 23.1078 19.2826C21.6088 19.1204 20.1853 18.5364 19 17.5973C19.9563 16.8393 20.7294 15.8724 21.261 14.7694C21.7926 13.6664 22.0688 12.4561 22.0688 11.2299C22.0688 10.0036 21.7926 8.79333 21.261 7.69034C20.7294 6.58734 19.9563 5.6204 19 4.86243C20.1853 3.92331 21.6088 3.33929 23.1078 3.17711C24.6068 3.01494 26.1208 3.28116 27.4768 3.94534C28.8328 4.60952 29.9761 5.64487 30.7759 6.93303C31.5757 8.2212 31.9999 9.71021 31.9998 11.2299Z"
      fill="#F79E1B"
    />
    <path
      d="M31.2336 16.2484V15.9877H31.3379V15.9346H31.0723V15.9877H31.1766V16.2484H31.2336ZM31.7493 16.2484V15.9341H31.6679L31.5742 16.1503L31.4805 15.9341H31.3991V16.2484H31.4566V16.0113L31.5444 16.2158H31.604L31.6918 16.0108V16.2484H31.7493Z"
      fill="#F79E1B"
    />
  </svg>
);

export const Visa = () => (
  <svg
    width="38"
    height="22"
    viewBox="0 0 38 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.3831 6.42334L13.5586 15.819H16.4766L18.3026 6.42334H15.3831Z"
      fill="#1431C2"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.0181 6.42532L8.1521 12.8419L7.84654 11.8731C7.28133 10.7057 5.67676 9.02915 3.79297 7.97258L6.41357 15.819L9.50982 15.8144L14.1181 6.42334L11.0181 6.42532Z"
      fill="#1431C2"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.73265 7.19152C6.566 6.61555 6.08304 6.44389 5.48357 6.42334H1.03882L1.00195 6.61157C4.46087 7.36583 6.7496 9.18321 7.69931 11.3684L6.73265 7.19152Z"
      fill="#1431C2"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M24.1475 8.17782C25.0906 8.16439 25.7743 8.35111 26.3052 8.54455L26.5654 8.65537L26.9554 6.57859C26.3845 6.38448 25.4896 6.17627 24.3729 6.17627C21.5242 6.17627 19.5163 7.47593 19.5007 9.33845C19.4821 10.7147 20.931 11.4831 22.0254 11.9418C23.1487 12.412 23.5254 12.7109 23.5202 13.1307C23.5113 13.7721 22.6245 14.0663 21.7963 14.0663C20.6418 14.0663 20.0286 13.9219 19.0818 13.5645L18.7103 13.4114L18.3047 15.556C18.9794 15.824 20.2244 16.0544 21.5167 16.0665C24.5471 16.0665 26.5165 14.7823 26.5373 12.7921C26.5499 11.7034 25.781 10.8725 24.1149 10.1908C23.1065 9.74615 22.4896 9.45062 22.4955 9.00195C22.4955 8.60365 23.019 8.17782 24.1475 8.17782Z"
      fill="#1431C2"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M32.329 6.42334H34.6109L37.0006 15.819H34.2607C34.2607 15.819 33.9888 14.739 33.9007 14.4108C33.6735 14.4108 32.7225 14.4097 31.8272 14.4087C31.0308 14.4077 30.2784 14.4069 30.1186 14.4069C30.0039 14.6606 29.498 15.819 29.498 15.819H26.3965L30.7817 7.20357C31.0932 6.59072 31.6203 6.42334 32.329 6.42334ZM32.1455 9.85522C32.1455 9.85522 31.2119 11.9429 30.9688 12.4826H33.4224L32.738 9.76824L32.5389 8.95769C32.4643 9.13079 32.3647 9.35545 32.2843 9.53681C32.2003 9.7262 32.1373 9.86835 32.1455 9.85522Z"
      fill="#1431C2"
    />
  </svg>
);

export const Amex = () => (
  <svg
    width="37"
    height="22"
    viewBox="0 0 37 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="37" height="22" rx="2" fill="#0070D1" />
    <path
      d="M3.32281 7.88881L2.61366 6.11807L1.90857 7.88881H3.32281ZM18.9452 7.1837C18.8028 7.27226 18.6344 7.2752 18.4327 7.2752H17.174V6.28862H18.4498C18.6304 6.28862 18.8188 6.29692 18.9411 6.36873C19.0755 6.43344 19.1587 6.57117 19.1587 6.76141C19.1587 6.95554 19.0796 7.11176 18.9452 7.1837ZM27.9243 7.88881L27.2073 6.11807L26.4942 7.88881H27.9243ZM11.1867 9.80544H10.1245L10.1206 6.32667L8.61825 9.80544H7.70855L6.20227 6.32358V9.80544H4.09497L3.69686 8.8147H1.53962L1.13746 9.80544H0.012159L1.86752 5.36366H3.40688L5.16902 9.56911V5.36366H6.86004L8.21596 8.37687L9.46154 5.36366H11.1866L11.1867 9.80544ZM15.42 9.80544H11.9588V5.36366H15.42V6.28862H12.995V7.08925H15.3618V7.99974H12.995V8.88678H15.42V9.80544ZM20.3001 6.55991C20.3001 7.2681 19.8388 7.63399 19.57 7.74385C19.7967 7.83227 19.9903 7.98848 20.0825 8.1179C20.2288 8.33883 20.254 8.53617 20.254 8.93287V9.80544H19.209L19.2051 9.2453C19.2051 8.97802 19.2301 8.59365 19.0415 8.37996C18.8901 8.22374 18.6594 8.18985 18.2864 8.18985H17.1741V9.80544H16.1381V5.36366H18.5212C19.0507 5.36366 19.4408 5.37799 19.7758 5.57641C20.1036 5.77482 20.3001 6.06448 20.3001 6.55991ZM21.9581 9.80544H20.9009V5.36366H21.9581V9.80544ZM34.2228 9.80544H32.7545L30.7907 6.47163V9.80544H28.6806L28.2774 8.8147H26.1251L25.734 9.80544H24.5216C24.018 9.80544 23.3803 9.6913 23.0192 9.31416C22.6551 8.93702 22.4657 8.42618 22.4657 7.61845C22.4657 6.95969 22.5789 6.35748 23.0242 5.8816C23.3592 5.52711 23.8837 5.36366 24.5977 5.36366H25.6007V6.31541H24.6187C24.2406 6.31541 24.0271 6.37302 23.8215 6.57854C23.6448 6.76557 23.5236 7.11912 23.5236 7.58468C23.5236 8.06056 23.6159 8.40367 23.8085 8.62781C23.968 8.80358 24.2579 8.8569 24.5306 8.8569H24.9959L26.4562 5.36379H28.0086L29.7628 9.56509V5.36379H31.3403L33.1615 8.45726V5.36379H34.2228V9.80544ZM0 10.6779H1.77038L2.16953 9.6913H3.06316L3.46126 10.6779H6.94436V9.92361L7.25527 10.6811H9.06343L9.37434 9.91236V10.6779H18.0305L18.0265 9.0584H18.1939C18.3112 9.06256 18.3455 9.07368 18.3455 9.27209V10.6779H22.8225V10.3009C23.1836 10.4992 23.7452 10.6779 24.4843 10.6779H26.3678L26.7709 9.6913H27.6645L28.0587 10.6779H31.6882V9.74073L32.2379 10.6779H35.1463V4.48291H32.2679V5.21454L31.8648 4.48291H28.9113V5.21454L28.5411 4.48291H24.5515C23.8837 4.48291 23.2967 4.57843 22.8225 4.84464V4.48291H20.0693V4.84464C19.7675 4.57026 19.3564 4.48291 18.8992 4.48291H8.84078L8.16589 6.08297L7.47282 4.48291H4.30468V5.21454L3.95665 4.48291H1.25473L0 7.42834V10.6779Z"
      fill="white"
    />
    <path
      d="M36.8618 13.9543H34.9735C34.785 13.9543 34.6597 13.9615 34.5542 14.0345C34.4449 14.1065 34.4028 14.2133 34.4028 14.3542C34.4028 14.5218 34.4951 14.6358 34.6294 14.6851C34.7387 14.7241 34.8561 14.7355 35.0287 14.7355L35.5902 14.7509C36.1569 14.7652 36.5351 14.865 36.7657 15.1085C36.8077 15.1424 36.8329 15.1804 36.8618 15.2185V13.9543ZM36.8618 16.8834C36.6102 17.2605 36.1197 17.4517 35.4558 17.4517H33.455V16.499H35.4477C35.6454 16.499 35.7837 16.4723 35.867 16.389C35.9392 16.3203 35.9895 16.2205 35.9895 16.0992C35.9895 15.9698 35.9392 15.867 35.863 15.8054C35.7878 15.7376 35.6784 15.7068 35.4979 15.7068C34.5251 15.6729 33.3114 15.7376 33.3114 14.3317C33.3114 13.6873 33.7107 13.009 34.7979 13.009H36.8618V12.125H34.9442C34.3656 12.125 33.9452 12.267 33.6475 12.4878V12.125H30.8113C30.3578 12.125 29.8254 12.2402 29.5736 12.4878V12.125H24.5089V12.4878C24.1059 12.1898 23.4257 12.125 23.1118 12.125H19.7711V12.4878C19.4522 12.1714 18.7431 12.125 18.3108 12.125H14.572L13.7164 13.0738L12.9151 12.125H7.33008V18.3243H12.81L13.6916 17.3605L14.5221 18.3243L17.8999 18.3273V16.869H18.232C18.6802 16.8761 19.2088 16.8576 19.6751 16.6511V18.3241H22.4612V16.7084H22.5957C22.7672 16.7084 22.784 16.7156 22.784 16.8913V18.324H31.2478C31.7851 18.324 32.3468 18.1831 32.6578 17.9273V18.324H35.3425C35.9011 18.324 36.4467 18.2437 36.8618 18.0382V16.8834ZM32.7289 15.1085C32.9307 15.3224 33.0388 15.5925 33.0388 16.0498C33.0388 17.0056 32.4561 17.4517 31.4112 17.4517H29.3932V16.499H31.4031C31.5996 16.499 31.739 16.4723 31.8263 16.389C31.8976 16.3203 31.9487 16.2205 31.9487 16.0992C31.9487 15.9698 31.8934 15.867 31.8222 15.8054C31.743 15.7376 31.6337 15.7068 31.4533 15.7068C30.4844 15.6729 29.2709 15.7376 29.2709 14.3317C29.2709 13.6873 29.666 13.009 30.7523 13.009H32.8294V13.9546H30.9288C30.7404 13.9546 30.6179 13.9618 30.5137 14.0348C30.4002 14.1067 30.3581 14.2135 30.3581 14.3545C30.3581 14.5221 30.4544 14.6361 30.5848 14.6854C30.6941 14.7244 30.8115 14.7358 30.988 14.7358L31.5457 14.7512C32.1082 14.7652 32.4943 14.8649 32.7289 15.1085ZM23.3798 14.8341C23.2413 14.9184 23.0699 14.9256 22.8682 14.9256H21.6096V13.9278H22.8854C23.0699 13.9278 23.2545 13.9318 23.3798 14.008C23.5141 14.08 23.5944 14.2175 23.5944 14.4077C23.5944 14.5978 23.5141 14.7509 23.3798 14.8341ZM24.0056 15.387C24.2362 15.4742 24.4247 15.6306 24.5131 15.76C24.6594 15.9769 24.6806 16.1793 24.6848 16.5709V17.4517H23.6446V16.8958C23.6446 16.6285 23.6697 16.2328 23.4771 16.0262C23.3257 15.867 23.095 15.829 22.717 15.829H21.6097V17.4517H20.5686V13.0088H22.9607C23.4852 13.0088 23.8672 13.0325 24.2073 13.2185C24.5343 13.4209 24.74 13.6983 24.74 14.2051C24.7398 14.9142 24.2783 15.2761 24.0056 15.387ZM25.3144 13.0088H28.7724V13.9276H26.3462V14.7354H28.7132V15.6418H26.3462V16.5258L28.7724 16.5298V17.4517H25.3144V13.0088ZM18.324 15.0592H16.9851V13.9278H18.3361C18.7101 13.9278 18.9698 14.084 18.9698 14.4725C18.9698 14.8567 18.7222 15.0592 18.324 15.0592ZM15.9532 17.0476L14.3624 15.2379L15.9532 13.4856V17.0476ZM11.8451 16.5258H9.29775V15.6418H11.5724V14.7354H9.29775V13.9276H11.8953L13.0286 15.2224L11.8451 16.5258ZM20.0821 14.4725C20.0821 15.7067 19.1835 15.9615 18.2779 15.9615H16.9851V17.4517H14.9713L13.6955 15.9809L12.3697 17.4517H8.26567V13.0088H12.4328L13.7075 14.4651L15.0254 13.0088H18.3361C19.1583 13.0088 20.0821 13.2421 20.0821 14.4725Z"
      fill="white"
    />
  </svg>
);

export const Plus = () => (
  <svg
    width="18"
    height="19"
    viewBox="0 0 18 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 1.5V17.5"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1 9.5H17"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Pencil = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.69063 20.2499H4.5C4.30109 20.2499 4.11033 20.1709 3.96967 20.0302C3.82902 19.8896 3.75 19.6988 3.75 19.4999L3.75 15.3093C3.74966 15.2119 3.76853 15.1154 3.80553 15.0253C3.84253 14.9352 3.89694 14.8533 3.96563 14.7843L15.2156 3.53429C15.2854 3.46343 15.3686 3.40715 15.4603 3.36874C15.5521 3.33033 15.6505 3.31055 15.75 3.31055C15.8495 3.31055 15.9479 3.33033 16.0397 3.36874C16.1314 3.40715 16.2146 3.46343 16.2844 3.53429L20.4656 7.71554C20.5365 7.78533 20.5928 7.86851 20.6312 7.96026C20.6696 8.052 20.6894 8.15046 20.6894 8.24992C20.6894 8.34938 20.6696 8.44784 20.6312 8.53958C20.5928 8.63132 20.5365 8.71451 20.4656 8.78429L9.21563 20.0343C9.1466 20.103 9.06469 20.1574 8.9746 20.1944C8.88452 20.2314 8.78802 20.2503 8.69063 20.2499V20.2499Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.75 6L18 11.25"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.375 8.625L6.375 17.625"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.95312 20.2031L3.79688 15.0469"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Cookie = () => (
  <svg
    width="38"
    height="36"
    viewBox="0 0 38 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.9627 0C9.69805 0.580033 8.49391 1.30224 7.37493 2.15857C3.97554 4.76007 1.56567 8.44372 0.543694 12.6005C-0.478277 16.7574 -0.0511549 21.1385 1.75445 25.0197C3.56006 28.9008 6.63605 32.0497 10.4739 33.9456C14.3118 35.8415 18.6817 36.371 22.8613 35.4466C27.0409 34.5222 30.7799 32.1992 33.4603 28.8616C35.9237 25.794 37.3573 22.0383 37.5735 18.1272C37.4124 18.152 37.2484 18.1688 37.082 18.1771C37.0629 18.1781 37.0437 18.1789 37.0246 18.1796C36.7992 21.9476 35.4101 25.5633 33.0354 28.5203C30.4327 31.7612 26.8021 34.0169 22.7436 34.9145C18.6852 35.8121 14.4419 35.298 10.7153 33.457C6.98866 31.616 4.00183 28.5585 2.24856 24.7898C0.495287 21.0211 0.0805458 16.767 1.07289 12.7307C2.06524 8.69431 4.40526 5.11744 7.70613 2.59134C8.74482 1.79645 9.85908 1.12058 11.028 0.570555C10.9952 0.38411 10.9732 0.193716 10.9627 0Z"
      fill="#05BFBE"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.064 1.44038C16.7851 1.31205 16.5243 1.15364 16.285 0.969727C12.1211 1.96274 8.4233 4.36148 5.84384 7.77004C5.29336 8.49745 4.80195 9.26011 4.37154 10.0509C5.02699 10.2739 5.49852 10.8946 5.49852 11.6255C5.49852 12.5439 4.75405 13.2883 3.8357 13.2883C3.5554 13.2883 3.2913 13.219 3.05962 13.0965C2.26289 15.5421 1.99163 18.1485 2.29219 20.7462C2.8261 25.3607 5.12405 29.5994 8.71624 32.5959C11.4953 34.4112 14.7202 35.449 18.0484 35.5988C21.3765 35.7487 24.6837 35.0051 27.6183 33.447C29.7567 32.3116 31.6391 30.7748 33.1648 28.9315C32.2895 28.7616 31.6286 27.991 31.6286 27.066C31.6286 26.0164 32.4794 25.1656 33.529 25.1656C34.1987 25.1656 34.7876 25.5121 35.126 26.0356C36.5697 23.3865 37.3289 20.4213 37.3297 17.4042C37.3302 16.7976 37.2999 16.1927 37.2394 15.5917C36.4428 16.1706 35.436 16.4652 34.3803 16.3468C33.4444 16.2418 32.6179 15.8288 31.9905 15.2201C30.8463 16.1309 29.3551 16.6061 27.7869 16.4302C24.6447 16.0777 22.3832 13.2447 22.7357 10.1025C22.7408 10.057 22.7464 10.0117 22.7525 9.96662C22.3493 10.0116 21.9355 10.0127 21.5165 9.96565C18.3743 9.61316 16.123 6.6892 16.4881 3.43478C16.5682 2.72012 16.7679 2.04896 17.064 1.44038ZM11.9122 15.6638C12.5682 15.6638 13.1 15.132 13.1 14.476C13.1 13.8201 12.5682 13.2883 11.9122 13.2883C11.2563 13.2883 10.7245 13.8201 10.7245 14.476C10.7245 15.132 11.2563 15.6638 11.9122 15.6638ZM10.0119 29.9165C10.9302 29.9165 11.6747 29.1721 11.6747 28.2537C11.6747 27.3354 10.9302 26.5909 10.0119 26.5909C9.09353 26.5909 8.34905 27.3354 8.34905 28.2537C8.34905 29.1721 9.09353 29.9165 10.0119 29.9165ZM21.1766 26.5909C22.4885 26.5909 23.552 25.5274 23.552 24.2154C23.552 22.9035 22.4885 21.84 21.1766 21.84C19.8646 21.84 18.8011 22.9035 18.8011 24.2154C18.8011 25.5274 19.8646 26.5909 21.1766 26.5909Z"
      fill="#05BFBE"
    />
  </svg>
);

export const User = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 15C15.3137 15 18 12.3137 18 9C18 5.68629 15.3137 3 12 3C8.68629 3 6 5.68629 6 9C6 12.3137 8.68629 15 12 15Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeMiterlimit="10"
    />
    <path
      d="M2.90625 20.2499C3.82775 18.6534 5.15328 17.3277 6.74958 16.406C8.34588 15.4843 10.1567 14.999 12 14.999C13.8433 14.999 15.6541 15.4843 17.2504 16.406C18.8467 17.3277 20.1722 18.6534 21.0938 20.2499"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DoorOpen = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_11753_5033)">
      <path
        d="M2.75 21H22.25"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.75 21V3.75C5.75 3.55109 5.82902 3.36032 5.96967 3.21967C6.11032 3.07902 6.30109 3 6.5 3H18.5C18.6989 3 18.8897 3.07902 19.0303 3.21967C19.171 3.36032 19.25 3.55109 19.25 3.75V21"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.5 3C15.6989 3 15.8897 3.07902 16.0303 3.21967C16.171 3.36032 16.25 3.55109 16.25 3.75V21"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.875 13.5C13.4963 13.5 14 12.9963 14 12.375C14 11.7537 13.4963 11.25 12.875 11.25C12.2537 11.25 11.75 11.7537 11.75 12.375C11.75 12.9963 12.2537 13.5 12.875 13.5Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_11753_5033">
        <rect width="24" height="24" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);

export const LogoutCircles = () => (
  <svg
    width="120"
    height="120"
    viewBox="0 0 120 120"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M84.9748 28.7234C82.7708 26.803 81.5668 24.4199 81.3728 21.5691C81.1737 18.6685 82.0345 16.1212 83.9549 13.9122C85.8753 11.7082 88.2584 10.5042 91.1092 10.3102C94.0098 10.1112 96.5571 10.9719 98.7661 12.8923C100.97 14.8127 102.174 17.2207 102.373 20.1213C102.567 22.9721 101.706 25.4995 99.786 27.7035C97.8656 29.9075 95.4576 31.1115 92.557 31.3105C89.7062 31.5046 87.1788 30.6439 84.9748 28.7234Z"
      fill="#05BFBE"
    />
    <path
      d="M17.123 72.3759C17.123 51.1417 33.9243 34.8479 54.6461 34.8479C75.3678 34.8479 92.174 51.1367 92.174 72.3709C92.174 93.6101 75.3728 109.899 54.651 109.899C33.9293 109.899 17.123 93.6151 17.123 72.3759ZM75.6763 72.3759C75.6763 59.4901 66.3975 50.9328 54.6461 50.9328C42.8946 50.9328 33.6159 59.4901 33.6159 72.3759C33.6159 85.2617 42.8946 93.819 54.6461 93.819C66.3975 93.819 75.6763 85.2617 75.6763 72.3759Z"
      fill="#05BFBE"
    />
  </svg>
);

export const LogOut = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.3125 8.0625L20.25 12L16.3125 15.9375"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.75 12H20.25"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.75 20.25H4.5C4.30109 20.25 4.11032 20.171 3.96967 20.0303C3.82902 19.8897 3.75 19.6989 3.75 19.5V4.5C3.75 4.30109 3.82902 4.11032 3.96967 3.96967C4.11032 3.82902 4.30109 3.75 4.5 3.75H9.75"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Bundles = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.81641 8.24999C4.67206 6.38318 6.1404 4.86512 7.9777 3.94781C9.81499 3.03049 11.9107 2.76909 13.9171 3.20698C15.9234 3.64488 17.7197 4.75574 19.0077 6.35518C20.2957 7.95461 20.9979 9.94642 20.9979 12C20.9979 14.0536 20.2957 16.0454 19.0077 17.6448C17.7197 19.2442 15.9234 20.3551 13.9171 20.793C11.9107 21.2309 9.81499 20.9695 7.9777 20.0522C6.1404 19.1349 4.67206 17.6168 3.81641 15.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.33203 9.58289C7.83004 8.44206 8.68464 7.51436 9.75399 6.95378C10.8233 6.3932 12.0431 6.23345 13.2108 6.50106C14.3786 6.76866 15.424 7.44752 16.1736 8.42495C16.9233 9.40238 17.332 10.6196 17.332 11.8746C17.332 13.1295 16.9233 14.3467 16.1736 15.3242C15.424 16.3016 14.3786 16.9804 13.2108 17.248"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Wallet = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.75 5.99951V17.9995C3.75 18.3973 3.90804 18.7789 4.18934 19.0602C4.47064 19.3415 4.85218 19.4995 5.25 19.4995H20.25C20.4489 19.4995 20.6397 19.4205 20.7803 19.2798C20.921 19.1392 21 18.9484 21 18.7495V8.24951C21 8.0506 20.921 7.85983 20.7803 7.71918C20.6397 7.57853 20.4489 7.49951 20.25 7.49951H5.25C4.85218 7.49951 4.47064 7.34148 4.18934 7.06017C3.90804 6.77887 3.75 6.39734 3.75 5.99951ZM3.75 5.99951C3.75 5.60169 3.90804 5.22016 4.18934 4.93885C4.47064 4.65755 4.85218 4.49951 5.25 4.49951H18"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.875 14.625C17.4963 14.625 18 14.1213 18 13.5C18 12.8787 17.4963 12.375 16.875 12.375C16.2537 12.375 15.75 12.8787 15.75 13.5C15.75 14.1213 16.2537 14.625 16.875 14.625Z"
      fill="currentColor"
    />
  </svg>
);

export const Sliders = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.875 16.125H3.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20.25 16.125H17.625"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.75 18C16.7855 18 17.625 17.1605 17.625 16.125C17.625 15.0895 16.7855 14.25 15.75 14.25C14.7145 14.25 13.875 15.0895 13.875 16.125C13.875 17.1605 14.7145 18 15.75 18Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.875 7.875H3.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20.25 7.875H11.625"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.75 9.75C10.7855 9.75 11.625 8.91053 11.625 7.875C11.625 6.83947 10.7855 6 9.75 6C8.71447 6 7.875 6.83947 7.875 7.875C7.875 8.91053 8.71447 9.75 9.75 9.75Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PaymentHistory = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 6.25V10"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.25 11.875L10 10"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.60938 7.78906H2.48438V4.66406"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.14063 14.8594C6.10189 15.8214 7.32689 16.4768 8.66066 16.7425C9.99443 17.0082 11.3771 16.8724 12.6336 16.3522C13.8902 15.832 14.9642 14.9508 15.7199 13.8201C16.4756 12.6894 16.879 11.36 16.879 10C16.879 8.64002 16.4756 7.31058 15.7199 6.17987C14.9642 5.04917 13.8902 4.16798 12.6336 3.64779C11.3771 3.12761 9.99443 2.99179 8.66066 3.25752C7.32689 3.52324 6.10189 4.17858 5.14063 5.14063L2.48438 7.78907"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Share = () => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.81641 12.5C7.19712 12.5 8.31641 11.3807 8.31641 10C8.31641 8.61929 7.19712 7.5 5.81641 7.5C4.43569 7.5 3.31641 8.61929 3.31641 10C3.31641 11.3807 4.43569 12.5 5.81641 12.5Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.5664 18.125C15.9471 18.125 17.0664 17.0057 17.0664 15.625C17.0664 14.2443 15.9471 13.125 14.5664 13.125C13.1857 13.125 12.0664 14.2443 12.0664 15.625C12.0664 17.0057 13.1857 18.125 14.5664 18.125Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.5664 6.875C15.9471 6.875 17.0664 5.75571 17.0664 4.375C17.0664 2.99429 15.9471 1.875 14.5664 1.875C13.1857 1.875 12.0664 2.99429 12.0664 4.375C12.0664 5.75571 13.1857 6.875 14.5664 6.875Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.4648 5.72681L7.91797 8.64868"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.91797 11.3518L12.4648 14.2737"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Voicemail = () => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.19141 13.75C7.26247 13.75 8.94141 12.0711 8.94141 10C8.94141 7.92893 7.26247 6.25 5.19141 6.25C3.12034 6.25 1.44141 7.92893 1.44141 10C1.44141 12.0711 3.12034 13.75 5.19141 13.75Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.4414 13.75C18.5125 13.75 20.1914 12.0711 20.1914 10C20.1914 7.92893 18.5125 6.25 16.4414 6.25C14.3703 6.25 12.6914 7.92893 12.6914 10C12.6914 12.0711 14.3703 13.75 16.4414 13.75Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.19141 13.75H16.4414"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Bars = () => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.36802 16.6113L3.375 11.1035L7.65885 11.1089"
      stroke="currentColor"
      strokeWidth="0.979167"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17.444 16.6293L2.14453 16.6099"
      stroke="currentColor"
      strokeWidth="0.979167"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.65242 16.6172L7.66406 7.4375L11.9479 7.44293"
      stroke="currentColor"
      strokeWidth="0.979167"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.237 3.77643L11.9531 3.771L11.9368 16.6225L16.2207 16.628L16.237 3.77643Z"
      stroke="currentColor"
      strokeWidth="0.979167"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Copy = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.125 13.125H16.875V3.125H6.875V6.875"
      stroke="black"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.125 6.875H3.125V16.875H13.125V6.875Z"
      stroke="black"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const UserThin = () => (
  <svg
    width="94"
    height="94"
    viewBox="0 0 94 94"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M47.0047 58.6007C59.8177 58.6007 70.2047 48.2137 70.2047 35.4007C70.2047 22.5877 59.8177 12.2007 47.0047 12.2007C34.1917 12.2007 23.8047 22.5877 23.8047 35.4007C23.8047 48.2137 34.1917 58.6007 47.0047 58.6007Z"
      stroke="#EFF1F7"
      strokeWidth="3"
      strokeMiterlimit="10"
    />
    <path
      d="M11.8359 78.9004C15.3991 72.7275 20.5245 67.6015 26.6968 64.0375C32.8692 60.4735 39.871 58.5972 46.9984 58.5972C54.1259 58.5972 61.1277 60.4735 67.3001 64.0375C73.4724 67.6015 78.5978 72.7275 82.1609 78.9004"
      stroke="#EFF1F7"
      strokeWidth="3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Camera = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.5 19.5H4.5C4.10218 19.5 3.72064 19.342 3.43934 19.0607C3.15804 18.7794 3 18.3978 3 18V7.5C3 7.10218 3.15804 6.72064 3.43934 6.43934C3.72064 6.15804 4.10218 6 4.5 6H7.5L9 3.75H15L16.5 6H19.5C19.8978 6 20.2794 6.15804 20.5607 6.43934C20.842 6.72064 21 7.10218 21 7.5V18C21 18.3978 20.842 18.7794 20.5607 19.0607C20.2794 19.342 19.8978 19.5 19.5 19.5Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 15.75C13.864 15.75 15.375 14.239 15.375 12.375C15.375 10.511 13.864 9 12 9C10.136 9 8.625 10.511 8.625 12.375C8.625 14.239 10.136 15.75 12 15.75Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Upload = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.0625 7.6875L12 3.75L15.9375 7.6875"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 14.25V3.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20.25 14.25V19.5C20.25 19.6989 20.171 19.8897 20.0303 20.0303C19.8897 20.171 19.6989 20.25 19.5 20.25H4.5C4.30109 20.25 4.11032 20.171 3.96967 20.0303C3.82902 19.8897 3.75 19.6989 3.75 19.5V14.25"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Star = () => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.4132 18.678L17.1382 21.678C17.7476 22.0623 18.4976 21.4905 18.3195 20.7873L16.9507 15.4061C16.9137 15.2569 16.9196 15.1003 16.9677 14.9544C17.0158 14.8084 17.1042 14.679 17.2226 14.5811L21.4601 11.0467C22.0132 10.5873 21.732 9.6592 21.0101 9.61233L15.4789 9.25608C15.3279 9.2473 15.1828 9.19474 15.0613 9.10484C14.9398 9.01493 14.847 8.89157 14.7945 8.74983L12.732 3.55608C12.6774 3.40599 12.5779 3.27633 12.4471 3.18471C12.3163 3.09309 12.1604 3.04395 12.0007 3.04395C11.841 3.04395 11.6852 3.09309 11.5544 3.18471C11.4235 3.27633 11.3241 3.40599 11.2695 3.55608L9.20698 8.74983C9.15441 8.89157 9.0617 9.01493 8.94016 9.10484C8.81862 9.19474 8.67353 9.2473 8.5226 9.25608L2.99135 9.61233C2.26948 9.6592 1.98823 10.5873 2.54135 11.0467L6.77885 14.5811C6.89729 14.679 6.98565 14.8084 7.03375 14.9544C7.08186 15.1003 7.08774 15.2569 7.05073 15.4061L5.7851 20.3936C5.56948 21.2373 6.46948 21.9217 7.19135 21.4623L11.5882 18.678C11.7115 18.5995 11.8546 18.5579 12.0007 18.5579C12.1468 18.5579 12.2899 18.5995 12.4132 18.678V18.678Z"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Info = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 17.5C14.1421 17.5 17.5 14.1421 17.5 10C17.5 5.85786 14.1421 2.5 10 2.5C5.85786 2.5 2.5 5.85786 2.5 10C2.5 14.1421 5.85786 17.5 10 17.5Z"
      stroke="black"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.375 9.375H10V13.75H10.625"
      stroke="black"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.84375 7.5C10.3615 7.5 10.7812 7.08027 10.7812 6.5625C10.7812 6.04473 10.3615 5.625 9.84375 5.625C9.32598 5.625 8.90625 6.04473 8.90625 6.5625C8.90625 7.08027 9.32598 7.5 9.84375 7.5Z"
      fill="black"
    />
  </svg>
);

export const Funnel = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.28886 3.75H16.7107C16.832 3.75004 16.9506 3.78533 17.0521 3.85159C17.1536 3.91784 17.2337 4.01219 17.2825 4.12315C17.3314 4.2341 17.3469 4.35687 17.3272 4.47649C17.3076 4.59611 17.2535 4.70743 17.1717 4.79688L12.0389 10.4453C11.932 10.5597 11.8733 10.7107 11.8748 10.8672V15.2891C11.8758 15.3929 11.8505 15.4954 11.8013 15.5869C11.7522 15.6784 11.6807 15.756 11.5936 15.8125L9.09355 17.4766C8.99977 17.5383 8.89109 17.5737 8.77892 17.5789C8.66675 17.5842 8.55524 17.5591 8.45611 17.5063C8.35699 17.4536 8.27391 17.3751 8.21561 17.2791C8.15731 17.1831 8.12594 17.0732 8.1248 16.9609V10.8672C8.12631 10.7107 8.06756 10.5597 7.96074 10.4453L2.82793 4.79688C2.7461 4.70743 2.69204 4.59611 2.67236 4.47649C2.65267 4.35687 2.6682 4.2341 2.71705 4.12315C2.76591 4.01219 2.84597 3.91784 2.9475 3.85159C3.04903 3.78533 3.16763 3.75004 3.28886 3.75Z"
      stroke="black"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ArrowDown = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17 10L12 15L7 10"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const List = () => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_12705_48777)">
      <path
        d="M3.75 12.5H20.25"
        stroke="#088BDD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.75 6.5H20.25"
        stroke="#088BDD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.75 18.5H20.25"
        stroke="#088BDD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_12705_48777">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const Tick = () => (
  <svg
    width="18"
    height="12"
    viewBox="0 0 18 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g id="Icon">
      <path
        id="Icon_2"
        d="M5.74557 11.3613L0.231144 6.49803C0.0770477 6.36131 0 6.18553 0 5.97069C0 5.75585 0.0770477 5.58007 0.231144 5.44335L1.4529 4.38866C1.607 4.23241 1.79962 4.1543 2.03076 4.1543C2.26191 4.1543 2.46553 4.23241 2.64164 4.38866L6.33994 7.6699L14.2649 0.638671C14.441 0.482422 14.6446 0.404297 14.8757 0.404297C15.1069 0.404297 15.2995 0.482422 15.4536 0.638671L16.6754 1.69336C16.8295 1.83007 16.9065 2.00586 16.9065 2.2207C16.9065 2.43554 16.8295 2.61132 16.6754 2.74804L6.93431 11.3613C6.78021 11.5176 6.58209 11.5957 6.33994 11.5957C6.09779 11.5957 5.89967 11.5176 5.74557 11.3613Z"
        fill="#05BEBD"
      />
    </g>
  </svg>
);

export const ArrowDownDark = () => (
  <svg
    width="18"
    height="10"
    viewBox="0 0 18 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.5 1L9 8.5L1.5 1"
      stroke="#0F133A"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Snapchat = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.0273 -0.00708008H11.9727C5.36035 -0.00708008 0 5.35485 0 11.9691V12.0238C0 18.6381 5.36035 24 11.9727 24H12.0273C18.6397 24 24 18.6381 24 12.0238V11.9691C24 5.35485 18.6397 -0.00708008 12.0273 -0.00708008Z"
      fill="white"
    />
    <path
      d="M15.7561 9.79913C15.7544 9.95978 15.7376 10.1204 15.7224 10.2811C15.7174 10.3341 15.7157 10.3879 15.7149 10.4409C15.7132 10.5326 15.7863 10.6158 15.878 10.6099C15.9217 10.6074 15.9663 10.5956 16.0083 10.5813C16.1487 10.5317 16.2875 10.4787 16.4271 10.4257C16.5532 10.377 16.6818 10.3332 16.8122 10.2979C16.9543 10.2592 17.0964 10.2306 17.241 10.2542C17.5353 10.3021 17.727 10.6192 17.6194 10.8976C17.5891 10.9758 17.5429 11.0431 17.4874 11.1053C17.4108 11.1903 17.3242 11.2626 17.2301 11.3265C17.1191 11.4014 17.0072 11.4746 16.8954 11.5486C16.7087 11.6714 16.5221 11.7933 16.3362 11.9178C16.2589 11.9691 16.1841 12.0238 16.1109 12.0802C16.0705 12.1113 16.0327 12.1474 15.9982 12.1853C15.9326 12.2585 15.8965 12.3518 15.9024 12.4494C15.9083 12.5528 15.9259 12.6538 15.9545 12.753C16.0428 13.0625 16.1782 13.3502 16.3421 13.6252C16.7819 14.3595 17.405 14.9331 18.1466 15.4016C18.35 15.5303 18.5678 15.6337 18.7822 15.7448C18.8125 15.7607 18.8419 15.7767 18.8714 15.7944C18.9008 15.812 18.9269 15.8356 18.9462 15.8642C18.9765 15.9071 18.9807 15.9525 18.9546 15.9996C18.9327 16.04 18.9008 16.0711 18.8663 16.0997C18.8276 16.1317 18.7856 16.1586 18.7419 16.183C18.4913 16.3251 18.2222 16.4311 17.9439 16.5051C17.8094 16.5413 17.6749 16.5791 17.5412 16.6187C17.4588 16.643 17.3814 16.6792 17.3049 16.7187C17.1762 16.7852 17.1031 16.892 17.0762 17.0308C17.061 17.1082 17.0518 17.1872 17.0375 17.2646C17.0022 17.4606 16.9316 17.5321 16.7399 17.5657C16.6726 17.5775 16.6036 17.5842 16.5355 17.5884C16.364 17.5977 16.1925 17.6027 16.0209 17.612C16.0159 17.612 16.0108 17.612 16.0066 17.6128C15.3214 17.6532 14.6588 17.8727 14.071 18.2276C14.0399 18.2461 14.0088 18.2655 13.9769 18.284C13.7389 18.4278 13.4967 18.5649 13.2369 18.6658C12.9468 18.7786 12.6475 18.8458 12.3372 18.8736C12.0757 18.8971 11.8151 18.8938 11.5552 18.8643C11.1659 18.8206 10.7968 18.7071 10.4479 18.5287C10.2856 18.4455 10.1292 18.353 9.97447 18.2579C9.96606 18.2529 9.95766 18.2478 9.94841 18.2419C9.33124 17.866 8.62914 17.638 7.90686 17.6086C7.90181 17.6086 7.89677 17.6086 7.89173 17.6086C7.74038 17.6027 7.58987 17.5968 7.43852 17.5876C7.35023 17.5825 7.26194 17.5708 7.17533 17.5472C7.06518 17.5169 6.9853 17.4202 6.96428 17.3083C6.95251 17.2469 6.94326 17.1855 6.93233 17.1241C6.92981 17.1107 6.9298 17.0964 6.92728 17.0829C6.90206 16.9029 6.81293 16.7709 6.64392 16.6943C6.5405 16.6472 6.43456 16.6094 6.32525 16.5791C6.1369 16.527 5.94687 16.4807 5.76272 16.4143C5.60044 16.3562 5.44405 16.2873 5.29354 16.2048C5.24814 16.1804 5.20441 16.1527 5.16237 16.1241C5.13378 16.1047 5.10688 16.0829 5.08333 16.0585C5.00766 15.9794 5.01522 15.876 5.10099 15.8087C5.1506 15.7692 5.20861 15.7431 5.26495 15.7153C5.65762 15.5227 6.03011 15.2973 6.37654 15.0298C6.89954 14.627 7.34266 14.1501 7.67815 13.5756C7.79755 13.3704 7.89677 13.1525 7.97833 12.9288C8.03214 12.7808 8.07587 12.6328 8.0868 12.4772C8.09437 12.3628 8.05568 12.2492 7.97748 12.1659C7.92115 12.1062 7.85556 12.0583 7.78914 12.0103C7.68992 11.9397 7.58986 11.8699 7.48896 11.8034C7.27203 11.6605 7.05425 11.52 6.83732 11.3778C6.7423 11.3156 6.65064 11.2483 6.56824 11.1701C6.50854 11.1129 6.45473 11.0515 6.41185 10.98C6.32272 10.8303 6.31599 10.6764 6.38242 10.5183C6.42194 10.4241 6.49594 10.3442 6.58843 10.3013C6.72044 10.2407 6.85834 10.2331 6.99708 10.2558C7.14675 10.2802 7.29052 10.3265 7.43262 10.3778C7.57809 10.4299 7.72103 10.4871 7.86566 10.541C7.93292 10.5662 8.00103 10.5914 8.0725 10.6024C8.08932 10.6049 8.10614 10.6099 8.12296 10.6091C8.2062 10.6066 8.27514 10.5797 8.2743 10.4678C8.2743 10.414 8.27263 10.3593 8.2701 10.3055C8.25749 10.0363 8.24151 9.76718 8.23479 9.49803C8.21965 8.85712 8.22217 8.21369 8.3895 7.59129C8.49208 7.21028 8.6695 6.85618 8.90914 6.54161C9.40607 5.88809 10.0619 5.47344 10.8464 5.25224C11.2752 5.13112 11.7133 5.09159 12.1573 5.10505C12.5769 5.11766 12.9948 5.18832 13.3916 5.32709C13.8625 5.49195 14.2905 5.73081 14.6546 6.06977C15.0683 6.45498 15.3869 6.90497 15.5602 7.44999C15.6384 7.69643 15.688 7.94875 15.7182 8.20444C15.7813 8.73348 15.7636 9.26588 15.7586 9.79745L15.7561 9.79913Z"
      fill="#0F133A"
    />
  </svg>
);

export const Cross = () => (
  <svg
    width="26"
    height="26"
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21.0879 4.91113L4.91016 21.0889M21.0879 21.0889L4.91016 4.91113L21.0879 21.0889Z"
      strokeWidth="2.6"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ToasterError = () => (
  <svg
    width="131"
    height="120"
    viewBox="0 0 131 120"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_301_4)">
      <path
        d="M-0.75359 4.05092C1.83737 -2.61152 8.25265 -7 15.4011 -7H113.261C125.453 -7 133.835 5.25309 129.416 16.6157L90.9526 115.521C88.3617 122.183 81.9464 126.571 74.7979 126.571H-23.0619C-35.2535 126.571 -43.6354 114.318 -39.2166 102.956L-0.75359 4.05092Z"
        fill="#FF7378"
      />
      <path
        d="M74.0884 51.9111L57.9106 68.0889M74.0884 68.0889L57.9106 51.9111"
        stroke="#160B2A"
        strokeWidth="2.6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_301_4">
        <rect width="131" height="120" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const CrossWithCircle = () => (
  <svg
    width="120"
    height="120"
    viewBox="0 0 120 120"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_10841_6256)">
      <path
        d="M59.9964 3.20007C92.1423 3.20007 116.8 28.6367 116.8 59.9963C116.8 91.3634 92.1498 116.8 60.0039 116.8C27.8581 116.808 3.2002 91.3709 3.2002 60.0038C3.2002 28.6367 27.8506 3.20007 59.9964 3.20007ZM60.0039 91.8352C79.5101 91.8352 92.4643 77.7877 92.4643 59.9963C92.4643 42.2049 79.5101 28.165 60.0039 28.165C40.4978 28.165 27.5436 42.2124 27.5436 60.0038C27.5436 77.7952 40.4978 91.8352 60.0039 91.8352Z"
        fill="#0F133A"
      />
      <path
        d="M67.0595 60.0001L79.4404 47.6192C80.1864 46.8731 80.1864 45.6599 79.4404 44.9064L74.3237 39.7897C73.9394 39.4054 73.4346 39.2246 72.9372 39.2321C72.4323 39.2246 71.935 39.4054 71.5507 39.7897L59.1999 52.1405L46.819 39.7596C46.073 39.0136 44.8597 39.0136 44.1062 39.7596L38.9896 44.8762C38.6052 45.2605 38.4244 45.7654 38.4319 46.2628C38.4244 46.7676 38.6052 47.265 38.9896 47.6493L51.3403 60.0001L38.9594 72.381C38.2134 73.127 38.2134 74.3402 38.9594 75.0938L44.0761 80.2104C44.4604 80.5947 44.9652 80.7756 45.4626 80.768C45.9675 80.7756 46.4648 80.5947 46.8491 80.2104L59.1999 67.8596L71.5808 80.2406C72.3268 80.9866 73.5401 80.9866 74.2936 80.2406L79.4102 75.1239C79.7946 74.7396 79.9754 74.2347 79.9679 73.7374C79.9754 73.2325 79.7946 72.7351 79.4102 72.3508L67.0595 60.0001Z"
        fill="#0F133A"
      />
    </g>
    <defs>
      <clipPath id="clip0_10841_6256">
        <rect width="120" height="120" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ToasterSuccess = () => (
  <svg
    width="132"
    height="120"
    viewBox="0 0 132 120"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M-0.346165 4.0684C2.24006 -2.60322 8.66002 -7 15.8154 -7H113.691C125.874 -7 134.256 5.2383 129.852 16.5983L91.3462 115.932C88.7599 122.603 82.34 127 75.1846 127H-22.6907C-34.8744 127 -43.2559 114.762 -38.8522 103.402L-0.346165 4.0684Z"
      fill="#CEBEE3"
    />
    <path
      d="M44.2249 61.6252L51.8082 69.2085L66.9749 52.9585"
      stroke="#160B2A"
      strokeWidth="2.6"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Umbrella = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15 19.8894L11.9766 10.8652"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.89769 12.7619C4.81568 12.7842 4.72992 12.7891 4.64588 12.7764C4.56184 12.7637 4.48137 12.7335 4.40963 12.6879C4.33789 12.6423 4.27645 12.5822 4.22923 12.5115C4.18202 12.4408 4.15008 12.361 4.13544 12.2772C3.78974 10.3136 4.19199 8.29174 5.26288 6.61046C6.33377 4.92918 7.99557 3.71048 9.92059 3.19467C11.8456 2.67887 13.8941 2.90339 15.6622 3.82398C17.4302 4.74456 18.7895 6.29439 19.472 8.16784C19.5012 8.2477 19.5134 8.33277 19.5079 8.41762C19.5023 8.50247 19.4792 8.58522 19.4398 8.6606C19.4005 8.73597 19.3459 8.8023 19.2795 8.85534C19.2131 8.90838 19.1363 8.94698 19.0541 8.96865L4.89769 12.7619Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.90787 11.6872C7.48495 6.37678 9.92077 3.19447 9.92077 3.19447C9.92077 3.19447 13.6214 4.73252 15.0443 10.0429"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const XLogo = () => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M23.9983 12.6586C23.9983 18.7194 19.5059 23.7299 13.6698 24.543C13.1242 24.6187 12.5659 24.6582 11.9992 24.6582C11.345 24.6582 10.7026 24.6061 10.0771 24.5052C4.36379 23.5853 0 18.6311 0 12.6586C0 6.03115 5.37276 0.658203 12 0.658203C18.6272 0.658203 24 6.03115 24 12.6586H23.9983Z"
      fill="white"
    />
    <path
      d="M4.87041 5.95117L10.4046 13.3505L4.83594 19.3667H6.08958L10.9654 14.0997L14.9046 19.3667H19.17L13.3247 11.5511L18.5083 5.95117H17.2547L12.7647 10.802L9.13667 5.95117H4.87124H4.87041ZM6.71346 6.87441H8.67254L17.3253 18.4435H15.3662L6.71346 6.87441Z"
      fill="#0F133A"
    />
  </svg>
);

export const Hourglass = () => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_13914_62139)">
      <path
        d="M10 10.75L5.25 7.1875C5.17238 7.12928 5.10938 7.05379 5.06598 6.96701C5.02259 6.88022 5 6.78453 5 6.6875V3.875C5 3.70924 5.06585 3.55027 5.18306 3.43306C5.30027 3.31585 5.45924 3.25 5.625 3.25H14.375C14.5408 3.25 14.6997 3.31585 14.8169 3.43306C14.9342 3.55027 15 3.70924 15 3.875V6.65937C14.9997 6.75574 14.9771 6.85074 14.934 6.93693C14.8909 7.02313 14.8285 7.09819 14.7516 7.15625L10 10.75Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 10.75L5.25 14.3125C5.17238 14.3707 5.10938 14.4462 5.06598 14.533C5.02259 14.6198 5 14.7155 5 14.8125V17.625C5 17.7908 5.06585 17.9497 5.18306 18.0669C5.30027 18.1842 5.45924 18.25 5.625 18.25H14.375C14.5408 18.25 14.6997 18.1842 14.8169 18.0669C14.9342 17.9497 15 17.7908 15 17.625V14.8406C14.9999 14.744 14.9775 14.6487 14.9344 14.5622C14.8913 14.4757 14.8287 14.4004 14.7516 14.3422L10 10.75Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_13914_62139">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0 0.75)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const Gear = () => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_46_8729)">
      <path
        d="M12 16.25C14.0711 16.25 15.75 14.5711 15.75 12.5C15.75 10.4289 14.0711 8.75 12 8.75C9.92893 8.75 8.25 10.4289 8.25 12.5C8.25 14.5711 9.92893 16.25 12 16.25Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.1906 19.8228C12.0649 19.8228 11.9384 19.8228 11.8156 19.8228L8.81088 21.5C7.64118 21.1065 6.55626 20.4959 5.61306 19.7L5.60181 16.325C5.53525 16.22 5.47244 16.1141 5.41431 16.0053L2.4265 14.3038C2.19117 13.1134 2.19117 11.8885 2.4265 10.6981L5.4115 9.00125C5.47244 8.89344 5.53525 8.78656 5.599 8.68156L5.614 5.30656C6.55634 4.50842 7.64099 3.89548 8.81088 3.5L11.8109 5.17719C11.9365 5.17719 12.0631 5.17719 12.1859 5.17719L15.1859 3.5C16.3556 3.89346 17.4405 4.50414 18.3837 5.3L18.3949 8.675C18.4615 8.78 18.5243 8.88594 18.5824 8.99469L21.5684 10.6953C21.8037 11.8857 21.8037 13.1106 21.5684 14.3009L18.5834 15.9978C18.5224 16.1056 18.4596 16.2125 18.3959 16.3175L18.3809 19.6925C17.4392 20.4908 16.3552 21.104 15.1859 21.5L12.1906 19.8228Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_46_8729">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const ChevronDown = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.5 9L12 16.5L4.5 9"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SelectedTick = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_54_17971)">
      <path
        d="M4.25 13.5L9.5 18.75L21.5 6.75"
        stroke="#7448B0"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_54_17971">
        <rect width="24" height="24" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);

export const Alert = () => (
  <svg
    width="30"
    height="26"
    viewBox="0 0 30 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15 10V16.8744"
      stroke="#FF7378"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.1413 2.04379L1.28873 21.8696C1.0999 22.1863 1.00034 22.5455 1 22.9112C0.999666 23.2769 1.09858 23.6363 1.28682 23.9534C1.47506 24.2704 1.74603 24.534 2.07258 24.7177C2.39912 24.9013 2.76978 24.9987 3.14742 25H26.8526C27.2302 24.9987 27.6009 24.9013 27.9274 24.7177C28.254 24.534 28.5249 24.2704 28.7132 23.9534C28.9014 23.6363 29.0003 23.2769 29 22.9112C28.9997 22.5455 28.9001 22.1863 28.7113 21.8696L16.8587 2.04379C16.6713 1.72667 16.4007 1.46312 16.0743 1.27983C15.7479 1.09653 15.3773 1 15 1C14.6227 1 14.2521 1.09653 13.9257 1.27983C13.5993 1.46312 13.3287 1.72667 13.1413 2.04379V2.04379Z"
      stroke="#FF7378"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 22.0234C15.5523 22.0234 16 21.5757 16 21.0234C16 20.4712 15.5523 20.0234 15 20.0234C14.4477 20.0234 14 20.4712 14 21.0234C14 21.5757 14.4477 22.0234 15 22.0234Z"
      fill="#FF7378"
    />
  </svg>
);

export const Briefcase = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_68_6027)">
      <path
        d="M16.875 5H3.125C2.77982 5 2.5 5.27982 2.5 5.625V15.625C2.5 15.9702 2.77982 16.25 3.125 16.25H16.875C17.2202 16.25 17.5 15.9702 17.5 15.625V5.625C17.5 5.27982 17.2202 5 16.875 5Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.125 5V3.75C13.125 3.41848 12.9933 3.10054 12.7589 2.86612C12.5245 2.6317 12.2065 2.5 11.875 2.5H8.125C7.79348 2.5 7.47554 2.6317 7.24112 2.86612C7.0067 3.10054 6.875 3.41848 6.875 3.75V5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.5 9.24219C15.2208 10.5609 12.6333 11.2534 10 11.2492C7.36684 11.2534 4.77939 10.5613 2.5 9.24297"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.75 8.75H11.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_68_6027">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Pending = () => (
  <svg
    width="21"
    height="21"
    viewBox="0 0 21 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_238_9829)">
      <path
        d="M10.5 3V5.5"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18 10.5H15.5"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.8031 15.8031L14.0352 14.0352"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5 18V15.5"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.19727 15.8031L6.96523 14.0352"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3 10.5H5.5"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.19727 5.19727L6.96523 6.96523"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_238_9829">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0.5 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const RenewAlert = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_238_9799)">
      <path
        d="M13.3515 3.77063L21.5509 18.0084C22.1256 19.0116 21.3831 20.25 20.2 20.25H3.80121C2.61808 20.25 1.87558 19.0116 2.45027 18.0084L10.6496 3.77063C11.2403 2.74313 12.7609 2.74313 13.3515 3.77063Z"
        stroke="#FF7378"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 13.5V9.75"
        stroke="#FF7378"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 18C12.6213 18 13.125 17.4963 13.125 16.875C13.125 16.2537 12.6213 15.75 12 15.75C11.3787 15.75 10.875 16.2537 10.875 16.875C10.875 17.4963 11.3787 18 12 18Z"
        fill="#FF7378"
      />
    </g>
    <defs>
      <clipPath id="clip0_238_9799">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PopularStar = () => (
  <svg
    width="18"
    height="17"
    viewBox="0 0 18 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.9469 12.9061L16.1588 14.2276C16.2938 14.2611 16.4384 14.2564 16.5744 14.2141C16.7104 14.1719 16.8316 14.094 16.9228 13.9903C17.0139 13.8866 17.0709 13.7617 17.0866 13.6315C17.1022 13.5013 17.0758 13.3716 17.0106 13.2587L14.5102 8.85694L17.6958 4.6109C17.7766 4.50118 17.8219 4.37354 17.826 4.24375C17.8301 4.11395 17.7929 3.98768 17.719 3.88053C17.6451 3.77337 17.5376 3.69002 17.4099 3.64076C17.2823 3.59151 17.14 3.57851 17.0006 3.60336L11.6021 4.62987L8.35934 0.684698C8.2763 0.58275 8.16099 0.507364 8.02811 0.468156C7.89523 0.428947 7.7508 0.427692 7.61324 0.464551C7.47568 0.501409 7.35123 0.574711 7.25575 0.675108C7.16028 0.775505 7.09811 0.898447 7.07717 1.02825L6.24147 6.06624L1.05297 7.87651C0.918712 7.92423 0.80084 8.00673 0.714076 8.11369C0.627313 8.22065 0.575505 8.34733 0.565127 8.47792C0.554748 8.6085 0.586258 8.73719 0.65572 8.8479C0.725182 8.95862 0.829517 9.04646 0.955688 9.10044L5.8375 11.1848L5.87195 16.2433C5.87195 16.3736 5.91393 16.4992 5.99258 16.6041C6.07122 16.7091 6.18301 16.7887 6.31381 16.8329C6.44461 16.8772 6.58855 16.884 6.72744 16.8526C6.86633 16.8212 6.99393 16.7529 7.09412 16.6565L10.9469 12.9061Z"
      fill="#7448B0"
      fill-opacity="0.6"
    />
  </svg>
);

export const Youtube = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24 11.9965C24 5.36904 18.6274 -0.00354004 12 -0.00354004C5.37259 -0.00354004 0 5.36904 0 11.9965C0 18.6239 5.37259 23.9965 12 23.9965C18.6274 23.9965 24 18.6239 24 11.9965Z"
      fill="white"
    />
    <path
      d="M19.5567 9.50986C19.4623 8.59833 19.259 7.59068 18.5111 7.06113C17.9318 6.65048 17.1704 6.63529 16.4595 6.63614C14.9569 6.63614 13.4534 6.63867 11.9508 6.63951C10.5055 6.6412 9.06017 6.64204 7.61488 6.64373C7.01113 6.64373 6.42424 6.59735 5.86349 6.85875C5.382 7.08305 5.00508 7.50973 4.77825 7.98447C4.46373 8.64471 4.39796 9.39266 4.36001 10.1229C4.29002 11.4527 4.29761 12.7858 4.38109 14.1147C4.44265 15.0845 4.59864 16.1562 5.34828 16.7743C6.01274 17.3215 6.95041 17.3485 7.81219 17.3494C10.5476 17.3519 13.2839 17.3544 16.0202 17.3561C16.371 17.357 16.7369 17.3502 17.0945 17.3114C17.7977 17.2355 18.4681 17.034 18.9201 16.5129C19.3762 15.9876 19.4935 15.2565 19.5626 14.5642C19.7313 12.8845 19.7296 11.1887 19.5567 9.50986ZM10.3689 14.3508V9.64141L14.4467 11.9957L10.3689 14.3508Z"
      fill="#0F133A"
    />
  </svg>
);

export const FadedAlert = () => (
  <svg
    width="86"
    height="76"
    viewBox="0 0 86 76"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      opacity="0.3"
      d="M68.7839 -28.7704C58.6436 -31.7933 47.8346 -31.7413 37.7238 -28.6211C27.613 -25.5008 18.6545 -19.4525 11.9812 -11.2408C5.30793 -3.02917 1.21955 6.97694 0.233052 17.5122C-0.753447 28.0474 1.40627 38.6385 6.43908 47.9463C11.4719 57.2541 19.1518 64.8605 28.5075 69.8035C37.8633 74.7466 48.4747 76.8043 59 75.7165C69.5252 74.6287 79.4915 70.4442 87.6386 63.6923C95.7856 56.9403 101.748 47.924 104.77 37.7837C108.824 24.186 107.31 9.53492 100.561 -2.94637C93.8121 -15.4277 82.3816 -24.7168 68.7839 -28.7704ZM41.9055 61.3918C40.9215 61.0981 40.005 60.6134 39.2082 59.9655C38.4115 59.3176 37.7502 58.5191 37.2621 57.6156C36.774 56.7121 36.4685 55.7213 36.3633 54.6998C36.2581 53.6783 36.3551 52.6461 36.6488 51.662C36.9425 50.678 37.4271 49.7615 38.0751 48.9647C38.723 48.168 39.5215 47.5067 40.4249 47.0186C42.2496 46.0327 44.3912 45.8121 46.3785 46.4053C48.3658 46.9984 50.0362 48.3568 51.022 50.1814C52.0078 52.0061 52.2284 54.1477 51.6353 56.135C51.0421 58.1223 49.6838 59.7926 47.8591 60.7785C46.0345 61.7643 43.8929 61.9849 41.9055 61.3918ZM68.9325 6.70259C68.8199 6.97286 68.7118 7.23638 68.6082 7.49314C64.2237 18.2784 58.7708 28.5978 52.3312 38.2972C50.7704 40.662 45.8785 39.2026 45.865 36.3647C45.9562 27.5458 46.6333 18.7426 47.8921 10.0134C48.6758 3.63503 48.0677 -7.28389 53.2231 -12.1893C57.7974 -16.5812 66.4258 -14.6961 70.4934 -10.4798C74.9866 -5.79741 71.0947 1.5607 68.9123 6.70259H68.9325Z"
      fill="#CEBEE3"
    />
  </svg>
);

export const FrequencyTick = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_16667_47357)">
      <path
        d="M3.75 13.5L9 18.75L21 6.75"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_16667_47357">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const CardPlus = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.5 8.5H14"
      stroke="#160B2A"
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.5 16.5H8.5"
      stroke="#160B2A"
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11 16.5H15"
      stroke="#160B2A"
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22.5 12.03V16.11C22.5 19.62 21.61 20.5 18.06 20.5H6.94C3.39 20.5 2.5 19.62 2.5 16.11V7.89C2.5 4.38 3.39 3.5 6.94 3.5H14"
      stroke="#160B2A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 6.25H22.5"
      stroke="#160B2A"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
    <path
      d="M19.75 9V3.5"
      stroke="#160B2A"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);

export const MoonStars = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_11037_28113)">
      <path
        d="M13 7.5V4.5"
        stroke="black"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.5 6H11.5"
        stroke="black"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 2V4"
        stroke="black"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11 3H9"
        stroke="black"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.1684 9.88641C12.7836 9.9621 12.3924 10.0002 12.0003 10.0002C11.1135 9.99941 10.2379 9.80228 9.43635 9.42292C8.63482 9.04355 7.92725 8.49137 7.36449 7.80605C6.80172 7.12073 6.39772 6.31927 6.18151 5.45926C5.96531 4.59924 5.94226 3.70201 6.11402 2.83203C5.24614 3.1494 4.47221 3.68032 3.86371 4.37578C3.2552 5.07123 2.83173 5.9088 2.6324 6.81113C2.43308 7.71346 2.46432 8.65148 2.72324 9.53855C2.98216 10.4256 3.46041 11.2332 4.11384 11.8866C4.76727 12.54 5.57481 13.0183 6.46188 13.2772C7.34895 13.5361 8.28696 13.5673 9.18929 13.368C10.0916 13.1687 10.9292 12.7452 11.6246 12.1367C12.3201 11.5282 12.851 10.7543 13.1684 9.88641Z"
        stroke="black"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_11037_28113">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PlanDataBars = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_11037_27938)">
      <path
        d="M10 4.5V12.5"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5 2V12.5"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.5 7V12.5"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5 9.5V12.5"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.5 12V12.5"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_11037_27938">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PlanDataCalendar = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_11037_27943)">
      <path
        d="M13 2.5H3C2.72386 2.5 2.5 2.72386 2.5 3V13C2.5 13.2761 2.72386 13.5 3 13.5H13C13.2761 13.5 13.5 13.2761 13.5 13V3C13.5 2.72386 13.2761 2.5 13 2.5Z"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11 1.5V3.5"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5 1.5V3.5"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.5 5.5H13.5"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_11037_27943">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Chip = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_11037_27913)">
      <path
        d="M9.5 6.5H6.5V9.5H9.5V6.5Z"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5 3H3.5C3.22386 3 3 3.22386 3 3.5V12.5C3 12.7761 3.22386 13 3.5 13H12.5C12.7761 13 13 12.7761 13 12.5V3.5C13 3.22386 12.7761 3 12.5 3Z"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13 6.5H14.5"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13 9.5H14.5"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M1.5 6.5H3"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M1.5 9.5H3"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.5 13V14.5"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.5 13V14.5"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.5 1.5V3"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.5 1.5V3"
        stroke="#667085"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_11037_27913">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ExpiryAlert = () => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_11037_28263)">
      <path
        d="M12.2381 3.45641L19.7542 16.5077C20.281 17.4273 19.6003 18.5625 18.5158 18.5625H3.48363C2.39909 18.5625 1.71847 17.4273 2.24527 16.5077L9.76136 3.45641C10.3028 2.51453 11.6967 2.51453 12.2381 3.45641Z"
        stroke="#0F133A"
        strokeWidth="1.375"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11 12.375V8.9375"
        stroke="#0F133A"
        strokeWidth="1.375"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11 16.5C11.5695 16.5 12.0312 16.0383 12.0312 15.4688C12.0312 14.8992 11.5695 14.4375 11 14.4375C10.4305 14.4375 9.96875 14.8992 9.96875 15.4688C9.96875 16.0383 10.4305 16.5 11 16.5Z"
        fill="#0F133A"
      />
    </g>
    <defs>
      <clipPath id="clip0_11037_28263">
        <rect width="22" height="22" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const HeadPhones = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_12166_16659)">
      <path
        d="M21.5 18.75V19.5C21.5 20.2956 21.1839 21.0587 20.6213 21.6213C20.0587 22.1839 19.2956 22.5 18.5 22.5H13.25"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21.5 12H18.5C18.1022 12 17.7206 12.158 17.4393 12.4393C17.158 12.7206 17 13.1022 17 13.5V17.25C17 17.6478 17.158 18.0294 17.4393 18.3107C17.7206 18.592 18.1022 18.75 18.5 18.75H21.5V12ZM21.5 12C21.5 10.8181 21.2672 9.64778 20.8149 8.55585C20.3626 7.46392 19.6997 6.47177 18.864 5.63604C18.0282 4.80031 17.0361 4.13738 15.9442 3.68508C14.8522 3.23279 13.6819 3 12.5 3C11.3181 3 10.1478 3.23279 9.05585 3.68508C7.96392 4.13738 6.97177 4.80031 6.13604 5.63604C5.30031 6.47177 4.63738 7.46392 4.18508 8.55585C3.73279 9.64778 3.5 10.8181 3.5 12M3.5 12V17.25C3.5 17.6478 3.65804 18.0294 3.93934 18.3107C4.22064 18.592 4.60218 18.75 5 18.75H6.5C6.89782 18.75 7.27936 18.592 7.56066 18.3107C7.84196 18.0294 8 17.6478 8 17.25V13.5C8 13.1022 7.84196 12.7206 7.56066 12.4393C7.27936 12.158 6.89782 12 6.5 12H3.5Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_12166_16659">
        <rect width="24" height="24" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);

export const Tag = () => (
  <svg
    width="17"
    height="17"
    viewBox="0 0 17 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_11372_18233)">
      <path
        d="M2.98219 9.14625C2.8886 9.05255 2.836 8.92556 2.83594 8.79313V3H8.62906C8.76149 3.00006 8.88849 3.05266 8.98219 3.14625L15.1897 9.35375C15.2834 9.44751 15.336 9.57464 15.336 9.70719C15.336 9.83974 15.2834 9.96687 15.1897 10.0606L9.89844 15.3538C9.80468 15.4474 9.67755 15.5001 9.545 15.5001C9.41245 15.5001 9.28532 15.4474 9.19156 15.3538L2.98219 9.14625Z"
        stroke="#0F133A"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.58594 6.5C6.00015 6.5 6.33594 6.16421 6.33594 5.75C6.33594 5.33579 6.00015 5 5.58594 5C5.17172 5 4.83594 5.33579 4.83594 5.75C4.83594 6.16421 5.17172 6.5 5.58594 6.5Z"
        fill="#0F133A"
      />
    </g>
    <defs>
      <clipPath id="clip0_11372_18233">
        <rect
          width="16"
          height="16"
          fill="white"
          transform="translate(0.335938 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const CreditCardSm = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_1701_11369)">
      <path
        d="M21 5.25H3C2.58579 5.25 2.25 5.58579 2.25 6V18C2.25 18.4142 2.58579 18.75 3 18.75H21C21.4142 18.75 21.75 18.4142 21.75 18V6C21.75 5.58579 21.4142 5.25 21 5.25Z"
        stroke="#088BDD"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M15.75 15.75H18.75"
        stroke="#088BDD"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11.25 15.75H12.75"
        stroke="#088BDD"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M2.25 9H21.75"
        stroke="#088BDD"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_1701_11369">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
