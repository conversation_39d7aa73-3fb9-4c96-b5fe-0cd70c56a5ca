import { formatPrice } from "./formatPrice";

const formatPlanData = (plan: any) => {
  let dataArr = [];
  if ("creditAmount" in plan) plan.credit = plan.creditAmount;
  if (plan.dataAllowance !== 0) {
    dataArr.push(`${plan.dataAllowance} GB`);
  }
  if (plan.voiceAllowance !== 0) {
    dataArr.push(`${plan.voiceAllowance} mins`);
  }
  if (plan.smsAllowance !== 0) {
    dataArr.push(`${plan.smsAllowance} SMS`);
  }
  if (plan.validity !== 0) {
    dataArr.push(`${plan.validity} days validity`);
  }
  if (plan.credit !== 0) {
    dataArr.push(
      `${plan.prices[0].currencySymbol}${formatPrice(
        plan.credit
      )} worldwide credit`
    );
  }
  return dataArr.join(", ");
};

export default formatPlanData;

export const formatDashboardPlanData = (plan: any) => {
  let dataArr = [];
  if (plan.initialBytes !== 0) {
    dataArr.push(`${plan.initialBytes / (1024 * 1024 * 1024)} GB`);
  }
  if (plan.initialMinutes !== 0) {
    dataArr.push(`${plan.initialMinutes} mins`);
  }
  if (plan.initialMessages !== 0) {
    dataArr.push(`${plan.initialMessages} SMS`);
  }
  return dataArr.join(", ");
};

export const formatTransactionData = (plan: any, isList = true) => {
  let dataArr = [];
  let validityStr = null as any;
  let creditStr = null as any;
  if (plan.dataAmount !== "0") {
    dataArr.push(`${plan.dataAmount} GB`);
  }
  if (plan.minutesAmount !== "0") {
    dataArr.push(`${plan.minutesAmount} mins`);
  }
  if (plan.smsAmount !== "0") {
    dataArr.push(`${plan.smsAmount} SMS`);
  }
  if (plan.daysValidity !== "0") {
    const string = `${plan.daysValidity} days validity`;
    if (isList) {
      validityStr = string;
    } else {
      dataArr.push(string);
    }
  }
  if (plan.creditAmount !== "0.0") {
    const string = `${plan.currencySymbol}${formatPrice(
      parseFloat(plan.creditAmount)
    )} worldwide credit`;
    if (isList) {
      creditStr = string;
    } else {
      dataArr.push(string);
    }
  }
  return isList
    ? `<ul style='padding-inline-start:24px;margin:0px;'>
      ${dataArr.length > 0 ? `<li>${dataArr.join(", ")}</li>` : ""}
      ${validityStr ? `<li>${validityStr}</li>` : ""}
      ${creditStr ? `<li>${creditStr}</li>` : ""}
    </ul>`
    : dataArr.join(", ");
};

export const formatBytes = (amount: number) => {
  const gb = amount / (1024 * 1024 * 1024);
  const mb = amount / (1024 * 1024);
  const kb = amount / 1024;

  if (gb >= 1) {
    return `${gb} GB`;
  } else if (mb >= 1) {
    return `${mb} MB`;
  } else {
    return `${kb} kB`;
  }
};
