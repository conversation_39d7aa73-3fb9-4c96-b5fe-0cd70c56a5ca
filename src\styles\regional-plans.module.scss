.plansView {
  padding: 60px 60px 200px 60px;
  text-align: center;
  color: rgba(15, 19, 58, 1);
  background-position: bottom;
  background-size: cover;
  background-repeat: no-repeat;
  background-color: #ceedf5;
  @media (max-width: 768px) {
    padding: 60px 16px 108px 16px;
  }
  h2 {
    font-size: 48px;
    font-weight: 700;
    line-height: 61px;
    margin: 0px;
    @media (max-width: 768px) {
      font-size: 30px;
      line-height: 38px;
    }
  }
  p {
    font-size: 16px;
  }
}

.container {
  padding: 60px 80px;
  @media (max-width: 865px) {
    padding: 48px 16px;
  }
}

.plans {
  width: 100%;
  @media (max-width: 865px) {
    > div {
      margin-bottom: 16px;
    }
    display: flex;
    flex-direction: column;
  }
}

.countries {
  padding: 0px 80px 60px 80px;
  @media (max-width: 768px) {
    padding: 0px 16px 24px 16px;
  }
  h4,
  p {
    text-align: center;
  }
  h4 {
    font-size: 30px;
    font-weight: 800;
    line-height: 38px;
    margin: 0px 0px 8px 0px;
  }
  p {
    font-size: 18px;
    font-weight: 400;
    line-height: 28px;
    margin: 0px;
  }
  .includedCountryContainer {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    grid-row-gap: 16px;
    grid-column-gap: 16px;
    margin-top: 32px;
    @media (max-width: 1050px) {
      grid-template-columns: 1fr 1fr 1fr 1fr;
    }
    @media (max-width: 880px) {
      grid-template-columns: 1fr 1fr 1fr 1fr;
    }
    @media (max-width: 680px) {
      grid-template-columns: 1fr 1fr 1fr;
    }
    @media (max-width: 570px) {
      display: flex;
      flex-direction: column;
      grid-row-gap: 0px;
      grid-column-gap: 0px;
    }
  }
  .includedCountry {
    display: grid;
    grid-template-columns: 20px 1fr;
    grid-column-gap: 8px;
    align-items: center;
    font-size: 16px;
    padding: 18px 16px;
    background: #fff;
    border-radius: 16px;
    @media (max-width: 570px) {
      margin-bottom: 8px;
    }
    .flag {
      width: 20px;
      height: 20px;
      border-radius: 100px;
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
    }
  }
}
