@use "../../styles/theme.scss" as *;

.main {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  padding: 50px 32px 24px 32px;
  height: 100%;
  text-align: center;
  position: relative;
  .icon {
    position: absolute;
    top: 0px;
    right: 0px;
    z-index: 0;
  }
  @media (max-width: 768px) {
    padding: 24px;
  }
  h5 {
    color: $primary;
    margin: 0 0 8px 0;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    position: relative;
    z-index: 1;
  }
  p {
    color: $dark-dark-purple;
    font-size: 16px;
    line-height: 24px;
    margin: 0 0 16px 0;
    position: relative;
    z-index: 1;
  }
}

.balanceContainer {
  background: #7448b033;
  margin-bottom: 16px;
  border-radius: 16px;
  padding: 8px 16px;
  color: $dark-dark-purple;
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
