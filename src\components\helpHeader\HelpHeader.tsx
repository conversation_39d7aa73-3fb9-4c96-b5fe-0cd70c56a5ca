import SearchBar from "../../components/SearchBar";
import styles from "../../styles/help.module.scss";
import { Link } from "react-router-dom";
import { CmsApiGet, cmsURL } from "../../pages/api/cms-api";
import { resolveRichText } from "../../components/utils/richTextConverter";
import { useEffect, useState } from "react";
import { t } from "i18next";
import { useTranslation } from "react-i18next";

type HelpProps = {
  questions?: questionsType[];
};

type questionsType = {
  id: number;
  attributes: {
    Question: String;
    Answer: String;
    Favourite: Boolean;
    category: String;
    topQuestionsType: String;
  };
};

const HelpHeader = ({ questions }: HelpProps) => {
  const { t, i18n } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchResults, setSearchResults] = useState<questionsType[]>([]);
  const [faqs, setFaqs] = useState<questionsType[]>([]);

  useEffect(() => {
    if (searchQuery.length < 1 && faqs.length === 0) {
      CmsApiGet("/api/faqs").then((response: any) => {
        setFaqs(response.data.data);
      });
    }
    if (searchQuery === "") {
      clearSearch();
    } else {
      handleSearch();
    }
  }, [searchQuery]);

  const [content, setContent] = useState<any>(null);

  useEffect(() => {
    CmsApiGet(`/api/help-page?locale=${i18n.language}&populate=deep`).then(
      (response: any) => {
        setContent(response.data.data.attributes);
      }
    );
  }, [i18n, i18n.language]);

  useEffect(() => {
    if (questions && questions.length > 0) {
      setFaqs(questions);
    }
  }, [questions]);

  const clearSearch = () => {
    setSearchQuery("");
    setSearchResults([]);
    setShowSearchResults(false);
  };

  const handleSearchChange = (e: any) => {
    setSearchQuery(e.target.value);
  };

  const handleSearch = () => {
    if (searchQuery === "") {
      clearSearch();
      return;
    }
    const searchStrings = searchQuery.split(" ");
    let filteredFaqs = faqs.filter((faq: any) =>
      searchStrings.some((str: string) =>
        faq.attributes.Question.toLowerCase().includes(str.toLowerCase())
      )
    );
    setSearchResults(filteredFaqs);
    setShowSearchResults(true);
  };

  return (
    <div className={styles.searchSection}>
      <h1>{content?.title}</h1>
      <p>{content?.text}</p>
      <SearchBar
        handleSearch={handleSearch}
        id="faqs-search-help"
        searchQuery={searchQuery}
        placeholder={t("general.search")}
        handleSearchChange={handleSearchChange}
        clearSearch={clearSearch}
        maxWidth={497}
      />
      <div className={styles.searchContainer + " flex justify-content-center"}>
        {searchQuery && searchResults.length > 0 && showSearchResults && (
          <div className={styles.searchResults}>
            {searchResults.length > 0 &&
              searchResults.map((faq: any, index: number) => {
                return (
                  <div key={index}>
                    <Link to={`/help/${faq.attributes.slug}`}>
                      <h5>{faq.attributes.Question}</h5>
                      <div>
                        <div
                          dangerouslySetInnerHTML={{
                            __html: faq.attributes.Answer
                              ? resolveRichText(faq.attributes.Answer)
                              : "",
                          }}
                        ></div>
                      </div>
                    </Link>
                  </div>
                );
              })}
          </div>
        )}
        {searchQuery && searchResults.length < 1 && showSearchResults && (
          <div className={styles.searchResults}>
            <p className={styles.notFound}>{t("search.noResults")}</p>
          </div>
        )}
      </div>
      <img
        src={cmsURL + content?.image?.data?.attributes?.url}
        alt="Need Assistance? Orbit Mobile are here to help you with your eSIM anywhere and to answer your questions"
      />
    </div>
  );
};

export default HelpHeader;
