@use "./theme.scss" as *;

.container {
  position: relative;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.main {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.backButton {
  padding: 0;
  background: none;
  border: none;
  cursor: pointer;
  position: absolute;
  top: 27px;
  left: 55px;
  @media (min-width: 1700px) {
    left: calc((100vw - 1600px) / 2);
  }
  @media (max-width: 768px) {
    left: 24px;
    top: 24px;
  }
  color: $dark-dark-purple;
  svg {
    vertical-align: middle;
  }
}

.emptyCart {
  padding: 111px 0 200px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  .imageContainer {
    width: 100%;
    position: relative;
    padding-bottom: 83.08%;
    .stickyContainer {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      img {
        width: 100%;
      }
    }
  }
  h4 {
    font-weight: 700;
    font-size: 40px;
    line-height: 60px;
    color: $primary;
    margin: 51px 0 8px 0;
    text-align: center;
    @media (max-width: 768px) {
      font-size: 30px;
      line-height: 45px;
    }
  }
  p {
    font-size: 16px;
    line-height: 24px;
    color: $dark-dark-purple;
    margin: 0 0 39px 0;
    text-align: center;
  }
}

.cartSummary {
  width: 100%;
  max-width: 734px;
  padding: 24px 0 58px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  @media (max-width: 768px) {
    padding: 70px 24px;
  }
  h3 {
    font-size: 48px;
    font-weight: 600;
    line-height: 72px;
    color: #1c1c1c;
    text-align: center;
    margin: 0 0 24px 0;
    @media (max-width: 768px) {
      font-size: 30px;
      line-height: 45px;
    }
  }
  .aboveBar,
  .totalContainer {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
  }
  .totalContainer {
    color: $primary;
    font-weight: 600;
    padding-top: 9px;
  }
  .useTitle {
    width: 100%;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
    color: $dark-dark-purple;
    margin-top: 24px;
    margin-bottom: 12px;
  }
  .useContainer {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-column-gap: 8px;
    width: 100%;
    height: 58px;
    margin-bottom: 12px;
    @media (max-width: 768px) {
      margin-bottom: 50px;
    }
    .singleUse {
      width: 100%;
      height: 100%;
      border-radius: 1000px;
      color: $dark-dark-purple;
      display: grid;
      grid-template-columns: 24px auto;
      grid-column-gap: 15px;
      font-size: 14px;
      line-height: 21px;
      align-items: center;
      justify-content: center;
      background: #eee7f8;
      cursor: pointer;
      @media (max-width: 768px) {
        display: flex;
        flex-direction: column;
        padding: 12px 0;
        border-radius: 20px;
        svg {
          margin-bottom: 6px;
        }
      }
      &.active {
        cursor: auto;
        background: $secondary;
        color: #fff;
      }
    }
  }
}

.paymentMethod {
  background-color: #eff1f7;
  width: 100%;
  border-radius: 24px;
  padding: 24px;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.plansContainer {
  width: 100%;
  margin-top: 12px;
}

.bottomSection {
  width: 100%;
  padding: 42px 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f5efff;
  margin-top: auto;
  @media (max-width: 1120px) {
    padding: 42px 24px;
  }
  @media (max-width: 975px) {
    flex-direction: column;
    align-items: flex-start;
  }
  .left {
    display: grid;
    grid-template-columns: 28px 1fr;
    grid-column-gap: 12px;
    align-items: center;
    justify-content: space-between;
    max-width: 620px;
    @media (max-width: 975px) {
      margin-bottom: 24px;
    }
    .message {
      font-size: 16px;
      font-weight: 400;
      line-height: 22px;
      margin: 0;
      margin-right: 24px;
      @media (max-width: 768px) {
        font-size: 14px;
        line-height: 21px;
      }
    }
    svg {
      width: 28px;
      height: 24px;
    }
  }
  .right {
    display: flex;
    align-items: center;
    justify-content: space-between;
    @media (max-width: 975px) {
      margin-left: auto;
    }
    .price {
      font-size: 24px;
      line-height: 36px;
      font-weight: 700;
      margin-right: 32px;
      @media (max-width: 768px) {
        font-size: 20px;
        line-height: 30px;
      }
    }
  }
}

.balanceContainer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #7448b033;
  border-radius: 16px;
  padding: 8px 8px 8px 16px;
  margin-bottom: 12px;
  .balanceLabel {
    font-size: 12px;
    font-weight: 400;
  }
  .balanceAmount {
    font-size: 24px;
    font-weight: 700;
    line-height: 26px;
  }
}
