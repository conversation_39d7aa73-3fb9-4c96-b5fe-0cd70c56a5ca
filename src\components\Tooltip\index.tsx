import styles from "./tooltip.module.scss";

const Tooltip = ({ show, text, style = {}, children }: any) => {
  return (
    <span className={styles.container} id="">
      {show && (
        <div className={styles.tooltip} style={style}>
          <div dangerouslySetInnerHTML={{ __html: text }} />
          <svg
            width="25"
            height="17"
            viewBox="0 0 25 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14.0848 15.9413C13.2842 16.9813 11.7158 16.9813 10.9152 15.9413L1.12234 3.21999C0.109949 1.90485 1.04748 -2.39998e-06 2.70715 -2.25488e-06L22.2929 -5.42646e-07C23.9525 -3.97553e-07 24.8901 1.90485 23.8777 3.21999L14.0848 15.9413Z"
              fill="white"
            />
          </svg>
        </div>
      )}
      {children}
    </span>
  );
};

export default Tooltip;
