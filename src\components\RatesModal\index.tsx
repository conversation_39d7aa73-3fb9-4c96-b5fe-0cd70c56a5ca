import styles from "./rates-modal.module.scss";
import { Fade } from "@mui/material";
import { useEffect, useState } from "react";
import $ from "jquery";
import { Close } from "../svgs";
import CountryRate from "../CountryRate";
import SearchBar from "../SearchBar";
import Button from "../Button";
import { ApiGet } from "../../pages/api/api";
import Pagination from "../Pagination";
import CountryRateSkeleton from "../CountryRateSkeleton";

const RatesModal = ({ show, setShow }: any) => {

  useEffect(() => {
    if (show) {
      $(document.body).css("overflow-y", "hidden");
    } else {
      $(document.body).css("overflow-y", "scroll");
    }
  }, [show]);

  const [rates, setRates] = useState([]);

  const [page, setPage] = useState(1);

  useEffect(() => {
    setRates([]);
    ApiGet("/rates", page)
      .then((response) => {
        console.log(response);
        setRates(response.data.rates);
      })
      .catch((error) => {});
  }, [page]);

  const [searchQuery, setSearchQuery] = useState("");
  const [queryDisplay, setQueryDisplay] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const handleSearchChange = (e: any) => {
    setSearchQuery(e.target.value);
  };

  const clearSearch = () => {
    setSearchQuery("");
  };

  useEffect(() => {
    if (searchQuery === "") {
      setSearchResults([]);
      setShowSearchResults(false);
    }
  }, [searchQuery]);

  const checkIfFirstWord = (item: any, query: any) => {
    const name = item.countryName;
    return query.some((queryStr: any) => {
      const combinedRegex = new RegExp("^" + queryStr.toLowerCase(), "gi");
      return combinedRegex.test(name.toLowerCase());
    });
  };

  const checkIfAnyWord = (item: any, query: any) => {
    const name = item.countryName;
    return query.some((queryStr: any) => {
      const combinedRegex = new RegExp("\\b" + queryStr.toLowerCase(), "gi");
      return combinedRegex.test(name.toLowerCase());
    });
  };

  const [searchLoading, setSearchLoading] = useState(false);

  const handleSearch = () => {
    /*if (searchQuery === "") return;

    setQueryDisplay(searchQuery);
    let query = searchQuery.split(" ");

    // Filter users to only show users that contain query string
    let toShow = rates.filter((rate: any) => {
      const name = rate.country.countryName;
      return query.some((singleQuery: string) =>
        name.toLowerCase().includes(singleQuery.toLowerCase())
      );
    });

    const firstWord = toShow.some((item: any) => {
      return checkIfFirstWord(item.country, query);
    });

    const anyWord = toShow.some((item: any) => {
      return checkIfAnyWord(item.country, query);
    });

    if (firstWord) {
      toShow = toShow.filter((item: any) => {
        return checkIfFirstWord(item.country, query);
      });
    } else if (anyWord) {
      toShow = toShow.filter((item: any) => {
        return checkIfAnyWord(item.country, query);
      });
    }

    console.log(toShow);

    setSearchResults(toShow);
    setShowSearchResults(true);*/
    if (searchQuery === "") return;

    setQueryDisplay(searchQuery);

    setSearchResults([]);
    setSearchLoading(true);
    ApiGet("/rates", 1, searchQuery)
      .then((response) => {
        console.log(response);
        setSearchResults(response.data.rates);
        setShowSearchResults(true);
        setSearchLoading(false);
      })
      .catch((error) => {
        setSearchLoading(false);
      });
  };

  return (
    <Fade in={show} unmountOnExit>
      <div className={styles.container}>
        <div className={styles.modal}>
          <div className={styles.main} id="modal-scroll">
            <div className={styles.topBar}>
              <h3>Worldwide Rates</h3>
              <div className={styles.searchContainer}>
                <SearchBar
                  handleSearch={handleSearch}
                  id="rates-search-input"
                  searchQuery={searchQuery}
                  placeholder="Search country"
                  handleSearchChange={handleSearchChange}
                  clearSearch={clearSearch}
                />
              </div>
              <button
                onClick={() => {
                  setShow(false);
                }}
                className={styles.closeButton}
              >
                <Close />
              </button>
            </div>
            <div className={styles.ratesContainer}>
              {searchLoading ? (
                [0, 1, 2, 3, 4, 5].map((i) => (
                  <CountryRateSkeleton key={`country-rate-skeleton-${i}`} />
                ))
              ) : showSearchResults ? (
                searchResults.length ? (
                  searchResults.map((rate: any) => (
                    <CountryRate
                      key={`country-rate-${rate.country.countryCode}`}
                      rate={rate}
                    />
                  ))
                ) : (
                  <div className={styles.noResults}>
                    <h4>No Results</h4>
                    <p>
                      We didn’t find any countries named “{queryDisplay}”.
                      <br />
                      Please double-check your text and try again or browse all
                      our countries.
                    </p>
                    <Button onClick={clearSearch}>View all countries</Button>
                  </div>
                )
              ) : rates.length > 0 ? (
                rates.map((rate: any) => (
                  <CountryRate
                    key={`country-rate-${rate.country.countryCode}`}
                    rate={rate}
                  />
                ))
              ) : (
                [0, 1, 2, 3, 4, 5].map((i) => (
                  <CountryRateSkeleton key={`country-rate-skeleton-${i}`} />
                ))
              )}
            </div>
            {!showSearchResults && (
              <div className={styles.pagination}>
                <Pagination
                  currentPage={page}
                  setCurrentPage={setPage}
                  disabled={rates.length === 0}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </Fade>
  );
};

export default RatesModal;
