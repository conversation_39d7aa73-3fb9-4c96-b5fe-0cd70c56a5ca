import styles from "./faq-question.module.scss";
import { Collapse } from "@mui/material";
import { useState } from "react";
import { CaretDown } from "../svgs";
import { resolveRichText } from "../utils/richTextConverter";
import { Link } from "react-router-dom";

const FaqQuestion = ({ question, answer, id }: any) => {
  const [open, setOpen] = useState(false);

  return (
    <div
      className={`${styles.main} ${open && styles.open}`}
      onClick={() => {
        setOpen(!open);
      }}
    >
      <div className={`${styles.question} ${open && styles.open}`}>
        <h4>{question}</h4>
        <CaretDown />
      </div>
      <Collapse in={open}>
        <div
          className={styles.answer}
          dangerouslySetInnerHTML={{ __html: resolveRichText(answer) }}
        />
        <Link to={`/help/${id}`} className={styles.learnMore}>
          Learn more
        </Link>
      </Collapse>
    </div>
  );
};

export default FaqQuestion;
