import styles from "./mobile-nav-menu.module.scss";
import { CSSTransition } from "react-transition-group";
import {
  Close,
  DoorOpen,
  Esim,
  LogOut,
  MagnifyingGlass,
  PaymentCard,
  PaymentHistory,
  User,
} from "../svgs";
import { navLinks } from "../Header";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import {
  clearAllBodyScrollLocks,
  disableBodyScroll,
  enableBodyScroll,
} from "body-scroll-lock";
import Button from "../Button";
import { motion } from "framer-motion";
import $ from "jquery";
import { t } from "i18next";

const MobileNavMenu = ({ open, setOpen, setShowLogoutConfirm }: any) => {
  const location = useLocation();
  const navigate = useNavigate();

  const { loggedIn, userInfo } = useSelector((state: any) => state);

  const dashboardLinks = [
    {
      icon: <Esim />,
      label: t("account.pages.myesims"),
      href: "/dashboard/home",
    },
    {
      icon: <PaymentCard />,
      label: t("account.pages.paymentCards"),
      href: "/dashboard/cards",
    },
    {
      icon: <PaymentHistory />,
      label: t("account.pages.paymentHistory"),
      href: "/dashboard/payments-history",
    },
    {
      icon: <User />,
      label: t("account.account"),
      href: "/dashboard/settings",
    },
  ];

  useEffect(() => {
    setOpen(false);
  }, [location.pathname]);

  /*useEffect(() => {
  const element = document.getElementById("mobile-nav-main")!;

    if (open) {
      disableBodyScroll(element);
    } else {
      enableBodyScroll(element);
    }

    return () => {
      clearAllBodyScrollLocks();
    };
  }, [open]);*/

  const [slideLinksIn, setSlideLinksIn] = useState(false);

  useEffect(() => {
    setSlideLinksIn(open);
  }, [open]);

  const handleActiveLink = (link: any) => {
    if (
      location.pathname.length > 1 &&
      link.length > 1 &&
      location.pathname.includes(link)
    ) {
      return styles.active;
    } else if (
      location.pathname.length === 1 &&
      location.pathname.length === link.length
    ) {
      return styles.active;
    } else {
      return "";
    }
  };

  return (
    <CSSTransition in={open} timeout={500} classNames="slide-top" unmountOnExit>
      <div className={styles.main} id="mobile-nav-main">
        <button
          className={styles.close}
          onClick={() => {
            setOpen(false);
          }}
        >
          <Close />
        </button>
        <Link to="/" className={styles.homeLink}>
          <img
            src="/images-int/logos/orbit_logo.svg"
            alt="Orbit mobile logo"
            className={styles.logo}
          />
        </Link>
        <nav
          className={`${styles.linksContainer} ${slideLinksIn && styles.open}`}
        >
          {navLinks.map((navLink: any, index) => (
            <Link key={index} to={navLink.link}>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
                key={navLink.link}
                className={`${styles.link} ${handleActiveLink(navLink.link)}`}
              >
                {t("pages." + navLink.label)}
              </div>
            </Link>
          ))}
          {loggedIn && (
            <div className={styles.dashLinksContainer}>
              <div className={styles.title}>{t("account.account")}</div>
              {dashboardLinks.map((linkItem: any) => (
                <Link to={linkItem.href}>
                  <div className={styles.dashboardLink}>
                    {linkItem.icon}
                    {linkItem.label}
                  </div>
                </Link>
              ))}
            </div>
          )}
        </nav>
        <div className={styles.buttons}>
          {loggedIn ? (
            <Button
              onClick={() => {
                setOpen(false);
                setShowLogoutConfirm(true);
              }}
              style={{ height: 48 }}
            >
              <DoorOpen />
              {t("account.logout")}
            </Button>
          ) : (
            <>
              <Link
                style={{
                  textDecoration: "none",
                  color: "inherit",
                  marginBottom: 10,
                  width: "100%",
                }}
                to="/login"
              >
                <Button
                  style={{ position: "relative", zIndex: 10, width: "100%" }}
                  color="primary"
                >
                  {t("account.login")}
                </Button>
              </Link>
              <Link
                style={{ textDecoration: "none", color: "inherit" }}
                to="/register"
              >
                <Button
                  style={{ position: "relative", zIndex: 10, width: "100%" }}
                >
                  {t("account.register")}
                </Button>
              </Link>
            </>
          )}
        </div>
      </div>
    </CSSTransition>
  );
};

export default MobileNavMenu;
