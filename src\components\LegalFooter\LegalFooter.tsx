import { t } from "i18next";
import Button from "../Button";
import styles from "./LegalFooter.module.scss"
import { useEffect, useState } from "react";
import { CmsApiGet } from "../../pages/api/cms-api";
import { useTranslation } from "react-i18next";

const LegalFooter = () => {
    const { i18n } = useTranslation();
    const [data, setData] = useState(null as any)

    useEffect(() => {
        CmsApiGet(`/api/legal-footer?locale=${i18n.language}`).then(
            (response: any) => {
              setData(response.data.data.attributes);
            }
          );
    }, [i18n.language])

    return (
        <section className={styles.legalFooter}>
            <div className="flex">
                <div>
                    { data?.sectionTitle && <h2>{ data.sectionTitle }</h2>}
                    { data?.subtitle &&  <p>{ data.subtitle }</p>}
                    <div className={styles.appBtns}>
                        {
                            data?.googlePlayUrl !== '#' && (
                                <Button>
                                    <a href={data?.googlePlayUrl}>
                                        <img
                                            src="/images-int/app-stores/googleStoreNoBorder.svg"
                                            alt={`${t('general.downloadApp')} - Google play store`}
                                        />
                                    </a>
                                </Button>
                            )
                        }
                        {
                            data?.appleUrl !== '#' && (
                                <Button>
                                    <a href={data?.appleUrl}>
                                        <img
                                            src="/images-int/app-stores/appleStoreNoBorder.svg"
                                            alt={`${t('general.downloadApp')} - Apple store`}
                                        />
                                    </a>
                                </Button>
                            )
                        }
                    </div>
                </div>
                <div className={styles.phoneImg}>
                    <img src="/images-int/phone.png" />
                </div>
            </div>
        </section>
    )
}

export default LegalFooter