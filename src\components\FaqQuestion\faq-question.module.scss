@use "../../styles/theme.scss" as *;

.main {
  padding: 16px 24px;
  cursor: pointer;
  margin: auto;
  background-color: #efeeed;
  border-radius: 34px;
  margin-bottom: 16px;
  .question {
    display: flex;
    justify-content: space-between;
    svg {
      height: 24px;
      width: 24px;
      transition: transform 0.3s ease;
      color: #088bdd;
    }
    h4 {
      margin: 0;
      text-align: start;
      font-weight: 700;
      font-size: 18px;
      line-height: 28px;
      color: $secondary;
      @media (max-width: 768px) {
        font-size: 18px;
        line-height: 27px;
      }
      @media (max-width: 425px) {
        font-size: 15px;
        line-height: 23px;
      }
    }
  }
}
.answer {
  padding-top: 16px;
  line-height: 24px;
  @media (max-width: 425px) {
    font-size: 14px;
    line-height: 23px;
  }
  a {
    text-decoration: underline;
    color: #088bdd;
    font-weight: 600;
  }
  p {
    font-weight: 400;
    padding-top: 16px;
    line-height: 24px;
    margin: 0;
    @media (max-width: 768px) {
      font-size: 16px;
      line-height: 24px;
    }
  }
  img {
    width: 29%;
    height: 50%;
    margin: 30px 5px;
  }
}
.open {
  svg {
    transform: rotate(180deg);
  }
}

.learnMore {
  color: #05bfbe;
  font-weight: 700;
  margin-top: 12px;
  display: block;
}
