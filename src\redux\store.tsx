import { createStore } from "redux";
import { v4 as uuidv4 } from "uuid";

const initialState = {
  notifications: [],
  basket: null,
  creditSelection: null,
  blogs: [],
  countries: [],
  plans: [],
  zones: [],
  customZones: [],
  localesList: ["en", "ar"],
  selectedCountry: {
    countryCode: "",
    iconUrl: "",
    countryImage: "",
    countryName: "",
  },
  loggedIn: false,
  userInfo: {
    firstName: "",
    lastName: "",
    currency: null,
    email: "",
    phone: "",
  },
  cards: [],
  toastersOnLoad: {
    registerToCheckout: false,
    addedToBasket: false,
  },
  smsRates: [],
  callRates: [],
  currency: "GBP",
  failedPlans: [],
  isBannerOpen: false,
  isSmartBannerOpen: true
};

const changeState = (state = initialState, { type, payload, ...rest }: any) => {
  switch (type) {
    case "set":
      return { ...state, ...rest };
    case "addToBasket":
      return {
        ...state,
        basket: [payload],
      };
    case "notify":
      //let current = [...state.notifications] as any;
      //current.push({ ...payload, id: uuidv4() });
      const newNotif = { ...payload, id: uuidv4() };
      return { ...state, notifications: [newNotif] };
    case "closeNotification":
      let currentOpen = [...state.notifications] as any;
      currentOpen = currentOpen.filter((item: any) => item.id !== payload);
      return { ...state, notifications: currentOpen };
    default:
      return state;
  }
};

const store = createStore(changeState);
export default store;
