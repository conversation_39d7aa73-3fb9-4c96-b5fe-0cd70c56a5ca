import { useEffect, useState } from "react";
import { Helmet } from 'react-helmet-async';
import styles from "../../styles/legals.module.scss";
import { CmsApiGet } from "../../pages/api/cms-api";
import LegalFooter from "../../components/LegalFooter/LegalFooter";
import { resolveRichText } from "../../components/utils/richTextConverter";
import { t } from "i18next";
import { useTranslation } from "react-i18next";

const TermsAndConditions = () => {
  const { i18n } = useTranslation();
  const [policy, setPolicy] = useState("");

  useEffect(() => {
    CmsApiGet(`/api/terms-and-condition?locale=${i18n.language}&populate=deep`).then(
      (response: any) => {
        setPolicy(response.data.data.attributes.richText);
      }
    );
  }, [i18n.language]);

  return (
    <div className={styles.main}>
      <Helmet>
        <title>{t("general.orbit")}| { t('pages.termsConditions') }</title>
      </Helmet>
      <div className={styles.container}>
        <h1 className={styles.heading}>{ t('pages.termsConditions') }</h1>
        <div
          className={styles.text}
          dangerouslySetInnerHTML={{
            __html: policy ? resolveRichText(policy) : "",
          }}
          id="privacy-content"
        />
        <LegalFooter />
      </div>
    </div>
  );
};

export default TermsAndConditions;
