import { Link, useSearchParams } from "react-router-dom";
import AppButtons from "../../components/AppButtons";
import Button from "../../components/Button";
import styles from "../../styles/payment-success.module.scss";
import { useSelector } from "react-redux";
import FailedPlanSummary from "../../components/FailedPlanSummary";
import { Envelope } from "../../components/svgs";
import { useEffect, useState } from "react";

const PaymentSuccess = ({trackPaymentFn} : { trackPaymentFn: Function  }) => {
  const { failedPlans } = useSelector((state: any) => state);

  const [noData, setNoData] = useState(false);

  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    trackPaymentFn('success')
    let noDataQuery = searchParams.get("noData");
    if (noDataQuery) {
      setNoData(true);
    }
  }, []);

  return (
    <div
      className={styles.container}
      style={{
        backgroundImage:
          failedPlans.length > 0
            ? "url('/images-int/payment/payment-semi-fail.png')"
            : "url('/images-int/payment/payment_success.png')",
      }}
    >
      <div
        className={styles.background}
        style={failedPlans.length > 0 ? { paddingTop: 50 } : {}}
      >
        {failedPlans.length > 0 ? (
          <>
            <h2>Whoops!</h2>
            <p>
              The following plan{failedPlans.length > 1 && "s"}{" "}
              {failedPlans.length > 1 ? "have" : "has"} not been added due to an
              error. Please contact customer support for further assistance.
            </p>
            <div
              className={`${styles.failedPlans} ${
                failedPlans.length > 1 && styles.grid
              }`}
            >
              {failedPlans.map((plan: any) => (
                <FailedPlanSummary planObj={plan} />
              ))}
            </div>
            <div className={styles.failedButtons}>
              <Link
                style={{ textDecoration: "none" }}
                to="mailto:<EMAIL>"
              >
                <Button>
                  <Envelope />
                  <EMAIL>
                </Button>
              </Link>
              <Link
                className={styles.dashboardButton}
                to="/dashboard/home"
                style={{ textDecoration: "none" }}
              >
                <Button>Go to Dashboard</Button>
              </Link>
            </div>
          </>
        ) : (
          <>
            <h2>Payment successful!</h2>
            <p>
              Just one more step! Download our app now and get your account up
              and running in no time!
            </p>
            <Link to="/dashboard/home" style={{ textDecoration: "none" }}>
              <Button style={{ marginTop: 40 }}>Go to Dashboard</Button>
            </Link>
            <div className={styles.appBar}>
              <div className={styles.appText}>
                <h5>Get the app!</h5>
                <p>
                  Build a plan, track your usage and enjoy the freedom as you
                  travel
                </p>
              </div>
              <AppButtons style={{ marginTop: 0 }} />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default PaymentSuccess;
