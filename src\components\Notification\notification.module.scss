@use "../../styles/theme.scss" as *;

.main {
  box-shadow: 0px 0px 20px 0px #0f133a26;
  background: #fafaf9;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  max-width: 750px;
  padding: 16px 18px;
  border-radius: 24px;
  z-index: 3000;
  pointer-events: all;
  margin-bottom: 10px;
  overflow: hidden;
  .textContainer {
    flex-grow: 1;
  }
  .image {
    width: 24px;
    margin-inline-end: 8px;
  }
}

.top {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.heading {
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  color: $secondary;
  margin: 0px;
}

.close {
  background: none;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  margin-left: auto;
  cursor: pointer;
}

.message {
  font-size: 14px;
  line-height: 18px;
  margin: 0;
  color: $secondary;
  margin-inline-start: 33px;
}
