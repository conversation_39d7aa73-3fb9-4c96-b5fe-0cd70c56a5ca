import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { ApiDelete, ApiGet } from "../../pages/api/api";
import Button from "../Button";
import CardTileSkeleton from "../CardTileSkeleton";
import DeleteCardModal from "../DeleteCardModal";
import { Helmet } from 'react-helmet-async';
import SavedCard from "../SavedCard";
import { Plus } from "../svgs";
import styles from "./wallet-section.module.scss";
import { useSearchParams } from "react-router-dom";
import { t } from "i18next";

const WalletSection = ({
  cards,
  getCards,
  cardsLoading,
  setCardsLoading,
}: any) => {
  const dispatch = useDispatch();
  const [activeCard, setActiveCard] = useState(null as any);
  const [showDelete, setShowDelete] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [editingCard, setEditingCard] = useState(false);
  const [deleteResult, setDeleteResult] = useState<"" | "success" | "fail">("");

  const handleDelete = (card: any) => {
    setActiveCard(card);
    setShowDelete(true);
  };

  const handleAfterDeleteAction = () => {
    if (deleteResult === "success") {
      setShowDelete(false);
      setActiveCard(null);
      setDeleteResult("");
    }
    if (deleteResult === "fail") {
      setDeleteResult("");
    }
  };

  const handleEdit = (card: any) => {
    setActiveCard(card);
    setEditingCard(true);
  };

  const handleDeleteFromEdit = () => {
    setEditingCard(false);
    setShowDelete(true);
  };

  const confirmDelete = () => {
    setDeleteLoading(true);
    ApiDelete("/payments/cards", {
      cardId: activeCard.id.toString(),
    })
      .then((response: any) => {
        setDeleteResult("success");
        getCards(() => {
          setDeleteLoading(false);
        });
      })
      .catch((error: any) => {
        setDeleteLoading(false);
        setDeleteResult("fail");
      });
  };

  const handleOpenAddCard = () => {
    ApiGet("/payments/cards/creationscreen").then((response) => {
      const { url, sessionId } = response.data;
      localStorage.setItem("sessionId", sessionId);
      localStorage.setItem("stripeLocation", "wallet");
      window.location.href = url;
    });
  };

  const [searchParams, setSearchParams] = useSearchParams();

  // Check for success of failure
  useEffect(() => {
    const success = searchParams.get("success");
    const failure = searchParams.get("failure");
    if (success) {
      dispatch({
        type: "notify",
        payload: {
          error: false,
          heading: t('account.msgs.success'),
          message: "Your card was saved successfully!",
        },
      });
    } else if (failure) {
      dispatch({
        type: "notify",
        payload: {
          error: true,
          heading: "That didn't work",
          message:
            "Something went wrong while saving your card, please try again",
        },
      });
    }
  }, []);

  return (
    <div className={styles.container}>
      <Helmet>
        <title>{t("general.orbit")} | { t('general.dashboard') } |  { t('account.pages.paymentCards') }</title>
      </Helmet>
      {/*cards.length ? (
        <EditCardModal
          show={editingCard}
          setShow={setEditingCard}
          card={activeCard}
          handleDeleteFromEdit={handleDeleteFromEdit}
        />
      ) : (
        ""
      )*/}
      <DeleteCardModal
        show={showDelete}
        digits={activeCard?.last4Digits}
        setShow={(val: boolean) => setShowDelete(val)}
        loading={deleteLoading}
        confirmDelete={confirmDelete}
        result={deleteResult}
        handleAfterDeleteAction={handleAfterDeleteAction}
      />
      <div className={styles.topBar}>
        <h3>{t("account.pages.paymentCards")}</h3>
        {cards.length !== 0 && (
          <Button onClick={handleOpenAddCard}>
            <Plus />
            {t("account.paymentCards.addCard")}
          </Button>
        )}
      </div>
      <div className={styles.cardsContainer}>
        {cardsLoading ? (
          [0, 1, 2].map((card: any) => (
            <CardTileSkeleton key={`card-skeleton-${card}`} />
          ))
        ) : cards.length ? (
          cards.map((card: any) => (
            <SavedCard
              card={card}
              getCards={getCards}
              setCardsLoading={setCardsLoading}
              handleDelete={handleDelete}
              handleEdit={handleEdit}
              key={`card-skeleton-${card.id}`}
            />
          ))
        ) : (
          <div className={styles.noCards}>
            <img
              src="/images-int/dashboard/no_plans.svg"
              className={styles.image}
            />
            <h5>{t("account.paymentCards.noCards")}</h5>
            <p>{t("account.paymentCards.noCardsMsg")}</p>
            <Button onClick={handleOpenAddCard}>
              <Plus />
              {t("account.paymentCards.addCard")}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default WalletSection;
