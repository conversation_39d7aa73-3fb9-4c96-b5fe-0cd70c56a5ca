import moment from "moment";

const formatDate = (dateInput: any) => {
  const date = new Date(dateInput);
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear().toString();
  return `${day}/${month}/${year}`;
};

export const months = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

export const formatDateWords = (dateInput: any, shortDate = false) => {
  const date =
    typeof dateInput === "string"
      ? moment(dateInput.replace(/-/g, "/").replaceAll("Z", ""))
      : moment(dateInput);
  const day = date.date().toString();
  const month = months[date.month()];
  const year = shortDate
    ? date.year().toString().slice(2)
    : date.year().toString();
  return `${day} ${month} ${year}`;
};

export default formatDate;
