import { SwitchTransition, CSSTransition } from "react-transition-group";
import { Briefcase, User } from "../svgs";
import styles from "./number-type-switch.module.scss";

const NumberTypeSwitch = ({ type, setType, disabled }: any) => {
  return (
    <div className={styles.container}>
      <div className={styles.label}>Number Type</div>
      <div
        className={`${styles.switchContainer} ${disabled && styles.disabled}`}
        onClick={() => {
          if (!disabled) {
            if (type === "mobile") {
              setType("landline");
            } else {
              setType("mobile");
            }
          }
        }}
      >
        <div className={styles.innerLabel}>
          <User />
          Personal
        </div>
        <div className={styles.innerLabel}>
          <Briefcase />
          Business
        </div>
        <div
          className={`${styles.thumb} ${
            type === "landline" && styles.business
          }`}
        >
          <SwitchTransition>
            <CSSTransition
              key={type}
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="quick-fade"
            >
              <div style={{ display: "flex", alignItems: "center" }}>
                {type === "mobile" ? (
                  <>
                    <User />
                    Personal
                  </>
                ) : (
                  <>
                    <Briefcase />
                    Business
                  </>
                )}
              </div>
            </CSSTransition>
          </SwitchTransition>
        </div>
      </div>
    </div>
  );
};

export default NumberTypeSwitch;
