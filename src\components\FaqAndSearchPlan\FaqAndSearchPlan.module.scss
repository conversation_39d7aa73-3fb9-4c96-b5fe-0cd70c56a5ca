@use "../../styles/theme.scss" as *;

.faqAndSearchPlan {
  .searchSection {
    background-color: $secondary;
    padding: 60px 0;
    color: #fff;
    text-align: center;
    @media (max-width: 768px) {
      padding: 30px 40px;
    }
    svg,
    button,
    input {
      color: #fff !important;
    }
    #countries_search_input {
      caret-color: #fff;
    }
    h2 {
      font-size: 48px;
      font-weight: 700;
      line-height: 61.32px;
      letter-spacing: -0.025em;
      text-align: center;
      margin: 0px;
      margin-bottom: 16px;

      @media (max-width: 768px) {
        font-size: 26px;
      }
    }
    .searchResults {
      background-color: #fff;
      position: absolute;
      width: 27%;
      margin: auto;
      border-radius: 11px;
      margin-top: 5px;
      right: 37%;
      padding: 20px 30px;
      font-size: 15px;
      box-shadow: 0px 4px 20px 0px #00000040;
      @media (max-width: 1024px) {
        width: 40%;
      }
      @media (max-width: 768px) {
        width: 52%;
        margin-top: -10px;
      }
      @media (max-width: 768px) {
        width: 85%;
        right: 7%;
      }
      @media (max-width: 320px) {
        padding: 20px;
        font-size: 13px;
      }
      a {
        color: #000c24;
        text-decoration: none;
      }
      div {
        margin-bottom: 15px;
        img {
          width: 25px;
          height: 25px;
          border-radius: 50%;
          @media (max-width: 320px) {
            width: 21px;
            height: 21px;
          }
        }
        span {
          margin-left: 15px;
        }
      }
    }
  }
  .faqSection {
    padding: 60px 200px;
    display: flex;
    flex-direction: column;
    @media (max-width: 768px) {
      padding: 60px 20px;
    }
    .faqHeading {
      font-size: 48px;
      font-weight: 700;
      color: $secondary;
      text-align: center;
      @media (max-width: 768px) {
        font-size: 40px;
      }
      @media (max-width: 768px) {
        font-size: 30px;
      }
    }
    .faqText {
      color: $secondary;
      font-size: 18px;
      font-weight: 400;
      width: 50%;
      margin: 10px auto 50px;
      text-align: center;
      @media (max-width: 768px) {
        width: 72%;
      }
      @media (max-width: 768px) {
        width: 85%;
        font-size: 15px;
      }
    }
    a {
      display: inline-block;
      text-decoration: none;
      margin-top: 25px;
    }
    button {
      margin: auto;
      padding: 0 30px;
    }
  }
}
