import { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { ApiDelete } from "../../pages/api/api";
import { useDispatch, useSelector} from "react-redux";
import { deleteUser } from "firebase/auth";
import auth from "../../components/utils/firebaseAuth";
import Loader from "../../components/utils/Loader";
import { t } from "i18next";

const DeleteAccount = () => {
    const [searchParams, setSearchParams] = useSearchParams();
    const { userInfo } = useSelector((state: any) => state);
    const [loading, setLoading] = useState(false);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const deleteAccount = (authToken:any) => {
        let error = false
        ApiDelete("/users", {
          otpToken: authToken,
        })
          .then(async (response:any) => {
            await deleteUser(auth.currentUser)
            .then((response: any) => {
                dispatch({
                  type: "notify",
                  payload: {
                    error: false,
                    heading: t('account.msgs.success'),
                    message: response.data.message,
                  },
                });
                dispatch({
                  type: "set",
                  loggedIn: false,
                });
                dispatch({
                  type: "set",
                  userInfo: {
                    firstName: "",
                    lastName: "",
                    currency: null,
                  },
                });
              })
            .catch((error) => {
              if (error) {
                error = true 
              }
            });
            localStorage.removeItem("expiryTime");
            localStorage.removeItem("basket");
            localStorage.removeItem("token")
              dispatch({
                type: "notify",
                payload: {
                  error: false,
                  heading: t('account.msgs.success'),
                  message: response.data.message,
                },
              });
              if (!error) {
                navigate("/login")
              }
          })
          .catch((error) => {
            error = true
            dispatch({
              type: "notify",
              payload: {
                heading: "Something went wrong",
                message: error.response.data.message || "Please try again.",
                error: true,
              },
            });
          })
      };

    useEffect(() =>  {
        const authToken: any = searchParams.get("auth");
        if (authToken) {
          setLoading(true);
          deleteAccount(authToken)
        } else {
            navigate("/login");
        }
    }, [userInfo]);
    
    return (
        <Loader />
    )
}

export default DeleteAccount