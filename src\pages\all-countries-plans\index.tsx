import { CaretDown } from "../../components/svgs";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import SearchBar from "../../components/SearchBar";
import { ApiGet } from "../api/api";
import styles from "../../styles/allCountires.module.scss";
import plansStyles from "../../styles/region-plans.module.scss";
import PlanTile from "../../components/PlanTile";
import PlanTileSkeleton from "../../components/PlanTileSkeleton";
import { NoSearchResults } from "../../components/NoSearchResults/NoSearchResults";
import { t } from "i18next";
import { Helmet } from "react-helmet-async";
import { useMediaQuery } from "@mui/material";
import { productSchema, countriesServedMarkUp } from "../../components/utils/schemaMarkups";

const AllCountriesPlans = () => {
  const { countries } = useSelector((state: any) => state);
  const [isOpen, setIsOpen] = useState(false);
  const [plans, setPlans] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [plansLoading, setPlansLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(null);

  const under768 = useMediaQuery("(max-width: 768px)");

  useEffect(() => {
    setSearchResults(countries);
  }, [countries]);

  useEffect(() => {
    if (searchQuery === "") {
      clearSearch();
    } else {
      handleSearch();
    }
  }, [searchQuery]);

  const handleSearchChange = (e: any) => {
    setSearchQuery(e.target.value);
  };

  const clearSearch = () => {
    setSearchQuery("");
    setSearchResults(countries);
  };

  const handleSearch = () => {
    if (searchQuery === "") {
      clearSearch();
      return;
    }
    const searchStrings = searchQuery.split(" ");
    let filteredCountries = countries.filter((country: any) =>
      searchStrings.some(
        (str: string) =>
          country.countryName.toLowerCase().includes(str.toLowerCase()) ||
          country.countryCode.toLowerCase().includes(str.toLowerCase())
      )
    );
    setSearchResults(filteredCountries);
  };

  const handleSelect = (code: any, index: any) => {
    if (code === selectedCountry) {
      setIsOpen(false);
      setSelectedCountry("");
      setSelectedIndex(null);
    } else {
      setPlansLoading(true);
      setSelectedCountry(code);
      setSelectedIndex(index);
      setIsOpen(true);
      ApiGet(`/plans?countryCode=${code}`).then((response: any) => {
        if (response) {
          setPlansLoading(false);
          setPlans(response.data.esimPlans);
        }
      });
    }
  };

  const returnPlans = () => {
    return (
      <div className={plansStyles.selectedCountry}>
        {plansLoading ? (
          Array.from({ length: 5 }).map((x, i) => (
            <PlanTileSkeleton index={i} />
          ))
        ) : plans.length > 0 ? (
          plans.map((plan: any, index) => {
            return <PlanTile plan={plan} index={index} />;
          })
        ) : (
          <p>No Plans Found</p>
        )}
      </div>
    );
  };

  return (
    <>
      <div className={styles.plansView}>
        <Helmet>
          <title>
            {t("general.orbit")}| {t("plan.discoverPlan")}
          </title>
          <script type="application/ld+json">
            {plans.length > 0 ? JSON.stringify(productSchema(plans ,'plans/countries')) :
             JSON.stringify(countriesServedMarkUp(countries, 'all countries' ,'plans/countries'))}
          </script>
          <meta name="description" content={t("plan.ifTravelCovered")} />
        </Helmet>
        <h2
          dangerouslySetInnerHTML={{ __html: t("slogans.stayConnected") }}></h2>
        <p>{t("general.allCountries")}</p>
        <p></p>
        <SearchBar
          handleSearch={handleSearch}
          id="plan-search-input"
          searchQuery={searchQuery}
          placeholder={t("placeholders.searchCountry")}
          handleSearchChange={handleSearchChange}
          clearSearch={clearSearch}
          maxWidth={420}
        />
      </div>
      <div className={"flex " + plansStyles.contianer}>
        <div
          className={
            "flex flex-wrap " +
            plansStyles.countryContainer + ' ' + plansStyles.center
          }>
          {searchResults.length > 0 ? (
            searchResults.map((country: any, index: number) => {
              return (
                <>
                  <div
                    key={country.countryCode}
                    onClick={() => handleSelect(country.countryCode, index)}
                    className={
                      (selectedCountry === country.countryCode
                        ? plansStyles.open
                        : "") +
                      " " +
                      plansStyles.countryBox +
                      " " +
                      plansStyles.countryBoxRegion
                    }
                  >
                    <div
                      className={plansStyles.flag}
                      style={{ backgroundImage: `url(${country.iconUrl})` }}
                    />
                    <p>{country.countryName}</p>
                    <CaretDown />
                  </div>
                  {under768
                    ? selectedCountry === country.countryCode && (
                        <>{isOpen && returnPlans()}</>
                      )
                    : ((index + 1) % 5 === 0 ||
                        index + 1 === searchResults.length) && (
                        <div
                          className="w-full"
                          style={{
                            display:
                              selectedIndex !== null &&
                              Math.ceil((index + 1) / 5) ===
                                Math.ceil((selectedIndex + 1) / 5)
                                ? "block"
                                : "none",
                          }}
                          key={country.countryCode + index}
                        >
                          {isOpen && returnPlans()}
                        </div>
                      )}
                </>
              );
            })
          ) : (
            <NoSearchResults query={searchQuery} />
          )}
        </div>
      </div>
    </>
  );
};

export default AllCountriesPlans;
