@use "../styles/theme.scss" as *;

.main {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 32px 24px 90px 24px;
}

.planTypes {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: flex-start;
  flex-grow: 1;
  @media (max-width: 768px) {
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .planTypeContainer {
    width: 100%;
    max-width: 374px;
    margin-right: 28px;
    @media (max-width: 990px) {
      margin-right: 18px;
    }
    @media (max-width: 768px) {
      margin-right: 0px;
      margin-bottom: 24px;
    }
    &:last-child {
      margin-right: 0;
      margin-bottom: 0px;
    }
    .popularContainer {
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: center;
      margin-bottom: 5px;
      svg {
        margin-right: 9px;
      }
      .popularText {
        font-family: Poppins;
        font-size: 20px;
        font-weight: 600;
        line-height: 30px;
        color: $secondary;
      }
    }
    .infoContainer {
      display: flex;
      width: 100%;
      align-items: flex-start;
      justify-content: space-between;
      a {
        text-decoration: none;
      }
      .name {
        font-weight: 600;
        font-size: 20px;
        line-height: 30px;
        @media (max-width: 990px) {
          font-size: 18px;
          line-height: 27px;
          margin-right: 8px;
        }
      }
      .info {
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        @media (max-width: 990px) {
          font-size: 14px;
          line-height: 21px;
          margin-right: 8px;
        }
      }
    }
    .imageContainer {
      position: relative;
      border-radius: 24px;
      height: auto;
      width: 100%;
      padding-bottom: 116%;
      background-size: cover;
      background-position: center;
      overflow: hidden;
      margin-bottom: 16px;
      &.combo {
        background-image: url("/../public/images-int/shop/combo.png");
      }
      &.data {
        background-image: url("/../public/images-int/shop/data.png");
      }
      &.number {
        background-image: url("/../public/images-int/shop/phone.png");
      }
      .imageMask {
        position: absolute;
        border-radius: 8px;
        top: 0px;
        left: 0px;
        bottom: 0px;
        right: 0px;
        background: linear-gradient(
          180deg,
          rgba(21, 13, 33, 0) 0%,
          rgba(21, 13, 33, 0.2) 100%
        );
        z-index: 50;
      }
      .freeCredit {
        position: absolute;
        top: 13px;
        right: 11px;
        width: 27%;
      }
    }
  }
}

.addedToBasket {
  position: absolute;
  z-index: 100;
  top: -12px;
  background: #fff;
  width: 100%;
  max-width: 1075px;
  height: 126px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
  border-radius: 0 0 12px 12px;
  padding: 21px 31px;
  display: flex;
  align-items: center;
  @media (max-width: 1120px) {
    max-width: initial;
    border-radius: 0px;
    padding: 40px 35px;
    height: auto;
  }
  @media (max-width: 768px) {
    flex-direction: column;
    padding: 35px 21px 9px 21px;
  }
  img {
    width: 72px;
    margin-right: 40px;
  }
  .planInfo {
    h5 {
      font-weight: 700;
      font-size: 24px;
      line-height: 36px;
      color: $primary;
      margin: 0 0 8px 0;
    }
    .data {
      font-size: 14px;
      line-height: 21px;
      color: $primary;
    }
  }
  .buttons {
    display: flex;
    margin-left: auto;
    @media (max-width: 768px) {
      margin-top: 20px;
      flex-direction: column;
      margin-left: 0px;
      width: 100%;
      button {
        margin: 0;
        width: 100%;
        margin-bottom: 12px;
      }
    }
  }
}

.countryName {
  display: flex;
  align-items: center;
  background-color: #854ce71a;
  padding: 4px 12px;
  border-radius: 16px;
  .flag {
    width: 16px;
    height: 16px;
    border-radius: 24px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    margin-right: 6px;
  }
  .text {
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: #7448b0;
  }
}

.shopHeading {
  font-size: 48px;
  font-weight: 600;
  line-height: 72px;
  margin: 24px 0 8px 0;
  text-align: center;
}

.shopTagline {
  font-size: 20px;
  font-weight: 400;
  line-height: 26px;
  margin: 0 0 34px 0;
  text-align: center;
}
