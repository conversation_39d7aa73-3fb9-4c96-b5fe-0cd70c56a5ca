.contianer {
  padding: 20px 200px;
  flex-direction: column;
  a {
    text-decoration: none;
    font-weight: 600;
    color: #088bdd;
    display: flex;
    align-items: center;
  }
  @media (max-width: 1250px) {
    padding: 24px;
  }
  @media (max-width: 786px) {
    padding: 20px 16px;
  }
  h4 {
    font-size: 26px;
  }
  .selectedCountry {
    margin-top: 15px;
    border: 1px solid #000;
    border-radius: 25px;
    padding: 20px;
    gap: 8px;
    flex-wrap: wrap;
    display: flex;
  }
  .countryContainer {
    gap: 15px;
    justify-content: flex-start;
    &.center {
      justify-content: center;
    }
    @media (max-width: 768px) {
      gap: 8px;
    }
    .countryBox {
      background-color: #efeeed;
      padding: 25px 15px 10px 15px;
      border-radius: 12px;
      width: 18%;
      cursor: pointer;
      text-align: center;
      @media (max-width: 768px) {
        text-align: left;
      }
      @media (max-width: 425px) {
        padding: 25px 30px 10px 30px;
        width: auto;
        border-radius: 27px;
      }
      .flag {
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin: 0 auto 8px auto;
        @media (max-width: 768px) {
          width: 40px;
          height: 40px;
        }
      }
      p {
        width: 100%;
        margin: 0;
        font-size: 15px;
        @media (max-width: 768px) {
          width: auto;
          margin-right: auto;
        }
      }
    }
    .countryBoxRegion {
      span {
        font-size: 14px;
        color: #6F7189;
        display: block;
      }
      @media (max-width: 768px) {
        width: 100%;
        max-width: 450px;
        margin: 0 auto;
        display: flex;
        justify-content: flex-start;
        padding: 18px 16px;
        align-items: center;
        border-radius: 24px;
        .flag,
        img {
          width: 20px;
          height: 20px;
          margin: 0px;
          margin-right: 8px;
        }
      }
    }
    .open {
      background-color: #fff;
      border: 1px solid #000;
      &.open svg {
        transform: rotate(180deg);
      }
    }
  }
  .regionContainer {
    gap: 15px;
    @media (max-width: 425px) {
      flex-direction: column;
    }
    .regionBox {
      background-size: cover;
      background-position: bottom;
      border-radius: 24px;
      width: calc(100% / 3.2);
      text-align: start;
      padding: 32px;
      cursor: pointer;
      min-height: 230px;
      @media (max-width: 425px) {
        width: 100%;
      }
      h2 {
        font-size: 24px;
        margin: 0px;
        line-height: 28px;
      }
      p {
        font-size: 18px;
        line-height: 28px;
        margin: 0;
      }
    }
  }
  svg {
    width: 25px;
    transition: transform 0.3s ease;
    color: #088bdd;
    @media (max-width: 768px) {
      flex-grow: 1;
      right: 40px;
      position: absolute;
      height: 24px;
      width: 24px;
    }
  }
}
.select_all {
  button {
    margin-top: 20px;
    a {
      color: #fff;
      text-decoration: none;
    }
  }
}
