import { Fade } from "@mui/material";
import { motion } from "framer-motion";
import styles from "./modal.module.scss";

const Modal = ({
  show,
  style = {},
  clickOff = null,
  noTransition,
  children,
}: any) => {
  return (
    <Fade in={show} unmountOnExit>
      <div
        className={styles.container}
        onClick={() => {
          if (clickOff) clickOff();
        }}
      >
        {noTransition ? (
          <div
            className={styles.modal}
            style={style}
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <div className={styles.main} id="modal-scroll">
              {children}
            </div>
          </div>
        ) : (
          <motion.div
            layout
            transition={{ durarion: 300, easing: "ease-out" }}
            className={styles.modal}
            style={style}
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <div className={styles.main} id="modal-scroll">
              {children}
            </div>
          </motion.div>
        )}
      </div>
    </Fade>
  );
};

export default Modal;
