import { Link } from "react-router-dom";
import styles from "./country.module.scss";

const Country = ({ image, name, flag, countryCode }: any) => {
  return (
    <Link
      style={{ textDecoration: "none", color: "inherit" }}
      to={`/shop/select-plan/${countryCode}`}
    >
      <div className={styles.main}>
        <div className={styles.imageContainer}>
          <div className={styles.stickyImageContainer}>
            <img src={image === "NoImage" ? "/no_image.png" : image} />
          </div>
        </div>
        <div className={styles.nameContainer}>
          <div
            className={styles.flag}
            style={{ backgroundImage: `url(${flag})` }}
          />
          {name}
        </div>
      </div>
    </Link>
  );
};

export default Country;
