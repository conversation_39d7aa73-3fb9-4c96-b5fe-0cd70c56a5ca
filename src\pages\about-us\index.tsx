import { Tick } from "../../components/svgs";
import styles from "../../styles/aboutus.module.scss";
import { useEffect, useState } from "react";
import { cmsURL, CmsApiGet } from "../api/cms-api";
import { useTranslation } from "react-i18next";
import { t } from "i18next";
import { Helmet } from 'react-helmet-async';
import { useSelector } from "react-redux";
import FaqAndSearchPlan from "../../components/FaqAndSearchPlan/FaqAndSearchPlan";
import { organizationSchema } from "../../components/utils/schemaMarkups";

export const AboutUs = () => {
  const { i18n } = useTranslation();
  const { isSmartBannerOpen } = useSelector((state: any) => state);
  const [data, setData] = useState<any>(null);

  useEffect(() => {
    CmsApiGet(`/api/about-us?locale=${i18n.language}&populate=deep`).then(
      (response: any) => {
        setData(response.data.data.attributes);
      }
    );
  }, [i18n, i18n.language]);

  return (
    <div className={styles.aboutus}>
      {
        data && (
          <Helmet>
            <title>{ t("general.orbit")}| {data?.metaTitle}</title>
            <script type="application/ld+json">
              {JSON.stringify(organizationSchema(data))}
            </script>
            <meta name="description" content={data?.metaDescription} />
          </Helmet>
        )
      }
      <div className={styles.ourStory}>
        <div style={{ paddingTop: isSmartBannerOpen ? '50px' : '0' }}></div>
        <h1>{data?.ourStory.title}</h1>
        <p>{data?.ourStory.briefText}</p>
        <img
          src={cmsURL + data?.ourStory.ourStoryImg.data.attributes.url}
          alt="Orbit Mobile E-sim Story"
        />
      </div>
      {/*
      <div className={styles.keyFacts}>
        <div className={styles.container}>
          <h2>{data?.keyfacts.title}</h2>
          <div className="flex justify-content-between">
            {data?.keyfacts.fact.map((item: any, index: number) => (
              <div key={index}>
                <img
                  src={cmsURL + item.factImg.data.attributes.url}
                  alt="Orbit Mobile E-sim Story"
                />
                <h2>{item.figure}</h2>
                <p>{item.fact}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
      */}
      <div
        className={
          "flex items-center justify-content-between max-width-container " +
          styles.message
        }
      >
        <div>
          <img
            src={cmsURL + data?.message.profile.data.attributes.url}
            alt="Orbit Mobile E-sim Story"
          />
        </div>
        <div>
          <h2>{data?.message.title}</h2>
          <p>{data?.message.vision}</p>
        </div>
      </div>
      <div className={styles.valuesContainer}>
        <div
          className={`${styles.values} flex justify-content-between max-width-container`}
        >
          <div>
            <h2>{data?.values.title}</h2>
            <p>{data?.values.briefText}</p>
            <div>
              {data?.values.values.map((item: any) => (
                <div key={item.id} className="flex items-center">
                  <Tick />
                  <div>
                    <h4>{item.value}</h4>
                    <p>{item.briefText}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="flex justify-content-center">
            <img
              src={cmsURL + data?.values.sideImg.data.attributes.url}
              alt=""
            />
          </div>
        </div>
      </div>
      <FaqAndSearchPlan />
    </div>
  );
};
