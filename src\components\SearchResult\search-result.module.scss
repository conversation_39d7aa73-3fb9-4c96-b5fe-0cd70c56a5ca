@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  max-width: 1440px;
  border-bottom: 1px solid $secondary;
  padding-bottom: 20px;
  margin-bottom: 20px;
  h5 {
    font-weight: 600;
    font-size: 18px;
    line-height: 27px;
    color: $dark-dark-purple;
    margin: 0;
    margin-bottom: 12px;
  }
  .summary {
    font-weight: 400;
    font-size: 14px;
    line-height: 170%;
    color: $dark-dark-purple;
    margin: 0 0 12px 0;
    p {
      margin: 0;
    }
    a {
      color: $secondary;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.bottom {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  .date {
    font-size: 12px;
    line-height: 18px;
    color: $dark-dark-purple;
  }
  .breadcrumbs {
    font-size: 12px;
    line-height: 18px;
    color: $secondary;
    .divider {
      margin: 0 4px;
    }
    a {
      text-decoration: none;
      color: $secondary;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
