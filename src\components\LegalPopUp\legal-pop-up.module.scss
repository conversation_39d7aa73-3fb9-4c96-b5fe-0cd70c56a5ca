@use "../../styles/theme.scss" as *;

.main {
  box-shadow: 0px 4px 10px rgba(22, 11, 42, 0.15);
  background: #fff;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 90%;
  max-width: 1124px;
  padding: 35px 50px;
  border-radius: 24px;
  z-index: 3000;
  pointer-events: all;
  overflow: hidden;
  margin-top: 15px;
  @media (max-width: 1100px) {
    flex-direction: column;
  }
  @media (max-width: 620px) {
    padding: 34px 24px;
  }
}

.heading {
  font-weight: 700;
  font-size: 18px;
  line-height: 27px;
  color: $secondary;
  margin: 0;
  @media (max-width: 620px) {
    text-align: center;
  }
}

.message {
  font-size: 18px;
  line-height: 27px;
  margin: 0;
  color: $secondary;
  font-weight: 500;
  margin-bottom: 6px;
}

.buttons {
  display: flex;
  align-items: center;
  margin-inline-start: auto;
  @media (max-width: 1100px) {
    margin: 30px 0 0 0;
  }
  @media (max-width: 620px) {
    flex-direction: column-reverse;
    margin: 24px 0 0 0;
    a {
      margin-top: 16px;
    }
    button {
      width: 100%;
    }
  }
}
