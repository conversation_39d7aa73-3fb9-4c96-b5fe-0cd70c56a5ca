@use "../../styles/theme.scss" as *;

.main {
  padding: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  text-align: center;
  @media (max-width: 768px) {
    padding: 24px;
    height: auto;
  }
  h4 {
    color: $primary;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    margin: 44px 0 20px 0;
    @media (max-width: 768px) {
      font-size: 20px;
      line-height: 30px;
      margin: 24px 0 16px 0;
    }
  }
  p {
    font-size: 16px;
    line-height: 24px;
    color: $dark-dark-purple;
    margin: 0 0 50px 0;
    @media (max-width: 768px) {
      margin: 0 0 16px 0;
    }
  }
}

.buttons {
  display: flex;
  align-items: center;
  margin-top: auto;
  @media (max-width: 680px) {
    flex-direction: column;
    width: 100%;
    margin-top: 50px;
    button {
      width: 100%;
      margin: 0 0 12px 0 !important;
    }
  }
}
