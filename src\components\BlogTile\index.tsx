import styles from "./blog-tile.module.scss";
import moment from "moment";
import { cmsURL } from "../../pages/api/cms-api";
import { resolveRichText } from "../../components/utils/richTextConverter";
import { BookOpen } from "../svgs";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";

const BlogTile = ({ blog, featured }: any) => {

  const dateFormat: any = moment(blog.attributes.createdAt).format("DD MMM YYYY");
  const [readingTime, setReadingTime] = useState(0)
  const navigate = useNavigate()

  useEffect(() => {
    if (blog.attributes.article ) {
      const div = document.getElementById(`privacy-content-${blog.id}`)
      const paragraphs = div?.getElementsByTagName('p')
      let totalText = ''
      if (paragraphs) {
        for (let p of paragraphs) {
          totalText += p.textContent || p.innerText;  // Get the text content of the paragraph
        }
        const amountOfWords = totalText.split(/\s+/).length
        setReadingTime(Math.round(amountOfWords / 200))
      }
    }
  }, [blog])

  return (
      <div className={styles.main + ' ' + (featured ? styles.featured : '')} onClick={() => navigate(`/blog/${blog.attributes.slug}`)}>
        <div className={styles.imageWrapper} style={{ backgroundImage: `url(${cmsURL + blog.attributes.thumbnail.data.attributes.url})`}}>
          <div className={styles.readingCounter}>
            <BookOpen />
            <span>{readingTime < 1 ? 1 : readingTime} { t('general.mins') }</span>
          </div>
        </div>
        <div className={styles.text}>
          <h5>{blog.attributes.title}</h5>
          <div
            className={styles.excerpt}
            dangerouslySetInnerHTML={{
              __html: blog.attributes.article ? resolveRichText(blog.attributes.article) : "",
            }}
            id={`privacy-content-${blog.id}`}
          />
          <div className={styles.authorData}>
            <img src={cmsURL + blog.attributes.thumbnail.data.attributes.url} />
            <div>
              <h6>{blog.attributes.author}</h6>
              <p>{dateFormat}</p>
            </div>
          </div>
        </div>
      </div>
  );
};

export default BlogTile;
