@use "../../styles/theme.scss" as *;

.main {
  box-shadow: 0px 4px 10px rgba(22, 11, 42, 0.15);
  background: #fff;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 90%;
  max-width: 1124px;
  padding: 35px 50px;
  border-radius: 24px;
  z-index: 3000;
  pointer-events: all;
  overflow: hidden;
  margin-top: 15px;
  @media (max-width: 1150px) {
    flex-direction: column;
  }
  @media (max-width: 768px) {
    padding: 35px 24px;
  }
}

.heading {
  font-weight: 700;
  font-size: 18px;
  line-height: 27px;
  color: $secondary;
  margin: 0;
}

.cookieContainer {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  svg {
    margin-inline-end: 16px;
  }
}

.message {
  font-size: 18px;
  line-height: 27px;
  margin: 0;
  color: $secondary;
  margin-bottom: 6px;
  font-weight: 500;
  @media (max-width: 768px) {
    font-size: 16px;
    line-height: 24px;
  }
}

.buttons {
  display: flex;
  align-items: center;
  margin-left: auto;
  @media (max-width: 1150px) {
    margin: 30px 0 0 0;
    a {
      margin-right: 20px;
    }
  }
  @media (max-width: 768px) {
    margin: 16px 0 0 0;
  }
}
