import { validateAll } from "indicative/validator";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import Button from "../../components/Button";
import { Input } from "../../components/Input";
import SocialLogin from "../../components/SocialLogin";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
} from "../../components/utils/InputHandlers";
import styles from "../../styles/login.module.scss";
import { ApiGet, ApiPost } from "../api/api";
import { useTranslation } from "react-i18next";
import RewardModal from "../../components/RewardModal";
import { signInWithEmailAndPassword } from "firebase/auth";
import auth from "../../components/utils/firebaseAuth";
import { t } from "i18next";
import { Helmet } from 'react-helmet-async';

// Input fields which are required
const fields = ["email", "password"];

// Get FLV rules and messages
const rules = getRules(fields);

const Login = () => {
  const dispatch = useDispatch();
  const { i18n } = useTranslation();
  const navigate = useNavigate();
  const { basket, loggedIn, isSmartBannerOpen } = useSelector((state: any) => state);
  const [messages, setMessages] = useState(getMessages(fields))

  useEffect(() => {
    setMessages(getMessages(fields))
  }, [i18n.language])

  useEffect(() => {
    if (loggedIn) {
      if (basket !== null) {
        navigate("/checkout");
      } else {
        navigate("/dashboard/home");
      }
    }
  }, [loggedIn]);

  /***********  Init State  ***********/

  const [loginInfo, setLoginInfo] = useState(createStateObject(fields));

  const [loading, setLoading] = useState(false);

  const login = () => {
    const data = {
      email: loginInfo.email.trim(),
      password: loginInfo.password.trim(),
    };

    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        dispatch({
          type: "set",
          notifications: [],
        });
        ApiPost("/users/login", {
          emailId: data.email,
          password: data.password,
        })
          .then((response: any) => {
            signInWithEmailAndPassword(auth, data.email, data.password)
              .then(() => {})
              .catch((error) => {
                const errorCode = error.code;
                const errorMessage = error.message;
                console.log(errorCode, errorMessage);
              });
            loginSuccess(response);
          })
          .catch((error: any) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                heading: "That didn't work",
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setLoginInfo);
      });
  };

  const appleLogin = (response: any) => {
    setLoading(true);
    ApiPost("/users/apple/login", response)
      .then((response) => {
        loginSuccess(response);
      })
      .catch((error: any) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: "That didn't work",
            message: error.response.data.message,
          },
        });
      });
  };

  const facebookLogin = (response: any) => {
    setLoading(true);
    ApiPost("/users/facebook/login", response)
      .then((response) => {
        loginSuccess(response);
      })
      .catch((error: any) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: "That didn't work",
            message: error.response.data.message,
          },
        });
      });
  };

  const googleLogin = (token: any) => {
    setLoading(true);
    ApiPost("/users/google/login", {
      token: token,
    })
      .then((response) => {
        loginSuccess(response);
      })
      .catch((error: any) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: "That didn't work",
            message: error.response.data.message,
          },
        });
      });
  };

  const loginSuccess = (response: any) => {
    setLoading(false);
    dispatch({
      type: "notify",
      payload: {
        error: false,
        heading: t('account.msgs.success'),
        message: i18n.language === 'en' ? response.data.message : 'تم تسجيل الدخول. مرحبًا بعودتك!',
      },
    });
    const token = response.data.auth;

    //  const hasReward = response.data.unusedReward;

    localStorage.setItem("token", token);
    dispatch({
      type: "set",
      plans: [],
    });
    ApiGet("/users").then((response: any) => {
      dispatch({
        type: "set",
        loggedIn: true,
      });
      /*  if (hasReward) {
        setShowRewardModal(true);
      } else {*/
      dispatch({
        type: "set",
        userInfo: {
          firstName: response.data.firstName,
          lastName: response.data.lastName,
          email: response.data.email,
        },
      });
    });
  };

  const [showRewardModal, setShowRewardModal] = useState(false);

  return (
    <div className={styles.container}>
      <Helmet>
        <title>{t("general.orbit")}| {t("account.login")}</title>
      </Helmet>
      <RewardModal show={showRewardModal} setShow={setShowRewardModal} />
      <div className={styles.main}>
        <div
          className={
            styles.links + " flex justify-content-around items-baseline w-full"
          }
          style={{ marginTop: isSmartBannerOpen ? '30px' : '0px' }}
        >
          <Link
            to="/login"
            className={styles.active}
            style={{ textDecoration: "none" }}
          >
            {t("account.login")}
          </Link>
          <Link to="/register" style={{ textDecoration: "none" }}>
            {t("account.register")}
          </Link>
        </div>
        <h2 className="title">{t("account.login")}</h2>
        {fields.map((prop: any) => (
          <Input
            value={loginInfo[prop]}
            sublink={
              prop === "password" && (
                <Link
                  to="/forgot-password"
                  style={{ color: "#0F133A", fontWeight: 700, fontSize: 14 }}
                >
                  {t("account.forgetPass")}
                </Link>
              )
            }
            label={t("placeholders." + prop)}
            disabled={loading}
            onChange={(e: any) => {
              handleInputChange(prop, e, loginInfo, setLoginInfo);
            }}
            error={loginInfo.errors[prop]}
            clear={() => {
              clearInput(prop, setLoginInfo);
            }}
            onKeyDown={login}
            key={`input-${prop}`}
            password={prop === "password"}
          />
        ))}
        <Button
          loading={loading}
          onClick={login}
          style={{ marginTop: 9, width: "100%" }}
        >
          {t("account.login")}
        </Button>
        <SocialLogin
          handleFacebookNext={facebookLogin}
          handleGoogleNext={googleLogin}
          handleAppleNext={appleLogin}
        />
      </div>
    </div>
  );
};

export default Login;
