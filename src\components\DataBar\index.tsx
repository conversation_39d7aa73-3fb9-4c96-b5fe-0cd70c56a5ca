/*
==========================================================================================

                                    DataBar

Description: Data display with circular progress bar for dashboard plans

Parameters: amount (str) - amount of data left
            percentage (num) - percentage for progress bar to display

Pages: /dashboard

==========================================================================================
*/

import { useEffect, useState } from "react";
import styles from "./data-bar.module.scss";
import { t } from "i18next";

const DataBar = ({ amount, percentage }: any) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (percentage) {
      setTimeout(() => {
        setProgress(percentage);
      }, 50);
    }
  }, [percentage]);

  return (
    <div className={styles.container}>
      <div className={styles.amountContainer}>
        <p className={styles.amount}>{amount}</p>
        <p className={styles.available}>{ t('plan.available') }</p>
      </div>
      <div className={styles.progressBar} id="data-ring">
        <DataRing percentage={progress} />
      </div>
    </div>
  );
};

export default DataBar;

// SVG for the circular progress bar
const DataRing = ({ percentage }: any) => {
  return (
    <svg
      width="100%"
      height="100%"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="00D6F55C95FF" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#04DCAD"></stop>
          <stop offset="100%" stopColor="#088BDD"></stop>
        </linearGradient>
      </defs>
      <circle
        className={styles.progress__meter}
        strokeWidth="14"
        fill="transparent"
        r="50"
        cx="60"
        cy="60"
      />
      <circle
        className={styles.progress__value}
        strokeWidth="14"
        fill="transparent"
        r="50"
        cx="60"
        cy="60"
        stroke="url(#00D6F55C95FF)"
        strokeLinecap="round"
        strokeDasharray="314.16 314.16"
        strokeDashoffset={(314.16 * (1 - percentage)).toString()}
      />
    </svg>
  );
};
