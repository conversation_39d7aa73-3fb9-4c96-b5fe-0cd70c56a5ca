import { ChevronDown } from "../svgs";
import styles from "./select.module.scss";
import Select from "react-select";

const SelectDropdown = ({
  value,
  error,
  onChange,
  options,
  disabled,
  placeholder,
  label,
  dropDownMaxHeight,
}: any) => {
  const root = getComputedStyle(document.getElementById("root")!);
  const customStyles = {
    control: (baseStyles: any, state: any) => ({
      ...baseStyles,
      height: 52,
      marginBottom: 0,
      borderRadius: 1000,
      border: "none",
      boxShadow: "none",
      color: "#000",
      background: "#EFF1F7",
      opacity: disabled ? 0.5 : 1,
    }),
    placeholder: (baseStyles: any, state: any) => ({
      ...baseStyles,
      color: "#160b2a",
    }),
    valueContainer: (baseStyles: any, state: any) => ({
      ...baseStyles,
      padding: "0px 14px",
      color: "#160b2a",
      fontSize: 14,
    }),
    menu: (baseStyles: any, state: any) => ({
      ...baseStyles,
      border: "none",
      marginTop: 0,
      zIndex: 3000,
    }),
    menuList: (baseStyles: any, state: any) => ({
      ...baseStyles,
      padding: 0,
      maxHeight: dropDownMaxHeight,
      "::-webkit-scrollbar": {
        width: "10px",
        height: "0px",
        borderRadius: "9999px",
      },
      "::-webkit-scrollbar-track": {
        background: "#eff1f7",
        borderRadius: "9999px",
      },
      "::-webkit-scrollbar-thumb": {
        background: "#7448b0",
        borderRadius: "9999px",
      },
      "::-webkit-scrollbar-thumb:hover": {
        background: "#7448b0",
      },
    }),
    option: (baseStyles: any, state: any) => ({
      ...baseStyles,
      height: 56,
      padding: "0 16px",
      lineHeight: "56px",
      color: state.isSelected ? "#fff" : "#000",
      background: state.isSelected ? "#7448b0" : "#fff",
      display: "flex",
      alignItems: "center",
      "&:hover": {
        background: state.isSelected ? "#7448b0" : "#cebee3",
      },
    }),
    indicatorSeparator: (baseStyles: any, state: any) => ({
      ...baseStyles,
      display: "none",
    }),
    indicatorsContainer: (baseStyles: any, state: any) => ({
      ...baseStyles,
      padding: "8px 16px 8px 8px",
    }),
  };

  return (
    <div className={styles.selectContainer}>
      <div className={styles.label}>{label}</div>
      <div style={{ position: "relative" }}>
        <Select
          styles={customStyles}
          value={value}
          onChange={onChange}
          options={options}
          placeholder={placeholder}
          isDisabled={disabled}
          components={{ DropdownIndicator: ChevronDown }}
        />
        {error && (
          <img
            src="/input_error.svg"
            className={styles.errorIcon}
            onMouseDown={(e) => {
              e.preventDefault();
            }}
            style={{ right: 45, top: 15 }}
          />
        )}
      </div>
      {error && <p className={styles.errorText}>{error || <br />}</p>}
    </div>
  );
};

export default SelectDropdown;
