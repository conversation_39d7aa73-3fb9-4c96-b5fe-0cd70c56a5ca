@use "../styles/theme.scss" as *;

.mainContainer {
  display: flex;
  padding: 16px 65px 0 65px;
  justify-content: center;
  align-items: center;
  width: 100%;
  flex-grow: 1;
  height: 290px;
  @media (max-width: 768px) {
    padding: 0;
    height: auto;
  }
}

.amountsContainer {
  display: flex;
  align-items: center;
  @media (max-width: 768px) {
    display: none;
  }
}

.creditAmountContainer {
  width: 75px;
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  transition: all 0.5s ease;
  margin-right: 46px;
  @media (max-width: 768px) {
    overflow: visible;
    &:last-child {
      margin: 0;
    }
  }
  &:last-child {
    margin-right: 0;
  }
  .creditAmount {
    font-weight: 700;
    font-size: 18px;
    line-height: 27px;
    color: $dark-dark-purple;
    position: relative;
    z-index: 20;
    background: #eee7f8;
    border-radius: 100px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.5s ease;
  }
  .blurBackground {
    position: absolute;
    width: 150px;
    height: 150px;
    background: $secondary;
    filter: blur(85.9914px);
    z-index: 10;
    @media (max-width: 900px) {
      display: none;
    }
  }
  &.active {
    .creditAmount {
      color: #fff;
      background: $secondary;
      font-size: 31px;
      line-height: 47px;
      box-shadow: 0px 0px 50px 30px rgba(116, 72, 176, 0.3);
      @media (max-width: 900px) {
        box-shadow: none;
      }
    }
    cursor: auto;
    width: 130px;
    height: 130px;
  }
}

.mobilePlansContainer {
  display: none;
  width: 100%;
  @media (max-width: 768px) {
    display: block;
  }
}

.mobilePlanTypeInfo {
  display: none;
  color: #fff;
  padding: 0 24px;
  margin-bottom: 5px;
  margin-top: 20px;
  width: 100%;
  h5 {
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    margin: 0 0 4px 0;
  }
  p {
    font-size: 16px;
    line-height: 24px;
    margin: 0;
  }
  .checkRates {
    margin-left: 8px;
    text-decoration: underline;
    cursor: pointer;
  }
  @media (max-width: 768px) {
    display: block;
  }
}

.bottomContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  color: #fff;
  padding: 50px;
  @media (max-width: 768px) {
    padding: 24px 24px 50px 24px;
    button {
      width: 100%;
    }
  }
  button {
    z-index: 2000;
  }
  .checkRates {
    padding: 0;
    background: none;
    border: none;
    margin-top: 6px;
    text-decoration: underline;
    color: #fff;
    font-size: 16px;
    line-height: 24px;
    cursor: pointer;
  }
  .planTypeInfo {
    @media (max-width: 768px) {
      display: none;
    }
  }
  h5 {
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    margin: 0 0 4px 0;
  }
  p {
    font-size: 16px;
    line-height: 24px;
    margin: 0;
  }
}
