import { useEffect, useState } from "react";
import styles from "../../styles/help.module.scss";
import Faqs from "../../components/Faqs/Faqs";
import Toggle from "../../components/Toggle";
import { Helmet } from "react-helmet-async";
import HelpHeader from "../../components/helpHeader/HelpHeader";
import { useTranslation } from "react-i18next";
import { CmsApiGet, cmsURL } from "../api/cms-api";
import { organizationSchema } from "../../components/utils/schemaMarkups";

type view = "Joining" | "Activating" | "تسجيل" | "تفعيل";

const Help = () => {
  const [faqs, setFaqs] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState("Getting Started");
  const [view, setView] = useState<view>("Joining");

  const [topQuestionsTypes, setTopQuestionTypes] = useState([] as any);

  const handleView = (view: any) => {
    setView(view);
  };

  const { t, i18n } = useTranslation();
  const [categories, setCategories] = useState([]);

  const data = {
    metaDescription: t("blog.description")
  }

  useEffect(() => {
    CmsApiGet(
      `/api/faqs-categories?locale=${i18n.language}&populate=deep`
    ).then((response: any) => {
      setCategories(response.data.data);
    });

    CmsApiGet(`/api/top-faqs-types?locale=${i18n.language}&populate=deep`).then(
      (response: any) => {
        setView(response.data.data[0]?.attributes?.label);
        setTopQuestionTypes(response.data.data);
      }
    );
  }, [i18n, i18n.language]);

  useEffect(() => {
    if (i18n.language === "ar") {
      setSelectedCategory("البدء");
      if (view === "Joining") {
        setView("تسجيل");
      } else {
        setView("تفعيل");
      }
    } else {
      setSelectedCategory("Getting Started");
      if (view === "تسجيل") {
        setView("Joining");
      } else {
        setView("Activating");
      }
    }
  }, [i18n.language]);

  return (
    <>
      <Helmet>
        <title>
          {t("general.orbit")}| {t("help.needHelp")}
        </title>
        <script type="application/ld+json">
          {JSON.stringify(organizationSchema(data))}
        </script>
        <meta name="description" content={t("blog.description")} />
      </Helmet>
      <HelpHeader questions={faqs} />
      <div className="max-width-container">
        <div className={styles.mainContainer}>
          <div className={styles.helpCategories}>
            <div className="flex justify-content-between">
              {categories.map((category: any, i: number) => {
                return (
                  <div
                    key={category?.attributes?.name + i}
                    className={
                      styles.helpCategory +
                      " " +
                      (selectedCategory == category?.attributes?.name
                        ? styles.active
                        : "")
                    }
                    onClick={() =>
                      setSelectedCategory(category.attributes.name)
                    }
                  >
                    <img
                      src={
                        cmsURL +
                        category?.attributes?.icon?.data?.attributes?.url
                      }
                      alt={`${category} - Orbit Mobile eSIM AP`}
                    />
                    <p>{category?.attributes?.name}</p>
                  </div>
                );
              })}
            </div>
          </div>
          <div className={styles.container}>
            <Faqs
              getQuestions={(questions: []) => setFaqs(questions)}
              filter={selectedCategory}
              filterType="category"
            />
          </div>
        </div>
        <div className={styles.topQuestions}>
          <h2>{t("help.topQuestions")}</h2>
          <Toggle
            choices={topQuestionsTypes.map(
              (item: any) => item.attributes?.label
            )}
            defaultChoice={view}
            handleSelected={(choice: string) => handleView(choice)}
          />
          <div>
            <Faqs
              getQuestions={(questions: []) => setFaqs(questions)}
              filter={view}
              filterType="topQuestionsType"
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default Help;

const SelectionTriangle = () => (
  <svg
    width="42"
    height="16"
    viewBox="0 0 42 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.497498 15.5675C1.9676 15.5675 3.38659 15.0278 4.48509 14.0507L17.4525 2.51679C19.7517 0.471825 23.2251 0.497563 25.4937 2.57637L37.95 13.9911C39.057 15.0051 40.503 15.5675 42.004 15.5675"
      fill="#FAFAF9"
    />
    <path
      d="M0.497498 15.5675C1.9676 15.5675 3.38659 15.0278 4.48509 14.0507L17.4525 2.51679C19.7517 0.471825 23.2251 0.497563 25.4937 2.57637L37.95 13.9911C39.057 15.0051 40.503 15.5675 42.004 15.5675"
      stroke="#0F133A"
      strokeWidth="1"
      strokeMiterlimit="10"
    />
  </svg>
);
