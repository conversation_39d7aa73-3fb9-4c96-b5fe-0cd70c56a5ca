@use "../../styles/theme.scss" as *;

.searchContainer {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: center;
  @media (max-width: 768px) {
    flex-direction: column;
  }
  @media (max-width: 425px) {
    width: 100%
  }
  .inputContainer {
    display: flex;
    align-items: center;
    height: 50px;
    width: 100%;
    margin-right: 16px;
    border-radius: 1000px;
    padding: 0 16px;
    border: 1px solid #838CA0;
    transition: border-color 0.15s ease;
    &.focus {
      border-color: $secondary;
    }
    &.filled {
      border-color: #d6d6d6;
    }
    @media (max-width: 768px) {
      margin-right: 0;
      margin-bottom: 16px;
    }
    input {
      border: none;
      background: none;
      width: 100%;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #000;
      height: 100%;
      caret-color: $secondary;
      &:focus {
        outline: none;
      }
      &::placeholder {
        color: $placeholder;
      }
    }
    .magGlass svg {
      color: #000;
      margin-right: 7px;
      vertical-align: middle;
    }
    .clearButton {
      background: none;
      border: none;
      padding: 0;
      color: $secondary;
      cursor: pointer;
      svg {
        vertical-align: middle;
      }
    }
  }
}
