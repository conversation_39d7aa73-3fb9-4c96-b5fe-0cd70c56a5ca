import Shimmer from "../Shimmer";
import { ToggleGlobal } from "../ToggleGlobal/ToggleGlobal";
import styles from "./card-tile-skeleton.module.scss";

const CardTileSkeleton = () => {
  return (
    <div className={styles.main}>
      <div className={styles.button}>
        <Shimmer />
      </div>
      <div className={styles.cardInfo}>
        <div className={styles.network}>
          <Shimmer />
        </div>
        <div className={styles.number}>
          <Shimmer />
        </div>
        <div className={styles.expiry}>
          <Shimmer />
        </div>
      </div>
      <div className={styles.bottomSection}>
        <div className={styles.primary}>
          <Shimmer />
        </div>
        <div className={styles.toggle}>
          <Shimmer />
        </div>
      </div>
    </div>
  );
};

export default CardTileSkeleton;
