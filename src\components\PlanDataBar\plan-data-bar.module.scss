@use "../../styles/theme.scss" as *;

.container {
  display: flex;
  width: 100%;
  align-items: center;
  margin-bottom: 12px;
  &.noMargin {
    margin-bottom: 0px;
  }
  svg {
    width: 20px;
    height: 20px;
    margin-right: 12px;
  }
}

.barContainer {
  position: relative;
  height: 32px;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #aab5d5;
  border-radius: 100px;
  overflow: hidden;
}

.bar {
  position: absolute;
  left: 0px;
  height: 100%;
  background: $secondary;
  border-radius: 100px;
  z-index: 800;
  transition: width 0.8s ease;
}

.text {
  color: #fff;
  font-weight: 700;
  font-size: 14px;
  line-height: 21px;
  position: relative;
  z-index: 1000;
}
