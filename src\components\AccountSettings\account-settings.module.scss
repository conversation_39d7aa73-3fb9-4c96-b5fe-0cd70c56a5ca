@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  max-width: 593px;
  margin: auto;
  @media (max-width: 768px) {
    padding-top: 36px;
  }
}
.top {
  h3 {
    color: $dark-dark-purple;
    font-weight: 800;
    font-size: 26px;
    line-height: 30px;
    text-align: center;
    margin: 0;
    margin-left: 6px;
    margin-bottom: 24px;
  }
}

.skeleton {
  background-color: $skeleton;
  overflow: hidden;
  position: relative;
  height: 24px;
  width: 140px;
  border-radius: 5px;
}

.container {
  padding: 27px 32px 40px 32px;
  background: #fff;
  border-radius: 32px;
  margin: 0 -26px;
  width: calc(100% + 52px);
  margin-bottom: 50px;
  @media (max-width: 850px) {
    width: 100%;
    margin: 0 0 25px 0;
  }
  button {
    span {
      font-size: 14px;
      font-weight: 700;
      svg {
        color: #691211;
      }
    }
  }
  h4 {
    margin: 0;
    color: $dark-dark-purple;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 24px;
  }
  .notificationsGrid {
    > div {
      margin-top: 20px;
    }
    h5 {
      margin: 0;
      font-weight: 400;
      font-size: 12px;
      color: #667085;
      line-height: 24px;
      margin-bottom: 20px;
    }
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 16px;
      font-size: 14px;
      .settingIcon {
        padding-top: 5px;
        svg {
          color: #0f133a;
        }
      }
      .itemDetails {
        margin-inline-start: 12px;
        word-break: break-all;
        h5 {
          margin-bottom: 0;
        }
        p {
          font-size: 14px;
          margin-top: 5px;
        }
      }
      .editAction {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        svg {
          cursor: pointer;
        }
      }
      .name {
        margin-right: 3px;
        font-size: 14px;
      }
    }
  }
}
