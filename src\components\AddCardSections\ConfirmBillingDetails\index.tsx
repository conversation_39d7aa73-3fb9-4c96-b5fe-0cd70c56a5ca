import { motion } from "framer-motion";
import { useState } from "react";
import Button from "../../Button";
import SavedCard from "../../SavedCard";
import styles from "./confirm-billing-details.module.scss";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import Radio from "../../Radio";
import { t } from "i18next";

const ConfirmBillingDetails = ({
  cardDetails,
  cancel,
  saveCard,
  addAddress,
  loading,
  savedBillingDetails,
  setSavedBillingDetails,
  cards,
}: any) => {
  let numberWithoutSpaces = cardDetails.number.replaceAll(" ", "");

  // Get network from card number
  let network = numberWithoutSpaces.match(/^4[0-9]{6,}$/)
    ? "visa"
    : numberWithoutSpaces.match(
        /^5[1-5][0-9]{5,}|222[1-9][0-9]{3,}|22[3-9][0-9]{4,}|2[3-6][0-9]{5,}|27[01][0-9]{4,}|2720[0-9]{3,}$/
      )
    ? "mastercard"
    : numberWithoutSpaces.match(/^3[47][0-9]{5,}$/)
    ? "amex"
    : numberWithoutSpaces.match(/^(?:2131|1800|35[0-9]{3})[0-9]{3,}$/)
    ? "jcb"
    : "";

  // Format card for SavedCard component
  const card = {
    primary: 0,
    network: network,
    cardholderName: cardDetails.name,
    last4Digits: numberWithoutSpaces.slice(
      numberWithoutSpaces.length - 4,
      numberWithoutSpaces.length
    ),
    expiry: cardDetails.expiry,
  };

  // State for select billing address view
  const [selectBilling, setSelectBilling] = useState(false);

  return (
    <div>
      <SwitchTransition>
        <CSSTransition
          key={selectBilling ? "select" : "view"}
          addEndListener={(node, done) =>
            node.addEventListener("transitionend", done, false)
          }
          classNames="fade"
        >
          <div className={styles.main}>
            {selectBilling ? (
              <>
                <h3 style={{ marginBottom: 38 }}>Billing details</h3>
                {cards.map((billing: any) => (
                  <div className={styles.billingAddress}>
                    <Radio
                      checked={savedBillingDetails.id === billing.id}
                      onClick={() => {
                        setSavedBillingDetails(billing);
                      }}
                    />
                    <div style={{ marginLeft: 16 }}>
                      {billing.cardholderName}, {billing.addressOne},{" "}
                      {billing.city}, {billing.postcode}, {billing.country}
                    </div>
                  </div>
                ))}
                <motion.div
                  transition={{ durarion: 300, easing: "ease-out" }}
                  layoutId="add-card-buttons"
                  className={styles.buttons}
                  style={{ marginTop: "auto" }}
                >
                  <Button
                    style={{ marginRight: 16 }}
                    color="secondary"
                    onClick={addAddress}
                  >
                    Add an address
                  </Button>
                  <Button
                    onClick={() => {
                      setSelectBilling(false);
                    }}
                  >
                    Use this address
                  </Button>
                </motion.div>
              </>
            ) : (
              <>
                <h3 className={styles.addHeading}>Add a new card</h3>
                <div className={styles.data}>
                  <SavedCard card={card} displayOnly />
                  <div className={styles.billing}>
                    <h5>Billing details</h5>
                    <p>
                      {(() => {
                        const selected = savedBillingDetails;
                        return (
                          <>
                            {cardDetails.cardholderName}
                            <br />
                            {selected.addressOne}
                            <br />
                            {selected.city}
                            <br />
                            {selected.postcode}
                            <br />
                            {selected.country} <br />
                            {selected.phoneCountryCode} {selected.phoneNumber}
                          </>
                        );
                      })()}
                    </p>
                    <Button
                      onClick={() => {
                        setSelectBilling(true);
                      }}
                      style={{ padding: 0 }}
                      color="tertiary"
                    >
                      Change
                    </Button>
                  </div>
                </div>
                <motion.div
                  transition={{ durarion: 300, easing: "ease-out" }}
                  layoutId="add-card-buttons"
                  className={styles.buttons}
                >
                  <Button
                    style={{ marginRight: 16 }}
                    color="secondary"
                    onClick={cancel}
                    disabled={loading}
                  >
                    { t('buttons.cancel') }
                  </Button>
                  <Button onClick={saveCard} loading={loading}>
                  { t('buttons.save') }
                  </Button>
                </motion.div>
              </>
            )}
          </div>
        </CSSTransition>
      </SwitchTransition>
    </div>
  );
};

export default ConfirmBillingDetails;
