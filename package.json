{"name": "orbit-web", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@aws-sdk/client-s3": "^3.306.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/material": "^5.11.9", "@paypal/react-paypal-js": "^8.1.3", "@szhsin/react-menu": "^4.1.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.11", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^4.0.4", "axios": "^1.3.3", "body-scroll-lock": "^4.0.0-beta.0", "crypto-js": "^4.2.0", "custom-react-smartbanner": "^0.0.16", "firebase": "^9.22.1", "framer-motion": "^9.0.3", "http-proxy-middleware": "^2.0.6", "i18next": "^24.0.5", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^3.0.1", "indicative": "^7.4.4", "jquery": "^3.6.3", "jwt-decode": "^3.1.2", "libphonenumber-js": "^1.10.31", "lunr": "^2.3.9", "moment": "^2.30.1", "node-fetch": "^3.3.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-i18next": "^15.1.3", "react-redux": "^8.0.5", "react-router-dom": "^6.8.1", "react-router-hash-link": "^2.4.3", "react-select": "^5.8.0", "react-slick": "^0.30.2", "react-transition-group": "^4.4.5", "redux": "^4.2.1", "sass": "^1.58.1", "sitemap": "^8.0.0", "slick-carousel": "^1.8.1", "swiper": "^9.0.5", "typescript": "^5.0.0", "uuid": "^9.0.0", "vite": "4.4.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-svgr": "3.2.0", "vite-tsconfig-paths": "4.2.0", "web-vitals": "^2.1.4"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "serve": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/body-scroll-lock": "^3.1.0", "@types/crypto-js": "^4.2.2", "@types/jquery": "^3.5.16", "@types/lunr": "^2.3.4", "@types/react-router-hash-link": "^2.4.9", "@types/react-slick": "^0.23.13", "@types/uuid": "^9.0.0", "nodemon": "^2.0.15", "npm-run-all": "^4.1.5"}}