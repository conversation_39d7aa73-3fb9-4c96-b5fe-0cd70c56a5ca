import { Link } from "react-router-dom";
import styles from "./search-result.module.scss";
import formatDate from "../utils/formatDate";
import formatPlanData from "../utils/formatPlanData";

export const PageSearchResult = ({ data }: any) => {
  return (
    <div className={styles.main}>
      <Link style={{ textDecoration: "none" }} to={data.link}>
        <h5>{data.title}</h5>
        <p className={styles.summary}>{data.summary}</p>
      </Link>
    </div>
  );
};

export const BlogSearchResult = ({ data }: any) => {
  return (
    <div className={styles.main}>
      <Link style={{ textDecoration: "none" }} to={`/blog/${data.slug}`}>
        <h5>{data.title}</h5>
      </Link>
      <Link style={{ textDecoration: "none" }} to={`/blog/${data.slug}`}>
        <div
          className={styles.summary}
          dangerouslySetInnerHTML={{ __html: data.excerpt }}
        />
      </Link>
      <div className={styles.bottom}>
        <div className={styles.breadcrumbs}>
          <Link to="/blog">Blog</Link>
          <span className={styles.divider}>{">"}</span>
          <Link to={`/blog/${data.slug}`}>{data.title}</Link>
        </div>
        <div className={styles.date}>{formatDate(data.date)}</div>
      </div>
    </div>
  );
};

export const CountrySearchResult = ({ data }: any) => {
  return (
    <div className={styles.main}>
      <h5>
        {data.planName} {data.displayType} Bundles
      </h5>
      <p className={styles.summary}>{formatPlanData(data)}</p>
      <div className={styles.bottom}>
        <div className={styles.breadcrumbs}>
          <Link to="/shop">Shop</Link>
          <span className={styles.divider}>{">"}</span>
          <Link to={`/shop/select-plan/${data.countries[0].countryCode}`}>
            {data.countries[0].countryName}
          </Link>
          <span className={styles.divider}>{">"}</span>
          <Link
            to={`/shop/select-plan/${data.countries[0].countryCode}/${data.shopLink}`}
          >
            {data.displayType} Bundles
          </Link>
        </div>
      </div>
    </div>
  );
};

export const FaqSearchResult = ({ data }: any) => {
  return (
    <div className={styles.main}>
      <h5>{data.question}</h5>
      {data.strings.map((item: any) => (
        <div
          key={item}
          className={styles.summary}
          dangerouslySetInnerHTML={{ __html: item }}
        />
      ))}
      <div className={styles.bottom}>
        <div className={styles.breadcrumbs}>
          <Link to="/help">Help</Link>
          <span className={styles.divider}>{">"}</span>
          <Link to="/help">FAQ</Link>
          <span className={styles.divider}>{">"}</span>
          <Link to="/help">{data.question}</Link>
        </div>
      </div>
    </div>
  );
};
