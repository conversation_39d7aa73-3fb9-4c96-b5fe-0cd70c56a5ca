import { useSelector } from "react-redux";
import Button from "../Button";
import Modal from "../Modal";
import { FadedAlert } from "../svgs";
import styles from "./low-balance-modal.module.scss";
import { getCurrencySymbol } from "../utils/getCurrencySymbol";
import { formatPrice } from "../utils/formatPrice";
import { Link } from "react-router-dom";
import { t } from "i18next";

const LowBalanceModal = ({ show, setShow }: any) => {
  const { userInfo } = useSelector((state: any) => state);

  return (
    <Modal
      show={show}
      setShow={setShow}
      style={{ height: "auto", maxWidth: 461 }}
    >
      <div className={styles.main}>
        <div className={styles.icon}>
          <FadedAlert />
        </div>
        <h5>Insufficient balance!</h5>
        <p>
          Looks like your wallet needs a little boost to purchase this plan.
          Please add funds to proceed.
        </p>
        <div className={styles.balanceContainer}>
          Available balance: {getCurrencySymbol(userInfo.currency)}
          {userInfo.credit ? formatPrice(parseFloat(userInfo.credit)) : "0.00"}
        </div>
        <div className={styles.buttons}>
          <Button
            color="secondary"
            style={{ marginRight: 16, width: "100%" }}
            onClick={() => {
              setShow(false);
            }}
          >
            { t('buttons.cancel') }
          </Button>
          <Link
            to="/shop/add-credit"
            style={{ textDecoration: "none", width: "100%" }}
          >
            <Button style={{ width: "100%" }}>Add Funds</Button>
          </Link>
        </div>
      </div>
    </Modal>
  );
};

export default LowBalanceModal;
