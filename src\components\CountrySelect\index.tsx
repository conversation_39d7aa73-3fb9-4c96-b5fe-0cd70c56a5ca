import styles from "./country-select.module.scss";
import { ChevronDown } from "../svgs";
import { useEffect, useRef, useState } from "react";
import Modal from "../Modal";
import SearchBar from "../SearchBar";
import { t } from "i18next";

const CountrySelect = ({
  label,
  options,
  disabled,
  onSelect,
  selected,
}: any) => {
  const ref = useRef(null);

  const [show, setShow] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [queryDisplay, setQueryDisplay] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const handleSearchChange = (e: any) => {
    setSearchQuery(e.target.value);
  };

  const clearSearch = () => {
    setShowSearchResults(false);
    setSearchQuery("");
  };

  const checkIfFirstWord = (item: any, query: any) => {
    const name = item.countryName;
    return query.some((queryStr: any) => {
      const combinedRegex = new RegExp("^" + queryStr.toLowerCase(), "gi");
      return combinedRegex.test(name.toLowerCase());
    });
  };

  const checkIfAnyWord = (item: any, query: any) => {
    const name = item.countryName;
    return query.some((queryStr: any) => {
      const combinedRegex = new RegExp("\\b" + queryStr.toLowerCase(), "gi");
      return combinedRegex.test(name.toLowerCase());
    });
  };

  useEffect(() => {
    if (searchQuery === "") {
      handleSearch();
    }
  }, [searchQuery]);

  const handleSearch = () => {
    if (searchQuery === "") {
      setShowSearchResults(false);
      return;
    }

    setQueryDisplay(searchQuery);
    let query = searchQuery.split(" ");

    // Filter users to only show users that contain query string
    let toShow = options.filter((option: any) => {
      const name = option.key.countryName;
      return query.some((singleQuery: string) =>
        name.toLowerCase().includes(singleQuery.toLowerCase())
      );
    });

    const firstWord = toShow.some((item: any) => {
      return checkIfFirstWord(item.key, query);
    });

    const anyWord = toShow.some((item: any) => {
      return checkIfAnyWord(item.key, query);
    });

    if (firstWord) {
      toShow = toShow.filter((item: any) => {
        return checkIfFirstWord(item.key, query);
      });
    } else if (anyWord) {
      toShow = toShow.filter((item: any) => {
        return checkIfAnyWord(item.key, query);
      });
    }

    console.log(toShow);

    setSearchResults(toShow);
    setShowSearchResults(true);
  };

  return (
    <div className={`${styles.box} country-select`}>
      <div className={styles.label}>{label}</div>
      <div
        ref={ref}
        className={`${styles.menuButton} ${disabled && styles.disabled}`}
        onClick={(e) => {
          setShow(true);
        }}
      >
        {(() => {
          if (selected && options.length) {
            return options.filter(
              (item: any) => item.key.countryCode === selected
            )[0]?.displayLabel;
          } else {
            return "Select a " + label;
          }
        })()}
        <div className={styles.expand}>
          <ChevronDown />
        </div>
      </div>
      <Modal
        show={show}
        style={{ maxWidth: 1188, minHeight: "90vh" }}
        clickOff={() => {
          setShow(false);
        }}
        noTransition
      >
        <div className={styles.modalMain}>
          <div className={styles.searchContainer}>
            <SearchBar
              handleSearch={handleSearch}
              id="country-search-input"
              searchQuery={searchQuery}
              placeholder={t('placeholders.searchCountry')}
              handleSearchChange={handleSearchChange}
              clearSearch={clearSearch}
            />
          </div>
          <div className={styles.itemsGrid}>
            {showSearchResults
              ? searchResults.map((item: any) => (
                  <div
                    onClick={() => {
                      onSelect(item);
                      setShow(false);
                    }}
                    className={styles.menuItem}
                    key={item.key}
                  >
                    {item.displayLabel}
                  </div>
                ))
              : options.map((item: any) => (
                  <div
                    onClick={() => {
                      onSelect(item);
                      setShow(false);
                    }}
                    className={styles.menuItem}
                    key={item.key}
                  >
                    {item.displayLabel}
                  </div>
                ))}
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CountrySelect;
