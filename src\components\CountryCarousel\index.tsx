import styles from "./country-carousel.module.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import { CaretLeft, CaretRight } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import Country from "../Country";
import SeeAllCountry from "../SeeAllCountry";
import { useMediaQuery } from "@mui/material";

const CountryCarousel = ({ title, countries }: any) => {
  // store controlled swiper instance
  const [controlledSwiper, setControlledSwiper] = useState(null as any);

  const [swiperIsEnd, setSwiperIsEnd] = useState(false);
  const [swiperIsBeginning, setSwiperIsBeginning] = useState(true);

  const dispatch = useDispatch();

  const under768 = useMediaQuery("(max-width: 768px)");

  return (
    <div className={styles.container}>
      <div className={styles.heading}>
        <h3>{title}</h3>
        <div className={styles.swiperButtons}>
          <button
            style={{
              color: swiperIsBeginning ? "#C1C1C1" : "#160B2A",
              cursor: swiperIsBeginning ? "auto" : "pointer",
              marginRight: 7,
            }}
            onClick={() => {
              if (controlledSwiper) {
                controlledSwiper.slidePrev();
              }
            }}
          >
            <CaretLeft />
          </button>
          <button
            style={{
              color: swiperIsEnd ? "#C1C1C1" : "#160B2A",
              cursor: swiperIsEnd ? "auto" : "pointer",
            }}
            onClick={() => {
              if (controlledSwiper) {
                controlledSwiper.slideNext();
              }
            }}
          >
            <CaretRight />
          </button>
        </div>
      </div>
      <Swiper
        spaceBetween={under768 ? 12 : 24}
        slidesPerView={1.12}
        breakpoints={{
          768: {
            slidesPerView: 2.5,
          },
          900: {
            slidesPerView: 3.5,
          },
          1500: {
            slidesPerView: 5.5,
          },
          1700: {
            slidesPerView: 7.5,
          },
          2500: { slidesPerView: 10.5 },
        }}
        slidesOffsetBefore={under768 ? 24 : 50}
        slidesOffsetAfter={under768 ? 24 : 50}
        onSwiper={(swiper: any) => setControlledSwiper(swiper)}
        onSlideChange={(swiper: any) => {
          setSwiperIsBeginning(swiper.isBeginning);
          setSwiperIsEnd(swiper.isEnd);
        }}
        speed={700}
        style={{ overflow: "visible" }}
      >
        {countries.map((country: any) => (
          <SwiperSlide
            style={{ overflow: "visible" }}
            key={`country-tile-${country.countryName}`}
          >
            <Country
              image={country.countryImage}
              flag={country.iconUrl}
              name={country.countryName}
              countryCode={country.countryCode}
            />
          </SwiperSlide>
        ))}
        <SwiperSlide style={{ overflow: "visible" }}>
          <SeeAllCountry link={`/shop/zone/${title.toLowerCase()}`} />
        </SwiperSlide>
      </Swiper>
    </div>
  );
};

export default CountryCarousel;
