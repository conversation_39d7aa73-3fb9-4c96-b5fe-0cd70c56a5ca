import styles from "./notification.module.scss";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { motion } from "framer-motion";

const Notification = ({ id, error, heading, message }: any) => {
  const dispatch = useDispatch();

  let timeout: any;

  const onClose = () => {
    if (timeout) clearTimeout(timeout);
    dispatch({
      type: "closeNotification",
      payload: id,
    });
  };

  useEffect(() => {
    timeout = setTimeout(() => {
      onClose();
    }, 5000);
  }, []);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: -150 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -150 }}
      transition={{
        type: "tween",
        ease: "easeInOut",
        duration: 0.3,
      }}
      className={styles.main}
      key={id}
    >
      <div className={styles.textContainer}>
        <div className={styles.top}>
          {error ? (
            <img src="/images-int/info-circle.svg" className={styles.image} />
          ) : (
            <img src="/images-int/tick-circle.svg" className={styles.image} />
          )}
          <h5 className={styles.heading}>{heading}</h5>
        </div>
        <p className={styles.message}>{message}</p>
      </div>
      <button className={styles.close} onClick={onClose}>
        <svg
          width="40"
          height="40"
          viewBox="0 0 40 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M26.6673 13.3335L13.334 26.6668"
            stroke="#160B2A"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M26.6673 26.6668L13.334 13.3335"
            stroke="#160B2A"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>
    </motion.div>
  );
};

export default Notification;
