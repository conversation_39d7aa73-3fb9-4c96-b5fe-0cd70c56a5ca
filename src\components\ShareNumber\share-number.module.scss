@use "../../styles/theme.scss" as *;

.container {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 0 24px;
  background: rgba(22, 11, 42, 0.6);
  @media (max-width: 768px) {
    align-items: flex-start;
    padding: 50px 24px;
  }
}

.closeButton {
  svg {
    vertical-align: middle;
  }
  padding: 0;
  background: none;
  border: none;
  cursor: pointer;
  position: absolute;
  top: 50px;
  right: 50px;
  @media (max-width: 768px) {
    top: 24px;
    right: 24px;
  }
}

.modal {
  width: 100%;
  max-width: 700px;
  height: 340px;
  max-height: 90vh;
  border-radius: 24px;
  background: #fff;
  display: flex;
  position: relative;
  @media (max-width: 768px) {
    height: auto;
  }
}

.main {
  width: 100%;
  padding: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: auto;
  position: relative;
  @media (max-width: 768px) {
    padding: 70px 24px 50px 24px;
  }
  h4 {
    color: $primary;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    margin: 0 0 50px 0;
  }
  .buttonContainer {
    display: flex;
    align-items: center;
    @media (max-width: 485px) {
      display: grid;
      grid-template-columns: auto auto auto;
      grid-template-rows: auto auto auto;
      grid-row-gap: 24px;
      grid-column-gap: 50px;
    }
    a {
      margin-right: 24px;
      @media (max-width: 485px) {
        margin-right: 0px;
      }
      img {
        width: 41px;
        &.xLogo {
          width: 35px;
        }
      }
      &:last-child {
        margin-right: 0px;
      }
    }
  }
  .numberContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 256px;
    background: #eff1f7;
    border-radius: 12px;
    margin-top: 50px;
    padding: 8px 24px;
    .number {
      color: $dark-dark-purple;
      font-weight: 600;
      font-size: 14px;
      line-height: 21px;
    }
    .copy {
      background: none;
      border: none;
      cursor: pointer;
      margin-right: 10px;
      width: 40px;
      height: 40px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      &:last-child {
        margin-right: 0px;
      }
      &:hover {
        background-color: #ccd2e6;
      }
      svg {
        width: 20px;
        height: 20px;
        vertical-align: middle;
      }
    }
  }
}
