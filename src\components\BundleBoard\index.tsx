import { useNavigate } from "react-router-dom";
import { HashLink as Link } from "react-router-hash-link";
import Button from "../Button";
import DashboardPlanTile from "../DashboardPlanTile";
import { Helmet } from 'react-helmet-async';
import { <PERSON><PERSON><PERSON> } from "../svgs";
import styles from "./bundle-board.module.scss";
import BundleBoardSkeleton from "../BundleBoardSkeleton";
import { ApiGet } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { v4 as uuidv4 } from "uuid";
import { t } from "i18next";

const BundleBoard = ({ allPlans, plans, loading, repopulate }: any) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleRenewNumber = (planToRenew: any) => {
    ApiGet(`/plans/${planToRenew.planId}`)
      .then((response) => {
        let planToAdd = response.data;

        planToAdd.iconUrl = planToAdd.countries[0].iconUrl;
        planToAdd.countryName = planToAdd.countries[0].countryName;
        planToAdd.basketId = uuidv4();
        planToAdd.planType = "PHONE";
        planToAdd.autoRenew = false;
        planToAdd.isRenew = true;
        planToAdd.subscriptionId = planToRenew.subscriptionId;
        if (planToAdd.hasMobileNumber) {
          planToAdd.didType = "MOBILE";
        } else if (planToAdd.hasLocalNumber) {
          planToAdd.didType = "LOCAL";
        } else {
          planToAdd.didType = "NA";
        }

        dispatch({
          type: "set",
          basket: [planToAdd],
        });

        navigate("/cart-summary");
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: t("buttons.oops"),
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <div className={styles.main}>
      <Helmet>
        <title>{t("general.orbit")} | { t('general.dashboard') } |  { t('account.pages.myesims') }</title>
      </Helmet>
      {loading ? (
        <BundleBoardSkeleton />
      ) : plans && plans.length ? (
        <>
          <div className={styles.title}>
            <div className={styles.titleText} onClick={handleRenewNumber}>
              {t("account.pages.myesims")}
            </div>
            <Link style={{ textDecoration: "none" }} to="/#plans-section">
              <Button style={{ whiteSpace: "nowrap" }}>
                <Trolley />
                {t("plan.getPlan")}
              </Button>
            </Link>
          </div>
          <div className={styles.planTilesContainer}>
            {plans.map((plan: any) => (
              <DashboardPlanTile
                key={"plan-" + plan.id}
                data={plan}
                subscription={plan}
                repopulate={repopulate}
              />
            ))}
          </div>
        </>
      ) : (
        <>
          <div className={styles.title}>
            <div className={styles.titleText}>{t("account.pages.myesims")}</div>
          </div>
          <div className={styles.noBundles}>
            <img
              src="/images-int/dashboard/no_plans.svg"
              className={styles.image}
            />
            <h5>{t("plan.noPlans")}</h5>
            <p>
              {t("plan.noActivePlans")}
              <br />
              {t("plan.noWorries")}
              <br />
              {t("plan.discoverPlan")}
            </p>
            <Link style={{ textDecoration: "none" }} to="/#plans-section">
              <Button style={{ whiteSpace: "nowrap" }}>
                <Trolley />
                {t("plan.addPlan")}
              </Button>
            </Link>
          </div>
        </>
      )}
    </div>
  );
};

export default BundleBoard;
