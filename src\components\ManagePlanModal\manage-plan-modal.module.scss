@use "../../styles/theme.scss" as *;

.main {
  padding: 24px;
  position: relative;
  h4 {
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
    color: $primary;
    margin: 0;
    margin-bottom: 16px;
  }
}

.closeButton {
  position: absolute;
  top: 24px;
  right: 24px;
  svg {
    vertical-align: middle;
  }
  padding: 0;
  background: none;
  border: none;
  cursor: pointer;
}

.grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 16px;
  align-content: start;
  @media (max-width: 690px) {
    grid-template-columns: 1fr;
  }
  &.held {
    display: flex;
    .colContainer {
      width: 100%;
      .country {
        margin-bottom: 16px;
      }
    }
  }
}

.colTitle {
  font-size: 12px;
  line-height: 18px;
  margin-bottom: 4px;
}

.colInfoBox {
  background: #eff1f7;
  padding: 16px;
  border-radius: 24px;
}

.colContainer {
  display: flex;
  flex-direction: column;
  @media (max-width: 690px) {
    margin-bottom: 16px;
  }
}

.flexApart {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: $dark-dark-purple;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
}

.priceLoad {
  position: relative;
  overflow: hidden;
  height: 24px;
  width: 45px;
  background: $skeleton;
  border-radius: 6px;
}

.country {
  color: $dark-dark-purple;
  font-size: 12px;
  line-height: 18px;
  margin-top: 2px;
  margin-bottom: 24px;
}

.initialGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-row-gap: 8px;
  grid-column-gap: 8px;
}

.initialData {
  display: flex;
  align-items: center;
  .value {
    color: $dark-dark-purple;
    font-size: 16px;
    line-height: 22px;
    font-weight: 600;
  }
  .label {
    color: $dark-dark-purple;
    font-size: 12px;
    line-height: 16px;
    font-weight: 400;
  }
  svg {
    color: $primary;
    vertical-align: middle;
    margin-right: 8px;
  }
}

.autoRenewBox {
  padding: 16px;
  background: #e4dbf0;
  border-radius: 24px;
  margin-top: 24px;
  &.held {
    margin-top: 9px;
    .row {
      @media (max-width: 650px) {
        flex-direction: column;
        align-items: flex-start;
      }
    }
  }
  .row {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6px;
    .title {
      color: $dark-dark-purple;
      font-weight: 600;
    }
  }

  .renewText {
    color: $dark-dark-purple;
    font-size: 12px;
  }

  .autorenew {
    display: flex;
    align-items: center;
    .autorenewText {
      font-size: 12px;
      margin-right: 8px;
      color: $dark-dark-purple;
    }
  }
}

.alert {
  background-color: #ffb9bc4d;
  border-radius: 24px;
  margin-top: 8px;
  padding: 16px;
  color: $dark-dark-purple;
  display: flex;
  flex-direction: column;
  svg {
    margin-bottom: 4px;
  }
  .heading {
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    margin-bottom: 4px;
  }
  p {
    margin: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
  }
}

.toolbar {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
}

.actionButtons {
  display: flex;
  align-items: center;
  .actionButton {
    background: none;
    border: none;
    cursor: pointer;
    margin-right: 10px;
    width: 40px;
    height: 40px;
    border-radius: 4px;
    color: #000;
    background-color: #eff1f7;
    &:last-child {
      margin-right: 0px;
    }
    &:hover {
      background-color: #ccd2e6;
    }
    svg {
      width: 20px;
      height: 20px;
      vertical-align: middle;
    }
  }
}
