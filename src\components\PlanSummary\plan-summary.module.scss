@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  padding: 24px;
  background: #eff1f7;
  border-radius: 24px;
  margin-bottom: 12px;
  display: grid;
  grid-template-rows: auto auto auto;
  grid-row-gap: 16px;
  color: $dark-dark-purple;
  font-size: 16px;
  line-height: 24px;
  .planType {
    font-size: 20px;
    line-height: 30px;
    font-weight: 700;
  }
  .price {
    font-weight: 600;
  }
  .divider {
    width: 100%;
    height: 1px;
    background-color: #c9cfe4;
  }
  .countryName {
    display: grid;
    align-items: center;
    grid-template-columns: 20px auto;
    font-weight: 600;
    grid-column-gap: 8px;
    .flag {
      width: 20px;
      height: 20px;
      border-radius: 1000px;
      background-color: #bdbdbd;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
    }
  }
  .row {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.binWrapper {
  svg {
    vertical-align: middle;
    margin-right: 0px;
  }
}
